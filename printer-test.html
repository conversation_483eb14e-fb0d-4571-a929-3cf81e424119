<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Thermal Printer Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      padding: 20px;
    }
    .container {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
      justify-content: center;
    }
    .printer-simulation {
      border: 2px solid #333;
      padding: 10px;
      margin-bottom: 20px;
    }
    .printer-58mm {
      width: 58mm;
      background-color: #f9f9f9;
    }
    .printer-80mm {
      width: 80mm;
      background-color: #f0f0f0;
    }
    .printer-custom {
      width: 100%;
      max-width: 300px;
      background-color: #e8e8e8;
    }
    .controls {
      margin-bottom: 20px;
      text-align: center;
    }
    button {
      padding: 8px 16px;
      margin: 5px;
      cursor: pointer;
    }
    h2 {
      text-align: center;
      margin-top: 0;
      font-size: 16px;
      border-bottom: 1px solid #333;
      padding-bottom: 5px;
    }
    #custom-width {
      width: 60px;
      padding: 5px;
    }
    .ticket-container {
      overflow: hidden;
    }
    #test-ticket {
      font-family: 'Arial', sans-serif;
      font-size: 9px;
      line-height: 1.2;
      width: 100%;
      box-sizing: border-box;
    }
    .header {
      text-align: center;
      margin-bottom: 5px;
      border-bottom: 1px dashed #000;
      padding-bottom: 3px;
    }
    .shop-name {
      font-size: 14px;
      font-weight: 900;
    }
    .section {
      margin-bottom: 5px;
      border-bottom: 1px dashed #000;
      padding-bottom: 3px;
    }
    .title {
      font-size: 12px;
      font-weight: 900;
      margin: 3px 0;
      border-bottom: 1px solid #000;
      padding: 2px 0;
    }
    .info-row {
      display: block;
      margin: 1px 0;
      width: 100%;
      box-sizing: border-box;
      overflow: hidden;
    }
    .label {
      font-weight: 900;
      text-decoration: underline;
      display: inline-block;
      width: 40%;
      padding-right: 2px;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: top;
    }
    .value {
      font-weight: 700;
      text-align: right;
      display: inline-block;
      width: 58%;
      overflow: hidden;
      text-overflow: ellipsis;
      vertical-align: top;
    }
    .problem-box {
      margin-top: 4px;
      border: 1px solid #000;
      padding: 2px;
    }
    .problem-title {
      font-weight: 900;
      text-align: center;
      text-decoration: underline;
      font-size: 10px;
      margin-bottom: 2px;
    }
    .problem-text {
      font-weight: 800;
      font-size: 9px;
      text-align: left;
    }
    .footer {
      text-align: center;
      margin-top: 5px;
      font-size: 9px;
      font-weight: 700;
      width: 100%;
      box-sizing: border-box;
    }
  </style>
</head>
<body>
  <h1>Thermal Printer Test Page</h1>
  <p>This page simulates how your ticket will look on different thermal printer widths.</p>
  
  <div class="controls">
    <button id="btn-58mm">Show 58mm Width</button>
    <button id="btn-80mm">Show 80mm Width</button>
    <button id="btn-custom">Custom Width: <input type="number" id="custom-width" value="100"> mm</button>
    <button id="btn-print">Print Preview</button>
  </div>
  
  <div class="container">
    <div id="printer-simulation" class="printer-simulation printer-80mm">
      <h2>Thermal Printer Simulation (80mm)</h2>
      <div class="ticket-container">
        <div id="test-ticket">
          <div class="header">
            <div class="shop-name">Repair QR Ninja</div>
            <div style="font-size: 8px; font-weight: 700;">Contact us: 24025024 - 24300025</div>
            <div>Repair Ticket #12345</div>
            <div>Jan 15, 2023 - 14:30</div>
          </div>

          <div class="section">
            <div class="title">Customer Information</div>
            <div class="info-row">
              <span class="label">Customer Name:</span>
              <span class="value">John Doe</span>
            </div>
            <div class="info-row">
              <span class="label">Customer Phone:</span>
              <span class="value">+216 55 123 456</span>
            </div>
          </div>

          <div class="section">
            <div class="title">Repair Details</div>
            <div class="info-row">
              <span class="label">Phone Model:</span>
              <span class="value">iPhone 13 Pro</span>
            </div>
            <div class="info-row">
              <span class="label">Status:</span>
              <span class="value">In Progress</span>
            </div>
            <div class="info-row">
              <span class="label">Created At:</span>
              <span class="value">Jan 15, 2023 - 14:30</span>
            </div>
            
            <div class="problem-box">
              <p class="problem-title">Problem Description:</p>
              <p class="problem-text">Screen is cracked and battery drains quickly. Customer mentioned water damage a few weeks ago. Needs urgent repair.</p>
            </div>

            <div style="margin-top: 3px; border-top: 1px dashed #000; padding-top: 2px;">
              <p style="font-weight: 900; text-decoration: underline; font-size: 9px;">Observations:</p>
              <div style="margin-top: 1px; padding-left: 2px;">
                <p style="font-weight: 700; font-size: 8px;">- Water damage confirmed, corrosion on logic board</p>
                <p style="font-size: 7px; text-align: right;">Jan 16, 2023 - 10:15</p>
              </div>
              <div style="margin-top: 3px; padding-left: 2px;">
                <p style="font-weight: 700; font-size: 8px;">- Replaced screen, battery needs replacement too</p>
                <p style="font-size: 7px; text-align: right;">Jan 16, 2023 - 15:45</p>
              </div>
            </div>
          </div>

          <div class="section">
            <div class="title">Payment Status</div>
            <div class="info-row">
              <span class="label">Initial Price:</span>
              <span class="value" style="font-weight: 800;">150.00 TND</span>
            </div>

            <div style="margin-top: 2px; border-top: 1px dashed #000; padding-top: 2px;">
              <p style="font-weight: 900; text-decoration: underline; font-size: 8px;">Price Modifications:</p>
              <div style="margin-top: 1px;">
                <div>
                  <span style="font-weight: 700; font-size: 8px; display: inline-block; width: 65%;">- Battery replacement:</span>
                  <span style="font-weight: 700; font-size: 8px; display: inline-block; width: 35%; text-align: right;">
                    +50.00 TND
                  </span>
                </div>
                <p style="font-size: 7px; text-align: right; margin: 0;">
                  Jan 16, 2023 - 15:45
                </p>
              </div>
              <div style="border-top: 1px dotted #000; padding-top: 2px; margin-top: 2px;">
                <span style="font-weight: 900; display: inline-block; width: 50%;">Total Price:</span>
                <span style="font-weight: 900; display: inline-block; width: 50%; text-align: right;">200.00 TND</span>
              </div>
            </div>

            <div style="margin-top: 3px; border-top: 1px dashed #000; padding-top: 2px;">
              <div style="margin-bottom: 1px;">
                <span style="font-weight: 700; display: inline-block; width: 50%;">Down Payment:</span>
                <span style="font-weight: 700; display: inline-block; width: 50%; text-align: right;">100.00 TND</span>
              </div>
              <div style="margin-bottom: 1px;">
                <span style="font-weight: 700; display: inline-block; width: 50%;">Balance:</span>
                <span style="font-weight: 800; display: inline-block; width: 50%; text-align: right;">100.00 TND</span>
              </div>
              <div>
                <span style="font-weight: 700; display: inline-block; width: 50%;">Payment Status:</span>
                <span style="font-weight: 800; display: inline-block; width: 50%; text-align: right;">Partial</span>
              </div>
            </div>
          </div>

          <div style="text-align: center; margin-top: 5px; width: 100%; box-sizing: border-box;">
            <div style="width: 50px; height: 50px; background-color: #000; margin: 0 auto;"></div>
            <p style="font-weight: 700; text-align: center;">Scan QR Code: abcd1234</p>
            <p style="font-weight: 800; text-align: center;">Ticket Number: #12345</p>
          </div>

          <div class="footer">
            <p style="font-weight: 700;">Thank you for your visit!</p>
            <p style="font-weight: 700;">Please bring this receipt when you pick up your device.</p>
          </div>
        </div>
      </div>
    </div>
  </div>

  <script>
    document.getElementById('btn-58mm').addEventListener('click', function() {
      const simulation = document.getElementById('printer-simulation');
      simulation.className = 'printer-simulation printer-58mm';
      simulation.querySelector('h2').textContent = 'Thermal Printer Simulation (58mm)';
      adjustFontSize(58);
    });

    document.getElementById('btn-80mm').addEventListener('click', function() {
      const simulation = document.getElementById('printer-simulation');
      simulation.className = 'printer-simulation printer-80mm';
      simulation.querySelector('h2').textContent = 'Thermal Printer Simulation (80mm)';
      adjustFontSize(80);
    });

    document.getElementById('btn-custom').addEventListener('click', function() {
      const width = document.getElementById('custom-width').value;
      const simulation = document.getElementById('printer-simulation');
      simulation.className = 'printer-simulation printer-custom';
      simulation.style.width = width + 'mm';
      simulation.querySelector('h2').textContent = `Thermal Printer Simulation (${width}mm)`;
      adjustFontSize(width);
    });

    document.getElementById('btn-print').addEventListener('click', function() {
      window.print();
    });

    function adjustFontSize(width) {
      const ticket = document.getElementById('test-ticket');
      
      // Adjust font size based on width
      if (width < 60) {
        ticket.style.fontSize = '7px';
      } else if (width < 70) {
        ticket.style.fontSize = '8px';
      } else if (width < 90) {
        ticket.style.fontSize = '9px';
      } else {
        ticket.style.fontSize = '10px';
      }
    }
  </script>
</body>
</html>
