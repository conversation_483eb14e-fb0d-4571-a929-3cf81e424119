#!/bin/bash

# This script fixes Content Security Policy issues for Supabase WebSocket connections

echo "=== Fixing Content Security Policy Issues ==="
echo "This script will update your application to allow WebSocket connections to Supabase"

# Stop the current Docker container
echo "Stopping current Docker container..."
docker stop repair-qr-ninja || true

# Remove the container
echo "Removing container..."
docker rm repair-qr-ninja || true

# Rebuild the Docker image
echo "Rebuilding Docker image..."
docker build -t repair-qr-ninja .

# Start the container with the updated configuration
echo "Starting container with updated configuration..."
docker run -d --name repair-qr-ninja -p 3001:3000 \
  -e NODE_ENV=production \
  -e VITE_SUPABASE_URL=${SUPABASE_URL:-https://qvwbetihlprjphvyaawi.supabase.co} \
  -e VITE_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY:-eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF2d2JldGlobHByanBodnlhYXdpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5NjQ0NzUsImV4cCI6MjA2MDU0MDQ3NX0.tjWg4fK22p50S2pIU898_BrsEL0rcc6p7HhK1Bzo4_U} \
  -v $(pwd)/public:/app/public \
  repair-qr-ninja

# Update Nginx configuration
echo "Updating Nginx configuration..."
echo "Please enter your sudo password when prompted to update the Nginx configuration."

# Assuming the Nginx config is in /etc/nginx/sites-available/
NGINX_CONFIG_PATH="/etc/nginx/sites-available/Multitech.talifouni.com"

# Check if the file exists
if [ -f "$NGINX_CONFIG_PATH" ]; then
    # Backup the existing config
    sudo cp "$NGINX_CONFIG_PATH" "${NGINX_CONFIG_PATH}.bak.$(date +%Y%m%d%H%M%S)"
    
    # Copy the new config
    sudo cp nginx-host-config.conf "$NGINX_CONFIG_PATH"
    
    # Test Nginx configuration
    echo "Testing Nginx configuration..."
    sudo nginx -t
    
    if [ $? -eq 0 ]; then
        # Reload Nginx if the test was successful
        echo "Reloading Nginx..."
        sudo systemctl reload nginx
        echo "Nginx configuration updated and reloaded successfully."
    else
        echo "Nginx configuration test failed. Please check the configuration manually."
        exit 1
    fi
else
    echo "Nginx configuration file not found at $NGINX_CONFIG_PATH"
    echo "Please manually update your Nginx configuration with the content from nginx-host-config.conf"
    echo "Then reload Nginx with: sudo systemctl reload nginx"
fi

echo ""
echo "=== Fix Completed ==="
echo "The application has been updated with the correct Content Security Policy settings."
echo "Please check your application at https://Multitech.talifouni.com to verify the issues are resolved."
echo ""
echo "If you still encounter issues, you may need to:"
echo "1. Clear your browser cache or use incognito mode"
echo "2. Check the Docker container logs: docker logs repair-qr-ninja"
echo "3. Check the Nginx error logs: sudo tail -f /var/log/nginx/error.log"
