<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSP Test</title>
    <script>
        // Function to test WebSocket connection
        function testWebSocket() {
            const url = 'wss://qvwbetihlprjphvyaawi.supabase.co/realtime/v1/websocket?apikey=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF2d2JldGlobHByanBodnlhYXdpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5NjQ0NzUsImV4cCI6MjA2MDU0MDQ3NX0.tjWg4fK22p50S2pIU898_BrsEL0rcc6p7HhK1Bzo4_U&vsn=1.0.0';
            
            try {
                const socket = new WebSocket(url);
                
                socket.onopen = function() {
                    document.getElementById('result').innerHTML = 'WebSocket connection successful!';
                    document.getElementById('result').style.color = 'green';
                };
                
                socket.onerror = function(error) {
                    document.getElementById('result').innerHTML = 'WebSocket connection failed: ' + error;
                    document.getElementById('result').style.color = 'red';
                    console.error('WebSocket error:', error);
                };
            } catch (e) {
                document.getElementById('result').innerHTML = 'Error creating WebSocket: ' + e.message;
                document.getElementById('result').style.color = 'red';
                console.error('Error:', e);
            }
        }
        
        // Function to check current CSP
        function checkCSP() {
            fetch('/csp-report', {
                method: 'HEAD'
            })
            .then(response => {
                const csp = response.headers.get('Content-Security-Policy');
                document.getElementById('csp').innerHTML = csp || 'No CSP header found';
            })
            .catch(error => {
                document.getElementById('csp').innerHTML = 'Error fetching CSP: ' + error;
            });
        }
    </script>
</head>
<body>
    <h1>Content Security Policy Test</h1>
    
    <h2>Current CSP:</h2>
    <pre id="csp">Loading...</pre>
    
    <h2>WebSocket Test:</h2>
    <button onclick="testWebSocket()">Test WebSocket Connection</button>
    <p id="result">Click the button to test the WebSocket connection</p>
    
    <script>
        // Check CSP on page load
        window.onload = checkCSP;
    </script>
</body>
</html>
