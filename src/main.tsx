import { createRoot } from "react-dom/client";
import App from "./App.tsx";
import "./index.css";
import "./styles/rtl-fixes.css"; // Import RTL fixes
import "./styles/rtl-form.css"; // Import RTL form fixes
import "./i18n"; // Import i18n configuration before anything else

const root = createRoot(document.getElementById("root")!);

// Initialize language direction based on current language
const setLanguageDirection = () => {
  const language = localStorage.getItem("i18nextLng") || "fr";
  document.documentElement.lang = language;
  document.documentElement.dir = language === "ar" ? "rtl" : "ltr";
};

// Set initial language direction
setLanguageDirection();

// Render the app
root.render(<App />);
