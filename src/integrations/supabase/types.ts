export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

export type Database = {
  public: {
    Tables: {
      product_categories: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          icon: string | null;
          color: string | null;
          repair_shop_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          icon?: string | null;
          color?: string | null;
          repair_shop_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          icon?: string | null;
          color?: string | null;
          repair_shop_id?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "product_categories_repair_shop_id_fkey";
            columns: ["repair_shop_id"];
            referencedRelation: "repair_shops";
            referencedColumns: ["id"];
          }
        ];
      };
      products: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          sku: string;
          barcode: string | null;
          category_id: string;
          price: number;
          cost: number;
          stock_quantity: number;
          min_stock_level: number;
          max_stock_level: number | null;
          is_active: boolean;
          image_url: string | null;
          repair_shop_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          sku: string;
          barcode?: string | null;
          category_id: string;
          price: number;
          cost: number;
          stock_quantity?: number;
          min_stock_level?: number;
          max_stock_level?: number | null;
          is_active?: boolean;
          image_url?: string | null;
          repair_shop_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          description?: string | null;
          sku?: string;
          barcode?: string | null;
          category_id?: string;
          price?: number;
          cost?: number;
          stock_quantity?: number;
          min_stock_level?: number;
          max_stock_level?: number | null;
          is_active?: boolean;
          image_url?: string | null;
          repair_shop_id?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "products_category_id_fkey";
            columns: ["category_id"];
            referencedRelation: "product_categories";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "products_repair_shop_id_fkey";
            columns: ["repair_shop_id"];
            referencedRelation: "repair_shops";
            referencedColumns: ["id"];
          }
        ];
      };
      repair_shops: {
        Row: {
          id: string;
          name: string;
          address: string | null;
          phone: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          address?: string | null;
          phone?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          name?: string;
          address?: string | null;
          phone?: string | null;
          created_at?: string;
        };
        Relationships: [];
      };
      user_repair_shops: {
        Row: {
          id: string;
          user_id: string;
          repair_shop_id: string;
          role: string;
          full_name?: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          repair_shop_id: string;
          role: string;
          full_name?: string | null;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          repair_shop_id?: string;
          role?: string;
          full_name?: string | null;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "user_repair_shops_repair_shop_id_fkey";
            columns: ["repair_shop_id"];
            referencedRelation: "repair_shops";
            referencedColumns: ["id"];
          }
        ];
      };
      repairs: {
        Row: {
          completed_at: string | null;
          created_at: string;
          customer_name: string;
          customer_phone: string;
          down_payment: number;
          id: string;
          payment_status: string;
          phone_model: string;
          problem_description: string;
          repair_price: number;
          status: string;
          user_id: string;
          repair_shop_id: string;
          assigned_technician?: string | null;
        };
        Insert: {
          completed_at?: string | null;
          created_at?: string;
          customer_name: string;
          customer_phone: string;
          down_payment: number;
          id?: string;
          payment_status: string;
          phone_model: string;
          problem_description: string;
          repair_price: number;
          status: string;
          user_id: string;
          repair_shop_id: string;
          assigned_technician?: string | null;
        };
        Update: {
          completed_at?: string | null;
          created_at?: string;
          customer_name?: string;
          customer_phone?: string;
          down_payment?: number;
          id?: string;
          payment_status?: string;
          phone_model?: string;
          problem_description?: string;
          repair_price?: number;
          status?: string;
          user_id?: string;
          repair_shop_id?: string;
          assigned_technician?: string | null;
        };
        Relationships: [
          {
            foreignKeyName: "repairs_repair_shop_id_fkey";
            columns: ["repair_shop_id"];
            referencedRelation: "repair_shops";
            referencedColumns: ["id"];
          }
        ];
      };
      sale_items: {
        Row: {
          id: string;
          sale_id: string;
          product_id: string;
          quantity: number;
          unit_price: number;
          discount: number;
          total_price: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          sale_id: string;
          product_id: string;
          quantity: number;
          unit_price: number;
          discount?: number;
          total_price: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          sale_id?: string;
          product_id?: string;
          quantity?: number;
          unit_price?: number;
          discount?: number;
          total_price?: number;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "sale_items_sale_id_fkey";
            columns: ["sale_id"];
            referencedRelation: "sales";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "sale_items_product_id_fkey";
            columns: ["product_id"];
            referencedRelation: "products";
            referencedColumns: ["id"];
          }
        ];
      };
      sales: {
        Row: {
          id: string;
          sale_number: string;
          customer_id: string | null;
          customer_name: string | null;
          customer_phone: string | null;
          subtotal: number;
          tax_amount: number;
          discount_amount: number;
          total_amount: number;
          payment_method: string;
          payment_status: string;
          amount_paid: number;
          change_amount: number;
          notes: string | null;
          user_id: string;
          repair_shop_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          sale_number?: string;
          customer_id?: string | null;
          customer_name?: string | null;
          customer_phone?: string | null;
          subtotal: number;
          tax_amount?: number;
          discount_amount?: number;
          total_amount: number;
          payment_method: string;
          payment_status: string;
          amount_paid?: number;
          change_amount?: number;
          notes?: string | null;
          user_id: string;
          repair_shop_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          sale_number?: string;
          customer_id?: string | null;
          customer_name?: string | null;
          customer_phone?: string | null;
          subtotal?: number;
          tax_amount?: number;
          discount_amount?: number;
          total_amount?: number;
          payment_method?: string;
          payment_status?: string;
          amount_paid?: number;
          change_amount?: number;
          notes?: string | null;
          user_id?: string;
          repair_shop_id?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "sales_repair_shop_id_fkey";
            columns: ["repair_shop_id"];
            referencedRelation: "repair_shops";
            referencedColumns: ["id"];
          }
        ];
      };
      stock_movements: {
        Row: {
          id: string;
          product_id: string;
          type: string;
          quantity: number;
          reason: string;
          reference: string | null;
          user_id: string;
          repair_shop_id: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          product_id: string;
          type: string;
          quantity: number;
          reason: string;
          reference?: string | null;
          user_id: string;
          repair_shop_id: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          product_id?: string;
          type?: string;
          quantity?: number;
          reason?: string;
          reference?: string | null;
          user_id?: string;
          repair_shop_id?: string;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "stock_movements_product_id_fkey";
            columns: ["product_id"];
            referencedRelation: "products";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "stock_movements_repair_shop_id_fkey";
            columns: ["repair_shop_id"];
            referencedRelation: "repair_shops";
            referencedColumns: ["id"];
          }
        ];
      };
      invoices: {
        Row: {
          id: string;
          invoice_number: string;
          repair_shop_id: string;
          customer_name: string;
          customer_phone: string | null;
          subtotal: number;
          discount_amount: number;
          total_amount: number;
          status: string;
          notes: string | null;
          user_id: string;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          invoice_number: string;
          repair_shop_id: string;
          customer_name: string;
          customer_phone?: string | null;
          subtotal: number;
          discount_amount?: number;
          total_amount: number;
          status?: string;
          notes?: string | null;
          user_id: string;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          invoice_number?: string;
          repair_shop_id?: string;
          customer_name?: string;
          customer_phone?: string | null;
          subtotal?: number;
          discount_amount?: number;
          total_amount?: number;
          status?: string;
          notes?: string | null;
          user_id?: string;
          created_at?: string;
          updated_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "invoices_repair_shop_id_fkey";
            columns: ["repair_shop_id"];
            referencedRelation: "repair_shops";
            referencedColumns: ["id"];
          }
        ];
      };
      invoice_items: {
        Row: {
          id: string;
          invoice_id: string;
          repair_id: string;
          description: string;
          amount: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          invoice_id: string;
          repair_id: string;
          description: string;
          amount: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          invoice_id?: string;
          repair_id?: string;
          description?: string;
          amount?: number;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "invoice_items_invoice_id_fkey";
            columns: ["invoice_id"];
            referencedRelation: "invoices";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "invoice_items_repair_id_fkey";
            columns: ["repair_id"];
            referencedRelation: "repairs";
            referencedColumns: ["id"];
          }
        ];
      };
      form_responses: {
        Row: {
          id: string;
          repair_id: string;
          template_id: string;
          responses: Json;
          created_at: string;
        };
        Insert: {
          id?: string;
          repair_id: string;
          template_id: string;
          responses: Json;
          created_at?: string;
        };
        Update: {
          id?: string;
          repair_id?: string;
          template_id?: string;
          responses?: Json;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "form_responses_repair_id_fkey";
            columns: ["repair_id"];
            referencedRelation: "repairs";
            referencedColumns: ["id"];
          }
        ];
      };
      repair_stock_items: {
        Row: {
          id: string;
          repair_id: string;
          product_id: string;
          quantity: number;
          unit_price: number;
          total_price: number;
          repair_shop_id: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          repair_id: string;
          product_id: string;
          quantity: number;
          unit_price: number;
          total_price?: number;
          repair_shop_id: string;
          created_at?: string;
        };
        Update: {
          id?: string;
          repair_id?: string;
          product_id?: string;
          quantity?: number;
          unit_price?: number;
          total_price?: number;
          repair_shop_id?: string;
          created_at?: string;
        };
        Relationships: [
          {
            foreignKeyName: "repair_stock_items_repair_id_fkey";
            columns: ["repair_id"];
            referencedRelation: "repairs";
            referencedColumns: ["id"];
          },
          {
            foreignKeyName: "repair_stock_items_product_id_fkey";
            columns: ["product_id"];
            referencedRelation: "products";
            referencedColumns: ["id"];
          }
        ];
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      generate_invoice_number: {
        Args: Record<PropertyKey, never>;
        Returns: string;
      };
      get_shop_users: {
        Args: {
          shop_id: string;
        };
        Returns: {
          user_id: string;
          role: string;
          email: string;
        }[];
      };
    };
    Enums: {
      [_ in never]: never;
    };
    CompositeTypes: {
      [_ in never]: never;
    };
  };
};

type DefaultSchema = Database[Extract<keyof Database, "public">];

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? (Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      Database[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R;
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
      DefaultSchema["Views"])
  ? (DefaultSchema["Tables"] &
      DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
      Row: infer R;
    }
    ? R
    : never
  : never;

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I;
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Insert: infer I;
    }
    ? I
    : never
  : never;

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof Database },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never
> = DefaultSchemaTableNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U;
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
  ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
      Update: infer U;
    }
    ? U
    : never
  : never;

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof Database },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never
> = DefaultSchemaEnumNameOrOptions extends { schema: keyof Database }
  ? Database[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
  ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
  : never;

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof Database },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof Database;
  }
    ? keyof Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never
> = PublicCompositeTypeNameOrOptions extends { schema: keyof Database }
  ? Database[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
  ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
  : never;

export const Constants = {
  public: {
    Enums: {},
  },
} as const;
