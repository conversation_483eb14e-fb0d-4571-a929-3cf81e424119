// This file is automatically generated. Do not edit it directly.
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";

const SUPABASE_URL = "https://thcapwaszlsoqhskkxsc.supabase.co";
const SUPABASE_PUBLISHABLE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRoY2Fwd2Fzemxzb3Foc2treHNjIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY4OTE4MTEsImV4cCI6MjA3MjQ2NzgxMX0.A4JbMHPGcbV6ENha8rMMTTYtHlS81iPP4C9RArfqCWo";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(
  SUPABASE_URL,
  SUPABASE_PUBLISHABLE_KEY,
  {
    auth: {
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
      flowType: "pkce",
    },
    global: {
      headers: {
        "x-client-info": "repair-qr-ninja",
      },
    },
  }
);
