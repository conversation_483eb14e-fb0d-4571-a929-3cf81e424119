// This file is automatically generated. Do not edit it directly.
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";

const SUPABASE_URL = "https://qvwbetihlprjphvyaawi.supabase.co";
const SUPABASE_PUBLISHABLE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InF2d2JldGlobHByanBodnlhYXdpIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDQ5NjQ0NzUsImV4cCI6MjA2MDU0MDQ3NX0.tjWg4fK22p50S2pIU898_BrsEL0rcc6p7HhK1Bzo4_U";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(
  SUPABASE_URL,
  SUPABASE_PUBLISHABLE_KEY
);
