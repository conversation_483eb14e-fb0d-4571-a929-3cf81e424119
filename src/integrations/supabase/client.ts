// This file is automatically generated. Do not edit it directly.
import { createClient } from "@supabase/supabase-js";
import type { Database } from "./types";

const SUPABASE_URL = "https://nvaqzlvwllygyaragzhn.supabase.co";
const SUPABASE_PUBLISHABLE_KEY =
  "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im52YXF6bHZ3bGx5Z3lhcmFnemhuIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTY5MTY0ODYsImV4cCI6MjA3MjQ5MjQ4Nn0.iU5-iZYozps2Kcx0VaPNyGuvsj6p16C1fjG1Vczl8wk";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(
  SUPABASE_URL,
  SUPABASE_PUBLISHABLE_KEY
);
