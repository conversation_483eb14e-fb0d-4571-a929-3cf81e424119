import { RepairItem } from "@/types";

/**
 * Base number for ticket numbering
 */
const BASE_NUMBER = 10000;

/**
 * Generates a ticket number for a repair based on its creation date and position in the sorted list of repairs
 *
 * NOTE: This function is kept for backward compatibility with older repairs that don't have a stored ticket number.
 * New repairs should use the ticket_number field stored in the database.
 *
 * @param repair The repair item to generate a ticket number for
 * @param allRepairs All repair items, used to determine the position of the current repair
 * @returns A ticket number string (e.g., "10001")
 */
export const generateTicketNumber = (
  repair: RepairItem,
  allRepairs: RepairItem[]
): string => {
  // Sort all repairs by creation date (oldest first)
  const sortedRepairs = [...allRepairs].sort(
    (a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime()
  );

  // Find the index of the current repair in the sorted list
  const index = sortedRepairs.findIndex((r) => r.id === repair.id);

  // Generate the ticket number (base + index + 1)
  // We add 1 because array indices are 0-based, but we want ticket numbers to start at 1
  const ticketNumber = BASE_NUMBER + index + 1;

  return ticketNumber.toString();
};

/**
 * Formats a ticket number with a prefix if needed
 *
 * @param ticketNumber The ticket number to format
 * @param prefix Optional prefix to add before the ticket number
 * @returns Formatted ticket number string
 */
export const formatTicketNumber = (
  ticketNumber: string,
  prefix: string = ""
): string => {
  return `${prefix}${ticketNumber}`;
};
