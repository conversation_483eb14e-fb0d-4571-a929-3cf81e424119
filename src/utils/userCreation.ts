/**
 * User Creation Utility
 * 
 * This utility handles the complete process of creating new users with authentication
 * and associating them with repair shops.
 */

import { supabase } from '@/lib/client';

export interface CreateUserData {
  email: string;
  password: string;
  fullName: string;
  role: 'administrator' | 'technician' | 'receptionist' | 'cashier';
  shopId: string;
}

export interface CreateUserResult {
  success: boolean;
  message: string;
  userId?: string;
  error?: string;
  action?: string;
}

/**
 * Creates a new user with authentication and associates them with a repair shop
 */
export async function createUserWithRole(userData: CreateUserData): Promise<CreateUserResult> {
  try {
    // Step 1: Call the database function to check if user exists and validate permissions
    const { data: checkResult, error: checkError } = await supabase
      .rpc('create_user_with_role', {
        shop_id: userData.shopId,
        user_email: userData.email,
        user_full_name: userData.fullName,
        user_password: userData.password,
        user_role: userData.role
      });

    if (checkError) {
      return {
        success: false,
        error: checkError.message
      };
    }

    // If user already exists and was added to shop
    if (checkResult.action === 'added_existing_user') {
      return {
        success: true,
        message: checkResult.message,
        userId: checkResult.user_id
      };
    }

    // If we need to create a new user
    if (checkResult.action === 'create_new_user') {
      // Step 2: Create the user using Supabase Admin API
      const { data: authData, error: authError } = await supabase.auth.admin.createUser({
        email: userData.email,
        password: userData.password,
        email_confirm: true, // Auto-confirm email for admin-created users
        user_metadata: {
          full_name: userData.fullName,
          role: userData.role,
          shop_id: userData.shopId
        }
      });

      if (authError) {
        return {
          success: false,
          error: `Failed to create user: ${authError.message}`
        };
      }

      if (!authData.user) {
        return {
          success: false,
          error: 'User creation failed - no user data returned'
        };
      }

      // Step 3: Complete the user creation by associating with repair shop
      const { data: completeResult, error: completeError } = await supabase
        .rpc('complete_user_creation', {
          new_user_id: authData.user.id,
          shop_id: userData.shopId,
          user_full_name: userData.fullName,
          user_role: userData.role
        });

      if (completeError) {
        // If association fails, we should clean up the auth user
        // Note: In production, you might want to handle this differently
        console.error('Failed to associate user with shop:', completeError);
        return {
          success: false,
          error: `User created but failed to associate with shop: ${completeError.message}`
        };
      }

      if (!completeResult.success) {
        return {
          success: false,
          error: completeResult.error || 'Failed to complete user creation'
        };
      }

      return {
        success: true,
        message: `User ${userData.email} created successfully and added to repair shop`,
        userId: authData.user.id
      };
    }

    // Handle other cases
    return {
      success: false,
      error: checkResult.error || 'Unknown error occurred'
    };

  } catch (error) {
    console.error('Error in createUserWithRole:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
}

/**
 * Invites an existing user to join a repair shop
 */
export async function inviteUserToShop(
  shopId: string,
  email: string,
  fullName: string,
  role: 'administrator' | 'technician' | 'receptionist' | 'cashier'
): Promise<CreateUserResult> {
  try {
    const { data, error } = await supabase
      .rpc('invite_user_to_shop', {
        shop_id: shopId,
        user_email: email,
        user_full_name: fullName,
        user_role: role
      });

    if (error) {
      return {
        success: false,
        error: error.message
      };
    }

    return {
      success: data.success,
      message: data.message,
      userId: data.user_id,
      error: data.error
    };

  } catch (error) {
    console.error('Error in inviteUserToShop:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'An unexpected error occurred'
    };
  }
}

/**
 * Gets the current user's default shop ID
 */
export async function getCurrentUserShopId(): Promise<string | null> {
  try {
    const { data, error } = await supabase
      .rpc('get_current_user_shop_id');

    if (error) {
      console.error('Error getting current user shop ID:', error);
      return null;
    }

    return data;
  } catch (error) {
    console.error('Error in getCurrentUserShopId:', error);
    return null;
  }
}

/**
 * Validates user creation data
 */
export function validateUserData(userData: Partial<CreateUserData>): string[] {
  const errors: string[] = [];

  if (!userData.email) {
    errors.push('Email is required');
  } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(userData.email)) {
    errors.push('Invalid email format');
  }

  if (!userData.password) {
    errors.push('Password is required');
  } else if (userData.password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }

  if (!userData.fullName) {
    errors.push('Full name is required');
  }

  if (!userData.role) {
    errors.push('Role is required');
  } else if (!['administrator', 'technician', 'receptionist', 'cashier'].includes(userData.role)) {
    errors.push('Invalid role specified');
  }

  if (!userData.shopId) {
    errors.push('Shop ID is required');
  }

  return errors;
}
