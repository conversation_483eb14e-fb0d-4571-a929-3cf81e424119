
import { RepairItem } from "@/types";
import { toast } from "sonner";
import <PERSON> from "papaparse";
import i18next from "i18next";

export const exportToCSV = (repairs: RepairItem[]) => {
  const t = i18next.t;

  try {
    // Convert dates to ISO strings for CSV export
    const dataToExport = repairs.map(repair => ({
      ...repair,
      createdAt: repair.createdAt.toISOString(),
      completedAt: repair.completedAt ? repair.completedAt.toISOString() : "",
    }));
    
    const csv = Papa.unparse(dataToExport);
    
    // Create a download link element and trigger a download
    const element = document.createElement("a");
    const file = new Blob([csv], { type: "text/csv;charset=utf-8" });
    element.href = URL.createObjectURL(file);
    element.download = `repair-data-${new Date().toISOString().slice(0, 10)}.csv`;
    document.body.appendChild(element);
    element.click();
    document.body.removeChild(element);
    
    toast.success(t("data.successExport"));
  } catch (error) {
    console.error("Export error:", error);
    toast.error(t("data.failedExport"));
  }
};

export const importFromCSV = (file: File): Promise<RepairItem[]> => {
  return new Promise((resolve, reject) => {
    Papa.parse(file, {
      header: true,
      complete: (results) => {
        try {
          const repairs = results.data.map((item: any) => ({
            ...(item as RepairItem),
            id: item.id || "",
            repairPrice: parseFloat(item.repairPrice),
            downPayment: parseFloat(item.downPayment),
            createdAt: new Date(item.createdAt),
            completedAt: item.completedAt ? new Date(item.completedAt) : undefined,
          }));
          resolve(repairs as RepairItem[]);
        } catch (error) {
          reject(error);
        }
      },
      error: (error) => {
        reject(error);
      },
    });
  });
};
