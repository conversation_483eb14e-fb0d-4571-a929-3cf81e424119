import JsBarcode from "jsbarcode";

/**
 * Generates a barcode data URL for the given text
 * @param text The text to encode in the barcode
 * @param options Optional configuration options for the barcode
 * @returns A Promise that resolves to a data URL containing the barcode image
 */
export const generateBarcodeDataURL = (
  text: string,
  options: {
    format?: string;
    width?: number;
    height?: number;
    displayValue?: boolean;
    fontSize?: number;
    textMargin?: number;
    background?: string;
    lineColor?: string;
    margin?: number;
  } = {}
): Promise<string> => {
  return new Promise((resolve, reject) => {
    try {
      // Create a canvas element
      const canvas = document.createElement("canvas");

      // Set default options
      const defaultOptions = {
        format: "CODE128", // Most common 1D barcode format that supports all characters
        width: 2, // Width of the bars
        height: 50, // Height of the barcode
        displayValue: true, // Show the text below the barcode
        fontSize: 12, // Font size of the text
        textMargin: 2, // Margin between the barcode and the text
        background: "#ffffff", // Background color
        lineColor: "#000000", // Bar color
        margin: 5, // Margin around the barcode
        ...options, // Override with any provided options
      };

      // Generate the barcode on the canvas
      JsBarcode(canvas, text, defaultOptions);

      // Convert the canvas to a data URL
      const dataURL = canvas.toDataURL("image/png");
      resolve(dataURL);
    } catch (error) {
      console.error("Failed to generate barcode:", error);
      reject(error);
    }
  });
};

/**
 * Formats a ticket number for CODE128 barcode
 * @param ticketNumber The ticket number to convert
 * @returns A formatted string for barcode generation
 */
export const formatTicketForBarcode = (
  ticketNumber: number | string
): string => {
  // Convert to string if it's a number
  let numStr = ticketNumber.toString();

  // Remove any non-numeric characters
  numStr = numStr.replace(/\D/g, "");

  // Return the cleaned ticket number
  return numStr;
};

/**
 * Generates a barcode specifically for repair tickets
 * @param repairId The repair ID or ticket number to encode
 * @returns A Promise that resolves to a data URL containing the barcode image
 */
export const generateRepairBarcode = (
  ticketNumber: string,
  isForTicket: boolean = false
): Promise<string> => {
  // Format the ticket number for CODE128 barcode
  const formattedTicket = formatTicketForBarcode(ticketNumber);

  console.log("Generating CODE128 barcode for ticket number:", ticketNumber);
  console.log("Formatted ticket number:", formattedTicket);

  // For repair tickets, we want a taller barcode that's easier to scan
  if (isForTicket) {
    return generateBarcodeDataURL(formattedTicket, {
      format: "CODE128", // Use CODE128 which accepts any text
      width: 2, // Thicker for better readability
      height: 70, // Taller for better scanning
      displayValue: true, // Show the number below the barcode
      fontSize: 10, // Slightly larger font for better readability
      margin: 5, // Small margin for better scanning
      textMargin: 2, // Small text margin
    });
  }

  // Default repair barcode
  return generateBarcodeDataURL(formattedTicket, {
    format: "CODE128",
    width: 2,
    height: 50,
    displayValue: true,
    fontSize: 8,
    margin: 3,
  });
};

/**
 * Generates a random EAN13 barcode number
 * @param prefix Optional prefix for the barcode (default: "200" for internal use)
 * @returns A valid EAN13 barcode string with check digit
 */
export const generateEAN13 = (prefix: string = "200"): string => {
  // Ensure prefix is valid (max 12 digits for EAN13)
  if (prefix.length > 12) {
    prefix = prefix.substring(0, 12);
  }

  // Generate random digits to fill up to 12 digits
  let barcode = prefix;
  while (barcode.length < 12) {
    barcode += Math.floor(Math.random() * 10).toString();
  }

  // Calculate EAN13 check digit
  const checkDigit = calculateEAN13CheckDigit(barcode);

  return barcode + checkDigit;
};

/**
 * Calculates the check digit for an EAN13 barcode
 * @param barcode The first 12 digits of the EAN13 barcode
 * @returns The check digit (0-9)
 */
export const calculateEAN13CheckDigit = (barcode: string): string => {
  if (barcode.length !== 12) {
    throw new Error(
      "EAN13 barcode must be exactly 12 digits for check digit calculation"
    );
  }

  let sum = 0;
  for (let i = 0; i < 12; i++) {
    const digit = parseInt(barcode[i]);
    // Multiply odd positions by 1, even positions by 3 (1-indexed)
    sum += digit * (i % 2 === 0 ? 1 : 3);
  }

  const checkDigit = (10 - (sum % 10)) % 10;
  return checkDigit.toString();
};

/**
 * Validates if a string is a valid EAN13 barcode
 * @param barcode The barcode string to validate
 * @returns True if valid EAN13, false otherwise
 */
export const isValidEAN13 = (barcode: string): boolean => {
  // Check if it's exactly 13 digits
  if (!/^\d{13}$/.test(barcode)) {
    return false;
  }

  // Validate check digit
  const first12 = barcode.substring(0, 12);
  const providedCheckDigit = barcode[12];
  const calculatedCheckDigit = calculateEAN13CheckDigit(first12);

  return providedCheckDigit === calculatedCheckDigit;
};

/**
 * Generates an EAN13 barcode specifically for products
 * @param productId Optional product ID to include in the barcode
 * @param companyPrefix Optional company prefix (default: "200" for internal use)
 * @returns A Promise that resolves to a data URL containing the EAN13 barcode image
 */
export const generateProductEAN13Barcode = (
  productId?: string,
  companyPrefix: string = "200"
): Promise<{ barcode: string; dataURL: string }> => {
  return new Promise((resolve, reject) => {
    try {
      // Generate EAN13 barcode number
      let prefix = companyPrefix;

      // If productId is provided, try to incorporate it
      if (productId) {
        // Extract numeric part from productId if possible
        const numericPart = productId.replace(/\D/g, "");
        if (numericPart.length > 0) {
          // Use last few digits of product ID
          const maxDigits = 12 - prefix.length;
          const productDigits = numericPart.slice(-maxDigits);
          prefix =
            prefix +
            productDigits.padStart(
              Math.min(maxDigits, productDigits.length),
              "0"
            );
        }
      }

      const ean13Code = generateEAN13(prefix);

      // Generate barcode image optimized for product stickers
      generateBarcodeDataURL(ean13Code, {
        format: "EAN13",
        width: 2,
        height: 60,
        displayValue: true,
        fontSize: 12,
        textMargin: 5,
        margin: 10,
        background: "#ffffff",
        lineColor: "#000000",
      })
        .then((dataURL) => {
          resolve({ barcode: ean13Code, dataURL });
        })
        .catch(reject);
    } catch (error) {
      console.error("Failed to generate EAN13 barcode:", error);
      reject(error);
    }
  });
};
