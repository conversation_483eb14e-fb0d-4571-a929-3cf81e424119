import JsBarcode from "jsbarcode";

/**
 * Generates a barcode data URL for the given text
 * @param text The text to encode in the barcode
 * @param options Optional configuration options for the barcode
 * @returns A Promise that resolves to a data URL containing the barcode image
 */
export const generateBarcodeDataURL = (
  text: string,
  options: {
    format?: string;
    width?: number;
    height?: number;
    displayValue?: boolean;
    fontSize?: number;
    textMargin?: number;
    background?: string;
    lineColor?: string;
    margin?: number;
  } = {}
): Promise<string> => {
  return new Promise((resolve, reject) => {
    try {
      // Create a canvas element
      const canvas = document.createElement("canvas");

      // Set default options
      const defaultOptions = {
        format: "CODE128", // Most common 1D barcode format that supports all characters
        width: 2, // Width of the bars
        height: 50, // Height of the barcode
        displayValue: true, // Show the text below the barcode
        fontSize: 12, // Font size of the text
        textMargin: 2, // Margin between the barcode and the text
        background: "#ffffff", // Background color
        lineColor: "#000000", // Bar color
        margin: 5, // Margin around the barcode
        ...options, // Override with any provided options
      };

      // Generate the barcode on the canvas
      JsBarcode(canvas, text, defaultOptions);

      // Convert the canvas to a data URL
      const dataURL = canvas.toDataURL("image/png");
      resolve(dataURL);
    } catch (error) {
      console.error("Failed to generate barcode:", error);
      reject(error);
    }
  });
};

/**
 * Formats a ticket number for CODE128 barcode
 * @param ticketNumber The ticket number to convert
 * @returns A formatted string for barcode generation
 */
export const formatTicketForBarcode = (
  ticketNumber: number | string
): string => {
  // Convert to string if it's a number
  let numStr = ticketNumber.toString();

  // Remove any non-numeric characters
  numStr = numStr.replace(/\D/g, "");

  // Return the cleaned ticket number
  return numStr;
};

/**
 * Generates a barcode specifically for repair tickets
 * @param repairId The repair ID or ticket number to encode
 * @returns A Promise that resolves to a data URL containing the barcode image
 */
export const generateRepairBarcode = (
  ticketNumber: string,
  isForTicket: boolean = false
): Promise<string> => {
  // Format the ticket number for CODE128 barcode
  const formattedTicket = formatTicketForBarcode(ticketNumber);

  console.log("Generating CODE128 barcode for ticket number:", ticketNumber);
  console.log("Formatted ticket number:", formattedTicket);

  // For repair tickets, we want a taller barcode that's easier to scan
  if (isForTicket) {
    return generateBarcodeDataURL(formattedTicket, {
      format: "CODE128", // Use CODE128 which accepts any text
      width: 2, // Thicker for better readability
      height: 70, // Taller for better scanning
      displayValue: true, // Show the number below the barcode
      fontSize: 10, // Slightly larger font for better readability
      margin: 5, // Small margin for better scanning
      textMargin: 2, // Small text margin
    });
  }

  // For standalone printing, we want a more readable barcode
  return generateBarcodeDataURL(formattedTicket, {
    format: "CODE128", // Use CODE128 which accepts any text
    width: 2.5, // Thicker bars for better scanning
    height: 120, // Taller height for better visibility and scanning
    displayValue: true, // Show text below barcode
    fontSize: 16, // Larger font for better readability
    margin: 10, // Add margin for better printing
    textMargin: 5, // Larger text margin
  });
};

/**
 * Generates an EAN13 barcode for products
 * @param productId The product ID to base the EAN13 on
 * @returns Promise<{barcode: string, dataURL: string}> The EAN13 code and its data URL
 */
export const generateProductEAN13Barcode = async (
  productId: string
): Promise<{ barcode: string; dataURL: string }> => {
  // Generate a 12-digit number from the product ID
  let numericString = "";

  // Convert product ID to a numeric string
  for (let i = 0; i < productId.length; i++) {
    numericString += productId.charCodeAt(i).toString();
  }

  // Ensure we have at least 12 digits, pad with zeros if necessary
  while (numericString.length < 12) {
    numericString = "0" + numericString;
  }

  // Take the first 12 digits
  const first12Digits = numericString.substring(0, 12);

  // Calculate EAN13 check digit
  let sum = 0;
  for (let i = 0; i < 12; i++) {
    const digit = parseInt(first12Digits[i]);
    sum += i % 2 === 0 ? digit : digit * 3;
  }
  const checkDigit = (10 - (sum % 10)) % 10;

  // Complete EAN13 code
  const ean13Code = first12Digits + checkDigit.toString();

  try {
    const dataURL = await generateBarcodeDataURL(ean13Code, {
      format: "EAN13",
      width: 2,
      height: 100,
      displayValue: true,
      fontSize: 12,
      textMargin: 5,
      margin: 10,
      background: "#ffffff",
      lineColor: "#000000",
    });

    return {
      barcode: ean13Code,
      dataURL: dataURL,
    };
  } catch (error) {
    console.error("Error generating EAN13 barcode:", error);
    throw error;
  }
};
