import { RepairItem, RepairShop } from "@/types";

export interface EmailTemplate {
  subject: string;
  htmlBody: string;
  textBody: string;
}

export interface EmailConfig {
  smtpHost?: string;
  smtpPort?: number;
  smtpUser?: string;
  smtpPassword?: string;
  fromEmail?: string;
  fromName?: string;
}

/**
 * Generate French email template for repair completion notification
 */
export const generateRepairCompletionEmailTemplate = (
  repair: RepairItem,
  repairShop?: RepairShop
): EmailTemplate => {
  const shopName = repairShop?.name || "Votre atelier de réparation";
  const shopPhone = repairShop?.phone || "";
  const shopAddress = repairShop?.address || "";

  const subject = `Réparation terminée - ${repair.phoneModel} (Ticket #${repair.ticketNumber || repair.id.slice(-6)})`;

  const htmlBody = `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Réparation terminée</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #4CAF50; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background-color: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
            .repair-details { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
            .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
            .button { display: inline-block; background-color: #4CAF50; color: white; padding: 10px 20px; text-decoration: none; border-radius: 5px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>✅ Réparation Terminée</h1>
            </div>
            <div class="content">
                <p>Bonjour <strong>${repair.customerName}</strong>,</p>
                
                <p>Nous avons le plaisir de vous informer que la réparation de votre appareil est maintenant <strong>terminée</strong> !</p>
                
                <div class="repair-details">
                    <h3>Détails de la réparation :</h3>
                    <ul>
                        <li><strong>Appareil :</strong> ${repair.phoneModel}</li>
                        <li><strong>Problème :</strong> ${repair.problemDescription}</li>
                        <li><strong>Numéro de ticket :</strong> #${repair.ticketNumber || repair.id.slice(-6)}</li>
                        <li><strong>Prix de la réparation :</strong> ${repair.repairPrice.toFixed(3)} TND</li>
                        <li><strong>Statut du paiement :</strong> ${getPaymentStatusText(repair.paymentStatus)}</li>
                    </ul>
                </div>
                
                <p><strong>Votre appareil est prêt à être récupéré !</strong></p>
                
                <p>Vous pouvez venir le récupérer pendant nos heures d'ouverture. N'oubliez pas d'apporter ce ticket ou votre numéro de référence.</p>
                
                ${repair.paymentStatus !== 'paid' ? `
                <p><strong>⚠️ Rappel de paiement :</strong><br>
                Montant restant à payer : ${(repair.repairPrice - repair.downPayment).toFixed(3)} TND</p>
                ` : ''}
                
                <div style="margin: 20px 0; padding: 15px; background-color: #e8f5e8; border-radius: 5px;">
                    <h4>Informations de l'atelier :</h4>
                    <p><strong>${shopName}</strong></p>
                    ${shopAddress ? `<p>📍 ${shopAddress}</p>` : ''}
                    ${shopPhone ? `<p>📞 ${shopPhone}</p>` : ''}
                </div>
                
                <p>Merci de votre confiance !</p>
                
                <p>Cordialement,<br>
                L'équipe de <strong>${shopName}</strong></p>
            </div>
            <div class="footer">
                <p>Cet email a été envoyé automatiquement. Merci de ne pas y répondre.</p>
            </div>
        </div>
    </body>
    </html>
  `;

  const textBody = `
Bonjour ${repair.customerName},

Nous avons le plaisir de vous informer que la réparation de votre appareil est maintenant terminée !

Détails de la réparation :
- Appareil : ${repair.phoneModel}
- Problème : ${repair.problemDescription}
- Numéro de ticket : #${repair.ticketNumber || repair.id.slice(-6)}
- Prix de la réparation : ${repair.repairPrice.toFixed(3)} TND
- Statut du paiement : ${getPaymentStatusText(repair.paymentStatus)}

Votre appareil est prêt à être récupéré !

Vous pouvez venir le récupérer pendant nos heures d'ouverture. N'oubliez pas d'apporter ce ticket ou votre numéro de référence.

${repair.paymentStatus !== 'paid' ? `
⚠️ Rappel de paiement :
Montant restant à payer : ${(repair.repairPrice - repair.downPayment).toFixed(3)} TND
` : ''}

Informations de l'atelier :
${shopName}
${shopAddress ? shopAddress : ''}
${shopPhone ? shopPhone : ''}

Merci de votre confiance !

Cordialement,
L'équipe de ${shopName}

---
Cet email a été envoyé automatiquement. Merci de ne pas y répondre.
  `;

  return {
    subject,
    htmlBody,
    textBody,
  };
};

/**
 * Generate custom email template
 */
export const generateCustomEmailTemplate = (
  repair: RepairItem,
  customSubject: string,
  customMessage: string,
  repairShop?: RepairShop
): EmailTemplate => {
  const shopName = repairShop?.name || "Votre atelier de réparation";
  
  const subject = customSubject || `Message concernant votre réparation - ${repair.phoneModel}`;

  const htmlBody = `
    <!DOCTYPE html>
    <html lang="fr">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
        <style>
            body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
            .container { max-width: 600px; margin: 0 auto; padding: 20px; }
            .header { background-color: #2196F3; color: white; padding: 20px; text-align: center; border-radius: 8px 8px 0 0; }
            .content { background-color: #f9f9f9; padding: 20px; border-radius: 0 0 8px 8px; }
            .repair-details { background-color: white; padding: 15px; border-radius: 5px; margin: 15px 0; }
            .footer { text-align: center; margin-top: 20px; font-size: 12px; color: #666; }
            .custom-message { background-color: white; padding: 20px; border-radius: 5px; margin: 15px 0; white-space: pre-wrap; }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>📧 Message de ${shopName}</h1>
            </div>
            <div class="content">
                <p>Bonjour <strong>${repair.customerName}</strong>,</p>
                
                <div class="custom-message">
                    ${customMessage.replace(/\n/g, '<br>')}
                </div>
                
                <div class="repair-details">
                    <h4>Référence de votre réparation :</h4>
                    <ul>
                        <li><strong>Appareil :</strong> ${repair.phoneModel}</li>
                        <li><strong>Numéro de ticket :</strong> #${repair.ticketNumber || repair.id.slice(-6)}</li>
                    </ul>
                </div>
                
                <p>Cordialement,<br>
                L'équipe de <strong>${shopName}</strong></p>
            </div>
            <div class="footer">
                <p>Cet email a été envoyé depuis notre système de gestion des réparations.</p>
            </div>
        </div>
    </body>
    </html>
  `;

  const textBody = `
Bonjour ${repair.customerName},

${customMessage}

Référence de votre réparation :
- Appareil : ${repair.phoneModel}
- Numéro de ticket : #${repair.ticketNumber || repair.id.slice(-6)}

Cordialement,
L'équipe de ${shopName}

---
Cet email a été envoyé depuis notre système de gestion des réparations.
  `;

  return {
    subject,
    htmlBody,
    textBody,
  };
};

/**
 * Helper function to get payment status text in French
 */
function getPaymentStatusText(status: string): string {
  switch (status) {
    case 'paid':
      return 'Payé intégralement';
    case 'partial':
      return 'Partiellement payé';
    case 'unpaid':
      return 'Non payé';
    default:
      return status;
  }
}

/**
 * Send email using a web service (placeholder implementation)
 * In a real implementation, this would integrate with an email service like:
 * - EmailJS for client-side sending
 * - A backend API that uses nodemailer, SendGrid, etc.
 * - Supabase Edge Functions with email service
 */
export const sendEmail = async (
  to: string,
  template: EmailTemplate,
  config?: EmailConfig
): Promise<{ success: boolean; error?: string }> => {
  try {
    // This is a placeholder implementation
    // In a real app, you would integrate with an actual email service
    
    console.log('Sending email to:', to);
    console.log('Subject:', template.subject);
    console.log('HTML Body:', template.htmlBody);
    console.log('Text Body:', template.textBody);
    console.log('Config:', config);
    
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // For now, we'll just log the email and return success
    // TODO: Integrate with actual email service
    
    return { success: true };
  } catch (error) {
    console.error('Error sending email:', error);
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    };
  }
};

/**
 * Validate email address
 */
export const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};
