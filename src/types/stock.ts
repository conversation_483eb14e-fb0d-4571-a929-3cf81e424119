export interface ProductCategory {
  id: string;
  name: string;
  description?: string;
  icon?: string;
  color?: string;
  createdAt: Date;
  repairShopId: string;
}

export interface Product {
  id: string;
  name: string;
  description?: string;
  sku: string;
  barcode?: string;
  categoryId: string;
  category?: ProductCategory;
  price: number;
  cost: number;
  stockQuantity: number;
  minStockLevel: number;
  maxStockLevel?: number;
  isActive: boolean;
  imageUrl?: string;
  createdAt: Date;
  updatedAt: Date;
  repairShopId: string;
}

export interface SaleItem {
  id: string;
  productId: string;
  product?: Product;
  quantity: number;
  unitPrice: number;
  discount: number;
  totalPrice: number;
}

export interface Sale {
  id: string;
  saleNumber: string;
  customerId?: string;
  customerName?: string;
  customerPhone?: string;
  items: SaleItem[];
  subtotal: number;
  taxAmount: number;
  discountAmount: number;
  totalAmount: number;
  paymentMethod: "cash" | "card" | "mobile" | "mixed";
  paymentStatus: "paid" | "partial" | "pending";
  amountPaid: number;
  changeAmount: number;
  notes?: string;
  createdAt: Date;
  userId: string;
  repairShopId: string;
}

export interface SaleItem {
  id: string;
  saleId: string;
  productId: string;
  product?: Product;
  quantity: number;
  unitPrice: number;
  discount: number;
  totalPrice: number;
  createdAt: Date;
  updatedAt: Date;
}

export interface StockMovement {
  id: string;
  productId: string;
  product?: Product;
  type: "in" | "out" | "adjustment";
  quantity: number;
  reason: string;
  reference?: string; // Sale ID, Purchase ID, etc.
  createdAt: Date;
  userId: string;
  repairShopId: string;
}

export interface ProductFormData {
  name: string;
  description?: string;
  sku: string;
  barcode?: string;
  categoryId: string;
  price: number;
  cost: number;
  stockQuantity: number;
  minStockLevel: number;
  maxStockLevel?: number;
  isActive: boolean;
  imageUrl?: string;
}

export interface CategoryFormData {
  name: string;
  description?: string;
  icon?: string;
  color?: string;
}

export interface SaleFormData {
  customerId?: string;
  customerName?: string;
  customerPhone?: string;
  items: {
    productId: string;
    quantity: number;
    unitPrice: number;
    discount: number;
  }[];
  discountAmount: number;
  paymentMethod: "cash" | "card" | "mobile" | "mixed";
  amountPaid: number;
  notes?: string;
}

// POS Cart Item for the interface
export interface CartItem {
  product: Product;
  quantity: number;
  unitPrice: number;
  discount: number;
  discountType: "percentage" | "fixed";
  totalPrice: number;
}

export interface HeldOrder {
  id: string;
  name: string;
  items: CartItem[];
  customerName?: string;
  customerPhone?: string;
  subtotal: number;
  totalDiscount: number;
  totalAmount: number;
  createdAt: Date;
  notes?: string;
}

export interface OrderDiscount {
  type: "percentage" | "fixed";
  value: number;
  reason?: string;
}

// Stock alert interface
export interface StockAlert {
  product: Product;
  currentStock: number;
  minStockLevel: number;
  alertType: "low" | "out";
}

// Sales summary for reports
export interface SalesSummary {
  totalSales: number;
  totalRevenue: number;
  totalProfit: number;
  topProducts: {
    product: Product;
    quantitySold: number;
    revenue: number;
  }[];
  salesByCategory: {
    category: ProductCategory;
    sales: number;
    revenue: number;
  }[];
}
