// Widget types available in the dashboard
export type WidgetType =
  | "repair-status"
  | "recent-repairs"
  | "repairs-by-status"
  | "repairs-by-device"
  | "welcome"
  | "payment-status"
  | "income";

// Position of a widget in the grid
export interface WidgetPosition {
  x: number;
  y: number;
}

// Size of a widget in the grid
export interface WidgetSize {
  w: number;
  h: number;
}

// Configuration for a dashboard widget
export interface WidgetConfig {
  id: string;
  type: WidgetType;
  title: string;
  position: WidgetPosition;
  size: WidgetSize;
  minSize?: WidgetSize;
  settings?: Record<string, any>;
}
