export interface RepairShop {
  id: string;
  name: string;
  address?: string;
  phone?: string;
  createdAt: Date;
}

export interface UserRepairShop {
  id: string;
  userId: string;
  repairShopId: string;
  role: "administrator" | "technician" | "receptionist" | "cashier";
  repairShop?: RepairShop;
  createdAt: Date;
}

export interface Observation {
  id: string;
  text: string;
  createdAt: Date;
  userId: string;
}

export interface PriceModification {
  id: string;
  amount: number;
  reason: string;
  createdAt: Date;
  userId: string;
}

export interface StatusHistory {
  id: string;
  status: "pending" | "inProgress" | "completed" | "cancelled" | "returned";
  createdAt: Date;
  userId: string;
}

export interface RepairItem {
  id: string;
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  phoneModel: string;
  problemDescription: string;
  repairPrice: number;
  paymentStatus: "paid" | "partial" | "unpaid";
  downPayment: number;
  createdAt: Date;
  completedAt?: Date;
  status: "pending" | "inProgress" | "completed" | "cancelled" | "returned";
  userId: string;
  repairShopId: string;
  repairShop?: RepairShop;
  observations?: Observation[];
  priceModifications?: PriceModification[];
  statusHistory?: StatusHistory[];
  ticketNumber?: number;
  assignedTechnician?: string;
}

export interface RepairFormData {
  customerName: string;
  customerPhone: string;
  customerEmail?: string;
  phoneModel: string;
  problemDescription: string;
  repairPrice: number;
  paymentStatus: "paid" | "partial" | "unpaid";
  downPayment: number;
  observations?: string[];
  customFormResponses?: {
    templateId?: string;
    responses?: Record<string, any>;
  };
  stockItems?: {
    id: string;
    quantity: number;
  }[];
  assignedTechnician?: string;
}

export interface Invoice {
  id: string;
  invoiceNumber: string;
  repairShopId: string;
  customerName: string;
  customerPhone?: string;
  subtotal: number;
  discountAmount: number;
  totalAmount: number;
  status: "draft" | "sent" | "paid";
  notes?: string;
  userId: string;
  createdAt: Date;
  updatedAt: Date;
  items: InvoiceItem[];
}

export interface InvoiceItem {
  id: string;
  invoiceId: string;
  repairId: string;
  description: string;
  amount: number;
  repair?: RepairItem;
}

export interface InvoiceFormData {
  repairIds: string[];
  discountAmount: number;
  notes?: string;
}
