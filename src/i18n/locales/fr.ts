// Remove the Translation import since it's not used
const translations = {
  app: {
    title: "Maaoui Store",
    slogan: "Bienvenue à Maaoui Store",
  },
  common: {
    optional: "optionnel",
    dashboard: "Tableau de bord",
    newRepair: "Nouvelle réparation",
    search: "Rechercher",
    stock: "Stock & PDV",
    searchRepairs: "Rechercher des réparations",
    viewDetails: "Voir les détails",
    clickToView: "Cliquer pour voir",
    loading: "Chargement...",
    processing: "Traitement...",
    submit: "Soumettre",
    update: "Mettre à jour",
    delete: "Supprimer",
    cancel: "Annuler",
    save: "Enregistrer",
    edit: "Modifier",
    back: "Retour au",
    areYouSure: "Êtes-vous sûr(e) ?",
    success: "Succès !",
    error: "Erreur !",
    print: "Imprimer",
    download: "Télécharger",
    balance: "Solde",
    pageNotFound: "Oups ! Page non trouvée",
    returnHome: "Retour à l'accueil",
    copied: "<PERSON><PERSON><PERSON> dans le presse-papiers",
    call: "Appel<PERSON>",
    startScanning: "Commencer le scan",
    stopScanning: "Arrêter le scan",
    scannerActive: "Scanner actif et prêt",
    scannerInactive: "Scanner inactif",
    logo: "Logo",
    repairShop: "Atelier de Réparation",
    tel: "Tél",
    phone: "Téléphone",
    clear: "Effacer",
    edit: "Modifier",
    actions: "Actions",
    details: "Détails",
    user: "Utilisateur",
    date: "Date",
    premium: "Premium",
    viewDetails: "Voir Détails",
    customerName: "Nom du Client",
    customerPhone: "Téléphone du Client",
    notes: "Notes",
    all: "Tous",
  },
  repair: {
    createNew: "Créer une nouvelle réparation",
    repairDetails: "Détails de la réparation",
    repairTimeline: "Chronologie de la réparation",
    statusChanged: "Statut changé en",
    customerInfo: "Details du client",
    customerName: "Nom de client",
    customerPhone: "Num Tel",
    stat: "Statut",
    phoneModel: "modèle d'appareil",
    problemDescription: "Description du problème",
    observations: "Observations",
    observationsOptional: "(optionnel)",
    addObservation: "Ajouter une observation",
    noObservations: "Aucune observation pour le moment",
    observationAdded: "Observation ajoutée avec succès",
    observationAddError: "Échec de l'ajout de l'observation",
    repairPrice: "Prix de la réparation",
    initialPrice: "Prix Initial",
    totalPrice: "Prix Total",
    paymentStatus: "Statut du paiement",
    downPayment: "Acompte",
    priceModifications: "Modifications de Prix",
    priceModified: "Le prix a été modifié",
    addPriceModification: "Ajouter une Modification de Prix",
    priceModificationAdded: "Modification de prix ajoutée avec succès",
    priceModificationAddError: "Échec de l'ajout de la modification de prix",
    amount: "Montant",
    reason: "Raison",
    createdAt: "Date de création",
    completedAt: "Date de complétion",
    submitRepair: "Soumettre la réparation",
    successCreated: "Réparation créée avec succès !",
    failedCreated: "Erreur lors de la création de la réparation.",
    successUpdated: "Réparation mise à jour avec succès !",
    notFound: "Réparation non trouvée.",
    active: "Actives",
    completed: "Terminées",
    returned: "Retournées",
    all: "Toutes",
    noActiveRepairs: "Aucune réparation active trouvée",
    noCompletedRepairs: "Aucune réparation terminée trouvée",
    noReturnedRepairs: "Aucune réparation retournée trouvée",
    noRepairsFound: "Aucune réparation trouvée",
    createFirstRepair: "Créer votre première réparation",
    addedAgo: "Ajoutée il y a {{time}}",
    viewDetails: "Voir les détails",
    scanQrCode: "Scanner le QR Code",
    scanBarcode: "Scanner ce code-barres avec un scanner 1D",
    barcodeSticker: "Étiquette Code-barres",
    repairTicket: "Ticket de Réparation",
    ticketNumber: "Numéro de Ticket",
    qrCode: "QR Code",
    selectCodeType: "Sélectionner le Type de Code",
    selectCodeTypeDescription:
      "Choisissez quel type de code inclure sur le ticket imprimé",
    repairNumber: "Numéro de Réparation",
    thankYou: "Merci de votre confiance !",
    bringReceipt:
      "Veuillez apporter ce ticket lors de la récupération de votre appareil.",
    disclaimer:
      "Note : Nous ne sommes pas responsables si vous perdez votre téléphone ou s'il dysfonctionne dans une période qui dépasse 30 jours",
    printTicket: "Imprimer le Ticket",
    updateStatus: "Mettre à jour le statut",
    deleteRepair: "Supprimer la réparation",
    deleteConfirmation:
      "Êtes-vous sûr(e) de vouloir supprimer cette réparation ? Cette action est irréversible.",
    enterDeleteCode: "Entrez le code de suppression",
    invalidDeleteCode: "Code de suppression invalide. Veuillez réessayer.",
    successDeleted: "Réparation supprimée avec succès !",
    payment: {
      paid: "Payé",
      partial: "Partiel",
      unpaid: "Non payé",
    },
    status: {
      pending: "En attente",
      inProgress: "En cours",
      completed: "Terminée",
      returned: "Retournée",
      cancelled: "Annulée",
    },
    addError: "Erreur lors de l'ajout de la réparation.",
    fetchError: "Erreur lors de la récupération des réparations.",
    updateError: "Erreur lors de la mise à jour de la réparation.",
    deleteError: "Erreur lors de la suppression de la réparation.",
    importError: "Erreur lors de l'importation des réparations.",
    noRepairFoundWithTicket:
      "Aucune réparation trouvée avec ce numéro de ticket. Veuillez vérifier et réessayer.",
    noTicketNumberForBarcode:
      "Aucun numéro de ticket disponible pour générer le code-barres.",
  },
  searchRepairs: {
    title: "Rechercher des réparations",
    noResults: "Aucune réparation trouvée",
    adjustFilters: "Essayez d'ajuster votre recherche ou vos filtres",
  },
  filters: {
    title: "Filtres",
    slogan: "Appliquer des filtres pour affiner votre recherche",
    status: "Statut",
    payment: "Paiement",
    clear: "Effacer les filtres",
    anyStatus: "Tous les statuts",
    anyPaymentStatus: "Tous les statuts de paiement",
    clearAll: "Effacer tous les filtres",
    dateFrom: "Date de début",
    dateTo: "Date de fin",
  },
  importExport: {
    import: "Importer",
    export: "Exporter",
    importRepairs: "Importer des Réparations",
    exportRepairs: "Exporter les Réparations",
    importDescription: "Importer les réparations à partir d'un fichier JSON",
    exportDescription: "Exporter toutes les réparations vers un fichier JSON",
    selectFile: "Sélectionner un fichier",
    downloadFile: "Télécharger le fichier",
    uploadFile: "Téléverser le fichier",
    chooseFile: "Choisir un fichier",
    downloadTemplate: "Télécharger le modèle",
    importedSuccess: "Réparations importées avec succès !",
    exportedSuccess: "Réparations exportées avec succès !",
    invalidFile:
      "Format de fichier invalide. Veuillez utiliser un fichier JSON valide.",
    importConfirmation:
      "Êtes-vous sûr(e) de vouloir importer ces réparations ? Cela écrasera toutes les données existantes.",
    successImport: "Réparations importées avec succès !",
    failedImport: "Erreur lors de l'importation des réparations.",
    successExport: "Réparations exportées avec succès !",
    failedExport: "Erreur lors de l'exportation des réparations.",
  },
  analytics: {
    title: "Analytiques de paiement",
    totalAmountPaid: "Montant total payé",
    averageAmountPaid: "Montant moyen payé",
    monthlyRevenue: "Recettes mensuelles",
  },
  repairAnalytics: {
    title: "Statistiques des Réparations",
    totalRepairs: "Nombre total de réparations",
    completedRepairs: "Réparations terminées",
    pendingRepairs: "Réparations en attente",
    totalRevenue: "Revenu total",
    averageRepairPrice: "Prix moyen par réparation",
  },
  auth: {
    login: "Connexion",
    logout: "Déconnexion",
    email: "Email",
    password: "Mot de passe",
    loginToContinue: "Connectez-vous pour continuer",
    genericError: "Une erreur s'est produite lors de la connexion",
    emailRequired: "L'email est requis",
    passwordRequired: "Le mot de passe est requis",
    invalidCredentials: "Email ou mot de passe incorrect",
  },
  languageSwitcher: {
    language: "Langue",
    english: "Anglais",
    french: "Français",
    arabic: "Arabe",
  },
  repairShop: {
    title: "Atelier de Réparation",
    name: "Nom de l'atelier",
    address: "Adresse",
    phone: "Téléphone",
    fetchError: "Erreur lors de la récupération des informations de l'atelier",
  },
  scanner: {
    henexScanner: "Scanner Dédié",
    cameraScanner: "Scanner Caméra",
    barcode: "Code-barres",
    henexInstructions:
      "Le scanner dédié est actif. Scannez un code-barres ou QR code directement",
    scannerInstructions:
      "Appuyez sur le bouton du scanner pour scanner un code",
    scanPlaceholder: "Scanner un code...",
    scanSuccess: "Code scanné avec succès!",
    scanError: "Erreur lors du scan du code. Veuillez réessayer.",
    autoScanEnabled: "Scan automatique activé",
    readyToScan: "Prêt à scanner",
    scanningActive: "Scanner actif",
  },
  shortcuts: {
    title: "Raccourcis clavier",
    description: "Utilisez ces raccourcis clavier pour naviguer rapidement",
    keyboard: "Raccourcis",
    showShortcuts: "Afficher les raccourcis clavier",
    tip: "Appuyez sur ? à tout moment pour afficher cette aide",
    goToDashboard: "Aller au tableau de bord (Alt+H)",
    newRepair: "Créer une nouvelle réparation (Alt+N)",
    printCurrentRepair: "Imprimer la réparation actuelle (Alt+P)",
    help: "Afficher les raccourcis clavier (?)",
    navigatedToDashboard: "Navigation vers le tableau de bord",
    creatingNewRepair: "Création d'une nouvelle réparation",
    printing: "Impression...",
  },
  theme: {
    light: "Clair",
    dark: "Sombre",
    system: "Système",
    toggleDarkMode: "Basculer en mode sombre",
  },
  dashboard: {
    title: "Tableau de bord",
    editLayout: "Modifier la disposition",
    saveLayout: "Enregistrer la disposition",
    reset: "Réinitialiser",
    addWidget: "Ajouter un widget",
    widgetAdded: "Widget ajouté avec succès",
    widgetRemoved: "Widget supprimé",
    dashboardReset: "Tableau de bord réinitialisé aux paramètres par défaut",
    widgetType: "Type de widget",
    widgetTitle: "Titre du widget",
    selectWidgetType: "Sélectionner le type de widget",
    addWidgetDescription:
      "Choisissez un type de widget et personnalisez son titre",
    totalRepairs: "Total des réparations",
    repairs: "Réparations",
    noRepairsFound: "Aucune réparation trouvée",
    unknownDevice: "Appareil inconnu",
    goodMorning: "Bonjour",
    goodAfternoon: "Bon après-midi",
    goodEvening: "Bonsoir",
    user: "Utilisateur",
    welcomeMessage:
      "Bienvenue sur votre tableau de bord personnalisé. Vous pouvez voir ici un aperçu de votre atelier de réparation.",
    widgets: {
      welcome: {
        title: "Bienvenue",
        defaultTitle: "Bienvenue",
      },
      "repair-status": {
        title: "État des réparations",
        defaultTitle: "État des réparations",
      },
      "recent-repairs": {
        title: "Réparations récentes",
        defaultTitle: "Réparations récentes",
      },
      "repairs-by-status": {
        title: "Réparations par statut",
        defaultTitle: "Réparations par statut",
      },
      "repairs-by-device": {
        title: "Réparations par appareil",
        defaultTitle: "Réparations par appareil",
      },
      "payment-status": {
        title: "État des paiements",
        defaultTitle: "État des paiements",
      },
      income: {
        title: "Revenus",
        defaultTitle: "Revenus",
      },
    },
    paid: "Payé",
    unpaid: "Non payé",
    partial: "Partiellement payé",
    noPaymentData: "Aucune donnée de paiement disponible",
    monthlyIncome: "Revenu mensuel",
    totalIncome: "Revenu total",
    income: "Revenu",
    increase: "augmentation",
    decrease: "diminution",
    allTime: "Tout le temps",
  },
  performanceMonitor: {
    title: "Moniteur de Performance",
    subtitle:
      "Surveiller les performances, analyser les métriques et optimiser les opérations commerciales",
    exportReport: "Exporter le Rapport de Performance",
    processOverview: "Aperçu des Processus",
    businessInsights: "Insights Business",
    improvementOpportunities: "Opportunités d'Amélioration",
    analysisPeriod: "Période d'Analyse",
    analyzingWorkflow:
      "Analyse des modèles de performance pour optimiser les opérations",
    workshopCapacity: "Capacité de l'Atelier",
    totalProductiveTime: "Temps productif total",
    processCompletion: "Achèvement des Processus",
    successfulOutcomes: "Résultats réussis",
    activeWorkflows: "Flux de Travail Actifs",
    currentlyInProgress: "Actuellement en cours",
    processEfficiency: "Efficacité des Processus",
    averageCycleTime: "Temps de cycle moyen",
    overduePending: "En Attente en Retard",
    pendingOverThreeDays: "En attente >3 jours • Cliquer pour voir",
    businessPerformanceMetrics: "Métriques de Performance Business",
    operationalExcellence:
      "Indicateurs clés pour l'excellence opérationnelle et la satisfaction client",
    averageServiceTime: "Temps de Service Moyen",
    industryBenchmark: "Référence industrie: 45-90min",
    processSuccessRate: "Taux de Réussite des Processus",
    targetRate: "Objectif: >85%",
    throughput: "Débit (réparations/heure)",
    workshopCapacityIndicator: "Indicateur de capacité d'atelier",
    strengthsIdentified: "Forces Identifiées",
    optimizationOpportunities: "Opportunités d'Optimisation",
    today: "Aujourd'hui",
    yesterday: "Hier",
    thisWeek: "Cette Semaine",
    thisMonth: "Ce Mois",
  },
  workflowIntelligence: {
    title: "Intelligence des Flux de Travail",
    subtitle:
      "Optimiser les processus, améliorer l'efficacité et améliorer le service client",
    processOverview: "Aperçu des Processus",
    businessInsights: "Insights Business",
    improvementOpportunities: "Opportunités d'Amélioration",
    analysisPeriod: "Période d'Analyse",
    analyzingWorkflow:
      "Analyse des modèles de flux de travail pour optimiser les opérations",
    workshopCapacity: "Capacité de l'Atelier",
    totalProductiveTime: "Temps productif total",
    processCompletion: "Achèvement des Processus",
    successfulOutcomes: "Résultats réussis",
    activeWorkflows: "Flux de Travail Actifs",
    currentlyInProgress: "Actuellement en cours",
    processEfficiency: "Efficacité des Processus",
    averageCycleTime: "Temps de cycle moyen",
    workshopResourceUtilization: "Utilisation des Ressources de l'Atelier",
    capacityDistribution:
      "Comprendre la répartition de la capacité dans l'équipe",
    processFlowAnalysis: "Analyse du Flux de Processus",
    workflowStageDistribution:
      "Distribution des étapes du flux de travail et identification des goulots d'étranglement",
    businessPerformanceMetrics: "Métriques de Performance Business",
    operationalExcellence:
      "Indicateurs clés pour l'excellence opérationnelle et la satisfaction client",
    averageServiceTime: "Temps de Service Moyen",
    industryBenchmark: "Référence industrie: 45-90min",
    processSuccessRate: "Taux de Réussite des Processus",
    targetRate: "Objectif: >85%",
    throughput: "Débit (réparations/heure)",
    workshopCapacityIndicator: "Indicateur de capacité d'atelier",
    processImprovementOpportunities:
      "Opportunités d'Amélioration des Processus",
    dataDrivernInsights:
      "Insights basés sur les données pour améliorer l'efficacité de l'atelier et la satisfaction client",
    strengthsIdentified: "Forces Identifiées",
    highCompletionRate:
      "Un taux d'achèvement élevé indique un bon contrôle des processus",
    consistentServiceTimes:
      "Des temps de service cohérents montrent des procédures standardisées",
    optimizationAreas: "Zones d'Optimisation",
    workflowAutomation:
      "Considérer l'automatisation du flux de travail pour les tâches routinières",
    peakHourAnalysis:
      "Analyser les heures de pointe pour une meilleure planification des ressources",
    exportIntelligenceReport: "Exporter le Rapport d'Intelligence",
    accessDenied:
      "Accès refusé. Vous avez besoin de privilèges d'intelligence de flux de travail.",
    today: "Aujourd'hui",
    yesterday: "Hier",
    thisWeek: "Cette Semaine",
    thisMonth: "Ce Mois",
    inProgress: "En Cours",
    completed: "Terminé",
    overduePending: "En Attente en Retard",
    pendingOverThreeDays: "En attente >3 jours • Cliquer pour voir",
    performanceSummary: "Résumé de Performance",
    successRate: "Taux de Réussite",
    avgServiceTime: "Temps de Service Moyen",
    repairsPerHour: "Réparations/Heure",
    overdue: "En Retard",
    excellentSuccessRate: "Excellent Taux de Réussite",
    successRateExceeds:
      "Votre taux d'achèvement de {{rate}}% dépasse les standards de l'industrie",
    efficientServiceTimes: "Temps de Service Efficaces",
    avgTimeOptimal:
      "Moyenne de {{time}} minutes par réparation est dans la plage optimale",
    highProductivity: "Haute Productivité",
    completingRepairsPerHour:
      "Compléter {{rate}} réparations par heure montre une forte efficacité",
    buildingFoundation: "Construction de Fondation",
    focusOnOptimization:
      "Se concentrer sur les zones d'optimisation pour améliorer les métriques de performance",
    optimizationOpportunities: "Opportunités d'Optimisation",
    addressOverdueRepairs: "Traiter les Réparations en Retard",
    repairsPendingAttention:
      "{{count}} réparations en attente >3 jours nécessitent une attention immédiate",
    improveCompletionRate: "Améliorer le Taux d'Achèvement",
    currentRateCanImprove:
      "Le taux actuel de {{rate}}% peut être amélioré à l'objectif de 85%+",
    optimizeServiceTime: "Optimiser le Temps de Service",
    avgTimeExceedsBenchmark:
      "Moyenne de {{time}} minutes dépasse la référence de 90min",
    increaseThroughput: "Augmenter le Débit",
    currentThroughputImprove:
      "Le débit actuel de {{rate}} réparations/heure peut être amélioré",
    workflowStandardization: "Standardisation du Flux de Travail",
    createChecklistsReduce:
      "Créer des listes de contrôle pour les types de réparation courants pour réduire la variation",
    recommendedActions: "Actions Recommandées",
    urgentProcessOverdue: "Urgent: Traiter les Réparations en Retard",
    reviewPrioritizeRepairs:
      "Examiner et prioriser {{count}} réparations en attente depuis plus de 3 jours",
    teamTraining: "Formation d'Équipe",
    identifySkillGaps:
      "Identifier les lacunes de compétences et fournir une formation ciblée pour l'efficacité",
    processDocumentation: "Documentation des Processus",
    documentBestPractices:
      "Documenter les meilleures pratiques des périodes de haute performance",
    capacityPlanning: "Planification de Capacité",
    useDataOptimizeStaffing:
      "Utiliser les données pour optimiser le personnel pendant les périodes de pointe",
  },
  data: {
    export: "Exporter les données",
    import: "Importer les données",
    successExport: "Données exportées avec succès",
    failedExport: "Échec de l'exportation des données",
    successImport: "Données importées avec succès",
    failedImport: "Échec de l'importation des données",
  },
  stock: {
    title: "Gestion de Stock & PDV",
    pos: "Point de Vente",
    products: "Produits",
    categories: "Catégories",
    sales: "Ventes",
    inventory: "Inventaire",
    reports: "Rapports",

    // Product Management
    addProduct: "Ajouter Produit",
    editProduct: "Modifier Produit",
    deleteProduct: "Supprimer Produit",
    productName: "Nom du Produit",
    productDescription: "Description",
    sku: "SKU",
    barcode: "Code-barres",
    category: "Catégorie",
    price: "Prix",
    cost: "Coût",
    stock: "Stock",
    minStock: "Stock Min",
    maxStock: "Stock Max",
    isActive: "Actif",
    image: "Image",

    // Categories
    addCategory: "Ajouter Catégorie",
    editCategory: "Modifier Catégorie",
    categoryName: "Nom de Catégorie",
    categoryDescription: "Description de Catégorie",

    // POS Interface
    cart: "Panier",
    addToCart: "Ajouter au Panier",
    removeFromCart: "Retirer",
    clearCart: "Vider le Panier",
    quantity: "Quantité",
    unitPrice: "Prix Unitaire",
    discount: "Remise",
    subtotal: "Sous-total",
    tax: "Taxe",
    total: "Total",

    // Payment
    payment: "Paiement",
    paymentMethod: "Méthode de Paiement",
    cash: "Espèces",
    card: "Carte",
    mobile: "Paiement Mobile",
    mixed: "Paiement Mixte",
    amountPaid: "Montant Payé",
    change: "Monnaie",
    processSale: "Traiter la Vente",

    // Customer
    customer: "Client",
    customerName: "Nom du Client",
    customerPhone: "Téléphone du Client",

    // Sales
    saleNumber: "Vente #",
    saleDate: "Date de Vente",
    saleTotal: "Total de Vente",
    viewSale: "Voir Vente",

    // Inventory
    lowStock: "Stock Faible",
    outOfStock: "Rupture de Stock",
    stockLevel: "Niveau de Stock",
    stockMovement: "Mouvement de Stock",
    stockIn: "Entrée de Stock",
    stockOut: "Sortie de Stock",
    adjustment: "Ajustement",

    // Messages
    productAdded: "Produit ajouté avec succès",
    productUpdated: "Produit mis à jour avec succès",
    productDeleted: "Produit supprimé avec succès",
    categoryAdded: "Catégorie ajoutée avec succès",
    categoryUpdated: "Catégorie mise à jour avec succès",
    categoryDeleted: "Catégorie supprimée avec succès",
    saleCompleted: "Vente terminée avec succès",
    insufficientStock: "Stock insuffisant",
    invalidQuantity: "Quantité invalide",
    fetchError: "Erreur de récupération des données",
    addError: "Erreur d'ajout d'élément",
    updateError: "Erreur de mise à jour d'élément",
    deleteError: "Erreur de suppression d'élément",
    saleError: "Erreur de traitement de vente",
    stockAdjusted: "Stock ajusté avec succès",
    adjustError: "Erreur d'ajustement de stock",

    // Touch Interface
    touchMode: "Mode Tactile",
    gridView: "Vue Grille",
    listView: "Vue Liste",
    searchProducts: "Rechercher des produits...",

    // Barcode Scanner
    scan: "Scanner",
    scanBarcode: "Scanner Code-barres",
    barcodeScanner: "Scanner de Code-barres",
    manualEntry: "Saisie Manuelle",
    cameraScanner: "Scanner Caméra",
    enterBarcode: "Entrer Code-barres",
    addToCart: "Ajouter au Panier",
    productNotFound: "Produit non trouvé",
    productInactive: "Produit inactif",
    productOutOfStock: "Produit en rupture de stock",
    barcodeAdded: "Produit ajouté au panier via code-barres",
    scannerActive: "Scanner Actif - Prêt à scanner",
    scanning: "Scan en cours",
    scannerInstructions: "Le scan automatique de codes-barres est actif :",
    useBarcodeScannerDevice: "Utiliser un scanner",
    typeBarcodeDirect: "Taper directement",
    automaticCartAdd: "Ajout auto au panier",

    // Sales Ticket & Printing
    salesTicket: "Ticket de Vente",
    printTicket: "Imprimer Ticket",
    print: "Imprimer",
    saleNumber: "Numéro de Vente",
    date: "Date",
    items: "Articles",
    discount: "Remise",
    paid: "Payé",
    partial: "Partiel",
    pending: "En attente",
    thankYouVisit: "Merci de votre visite !",
    keepTicket: "Veuillez conserver ce ticket",
    printInstructions1: "Optimisé pour imprimantes thermiques 80mm",
    printInstructions2: "Assurez-vous que l'imprimante est prête",
    printInstructions3: "Cliquez Imprimer pour générer le reçu",

    // Sales History
    todaysSales: "Ventes d'Aujourd'hui",
    todaysRevenue: "Revenus d'Aujourd'hui",
    totalSales: "Total des Ventes",
    noSalesYet: "Aucune vente encore",
    walkInCustomer: "Client de Passage",
    saleId: "ID de Vente",
    totalValue: "Valeur Totale",
    noLowStockItems: "Aucun article en stock faible",
    noOutOfStockItems: "Aucun article en rupture",

    // Barcode Scanner
    cameraStarted: "Caméra démarrée. Pointez vers le code-barres pour scanner.",
    cameraError:
      "Impossible d'accéder à la caméra. Veuillez utiliser la saisie manuelle.",
    manualEntry: "Manuel",
    cameraScanner: "Caméra",
    enterBarcodeManually: "Saisir le Code-Barres Manuellement",
    enterOrScanBarcode: "Saisir ou scanner le code-barres...",
    pointCameraAtBarcode: "Pointez la caméra vers le code-barres pour scanner",
    orTypeBarcodeHere: "Ou tapez le code-barres ici...",
    cameraNotStarted: "Caméra non démarrée",
    startCamera: "Démarrer la Caméra",

    // Barcode Sticker
    generateSticker: "Générer Étiquette",
    printSticker: "Imprimer Étiquette",
    generateBarcode: "Générer Code-barres",
    generateEAN13Barcode: "Générer Code-barres EAN13",
    barcodeSticker: "Étiquette Code-barres",
    stickerPreview: "Aperçu de l'Étiquette",
    copies: "Copies",
    productNeedsBarcode: "Ce produit a besoin d'un code-barres",
    addBarcodeToProduct:
      "Ajoutez un code-barres à ce produit pour imprimer des étiquettes",
    generateNewBarcode:
      "Ce produit n'a pas de code-barres. Générez-en un ci-dessous.",

    // Product Management
    showInactive: "Afficher Inactifs",
    hideInactive: "Masquer Inactifs",
    inactive: "Inactif",
    product: "Produit",
    sku: "SKU",
    price: "Prix",
    stock: "Stock",
    status: "Statut",
    actions: "Actions",
    tryAdjustingSearch: "Essayez d'ajuster vos termes de recherche",
    getStartedAddProduct: "Commencez par ajouter votre premier produit",

    // POS Interface Messages
    cartIsEmpty: "Le panier est vide",
    insufficientPayment: "Montant de paiement insuffisant",
    saleFailed: "Échec de la vente",
    unknownError: "Erreur inconnue",
    saleProcessedSuccessfully: "Vente traitée avec succès !",

    // Quick Setup
    userOrShopNotFound: "Utilisateur ou atelier non trouvé",
    errorCheckingSetup: "Erreur lors de la vérification de la configuration",
    errorCreatingSampleData: "Erreur lors de la création des données d'exemple",
    sampleDataCreatedSuccessfully: "Données d'exemple créées avec succès !",
    checking: "Vérification...",
    checkSetup: "Vérifier la Configuration",
    createSampleData: "Créer des Données d'Exemple",

    // Product Form
    priceAndCostMustBePositive:
      "Le prix et le coût doivent être des nombres positifs",
    enterProductName: "Saisir le nom du produit",
    enterSKU: "Saisir le SKU",
    enterProductDescription: "Saisir la description du produit",
    enterBarcode: "Saisir le code-barres",
    searchProducts: "Rechercher des produits...",

    // Stock Validation Messages
    productNoLongerExists: "Le produit n'existe plus",
    productNoLongerActive: "Le produit n'est plus actif",
    insufficientStockDetailed:
      "Stock insuffisant pour {{productName}}. Disponible: {{available}}, Requis: {{required}}",

    // Advanced POS Features
    holdOrder: "Mettre en Attente",
    heldOrders: "Commandes en Attente",
    recallOrder: "Rappeler Commande",
    deleteOrder: "Supprimer Commande",
    orderHeld: "Commande mise en attente avec succès",
    orderRecalled: "Commande rappelée avec succès",
    orderDeleted: "Commande supprimée avec succès",
    applyDiscount: "Appliquer Remise",
    orderDiscount: "Remise Commande",
    itemDiscount: "Remise Article",
    discountType: "Type de Remise",
    percentage: "Pourcentage",
    fixedAmount: "Montant Fixe",
    discountValue: "Valeur de Remise",
    discountApplied: "Remise appliquée avec succès",
    notes: "Notes",
    addNotes: "Ajouter Notes",
    orderNotes: "Notes de Commande",
    noHeldOrders: "Aucune commande en attente",
    heldOn: "Mise en attente le",
    orderTotal: "Total Commande",
    discountAmount: "Montant de Remise",
    cart: "Panier",
    customer: "Client",
    customerName: "Nom du client",
    customerPhone: "Téléphone du client",
    payment: "Paiement",
    subtotal: "Sous-total",
    tax: "Taxe",
    total: "Total",
    paymentMethod: "Méthode de paiement",
    cash: "Espèces",
    card: "Carte",
    mobile: "Mobile",
    amountPaid: "Montant payé",
    change: "Monnaie",
    processSale: "Traiter la vente",
    insufficientStock: "Stock insuffisant",
  },

  // Fonctionnalités Premium
  userManagement: {
    title: "Gestion des Utilisateurs",
    createUser: "Créer Utilisateur",
    inviteUser: "Inviter Utilisateur",
    email: "Email",
    password: "Mot de passe",
    fullName: "Employé",
    role: "Rôle",
    administrator: "Administrateur",
    technician: "Technicien",
    receptionist: "Réceptionniste",
    cashier: "Caissier",
    userCreated: "Utilisateur créé avec succès",
    userUpdated: "Utilisateur mis à jour avec succès",
    userDeleted: "Utilisateur supprimé avec succès",
    removeUser: "Supprimer Utilisateur",
    changeRole: "Changer le Rôle",
    activityLog: "Journal d'Activité",
    noUsers: "Aucun utilisateur trouvé pour cet atelier",
    noActivity: "Aucun journal d'activité trouvé",
  },

  timeTracking: {
    title: "Suivi du Temps",
    overview: "Aperçu",
    entries: "Entrées de Temps",
    reports: "Rapports",
    totalTime: "Temps Total",
    activeRepairs: "Réparations Actives",
    activeTechnicians: "Techniciens Actifs",
    completed: "Terminé",
    activeNow: "Actif Maintenant",
    avgTime: "Temps Moyen",
    perRepair: "par réparation",
    inProgress: "En cours",
    timeByTechnician: "Temps par Technicien",
    statusDistribution: "Distribution des Statuts",
    technicianPerformance: "Performance des Techniciens",
    timeDistribution: "Distribution du Temps",
    totalWorkTime: "Temps de Travail Total",
    completionRate: "Taux de Completion",
    avgRepairTime: "Temps Moyen de Réparation",
    perCompletedRepair: "Par réparation terminée",
    timeSpent: "Temps Passé",
    repairs: "réparations",
    entries: "entrées",
    repairsFinished: "réparations terminées",
    noTimeEntries:
      "Aucune entrée de temps trouvée pour les filtres sélectionnés",
    noPerformanceData: "Aucune donnée de performance disponible",
    noTimeData: "Aucune donnée de temps disponible encore",
    period: "Période",
    today: "Aujourd'hui",
    yesterday: "Hier",
    thisWeek: "Cette Semaine",
    thisMonth: "Ce Mois",
    allTechnicians: "Tous les Techniciens",
    editTimeEntry: "Modifier l'Entrée de Temps",
    duration: "Durée",
    durationMinutes: "Durée (minutes)",
    manualEditNotAvailable:
      "Modification manuelle non disponible - le temps est calculé à partir des changements de statut de réparation",
    unknownUser: "Utilisateur Inconnu",
    started: "Commencé",
    repair: "Réparation",
    status: "Statut",
    actions: "Actions",
  },

  advancedStock: {
    title: "Gestion Avancée du Stock",
    inventory: "Inventaire",
    lowStock: "Stock Faible",
    categories: "Catégories",
    movements: "Mouvements de Stock",
    addProduct: "Ajouter Produit",
    addCategory: "Ajouter Catégorie",
    recordMovement: "Enregistrer Mouvement",
    productInventory: "Inventaire des Produits",
    lowStockAlerts: "Alertes de Stock Faible",
    productCategories: "Catégories de Produits",
    stockMovements: "Mouvements de Stock",
    noProducts: "Aucun produit trouvé",
    noLowStock: "Aucun produit en stock faible trouvé",
    noCategories: "Aucune catégorie trouvée",
    noMovements: "Aucun mouvement de stock trouvé",
    restock: "Réapprovisionner",
    editCategory: "Modifier Catégorie",
    editProduct: "Modifier Produit",
    adjust: "Ajuster",
    stockIn: "Entrée de Stock",
    stockOut: "Sortie de Stock",
    adjustment: "Ajustement Direct",
    reason: "Raison",
    reference: "Référence",
    newQuantity: "Nouvelle Quantité",
    quantityToAddRemove: "Quantité à ajouter/retirer",
    whyChangeBeingMade: "Pourquoi ce changement est-il effectué?",
    optionalReferenceNumber: "Numéro de référence optionnel",
    stockAdded: "Stock ajouté avec succès",
    stockRemoved: "Stock retiré avec succès",
    stockAdjusted: "Stock ajusté avec succès",
    notEnoughStock: "Stock insuffisant disponible",
    trackAllStockChanges: "Suivre tous les changements de stock",
    dateTime: "Date et Heure",
    product: "Produit",
    type: "Type",
    quantity: "Quantité",
    user: "Utilisateur",
    usedInRepair: "Utilisé dans la réparation",
  },

  assignTechnician: {
    assignTechnician: "Assigner Technicien",
    selectTechnician: "Sélectionner Technicien",
    chooseTechnician: "Choisir un technicien",
    noAssignment: "Aucune assignation",
    assignedTechnician: "Technicien Assigné",
    technicianAssignmentUpdated: "Assignation du technicien mise à jour",
    failedToUpdateTechnician:
      "Échec de la mise à jour de l'assignation du technicien",
  },

  customForms: {
    title: "Formulaires Personnalisés",
    createForm: "Créer Formulaire",
    editForm: "Modifier Formulaire",
    deleteForm: "Supprimer Formulaire",
    formName: "Nom du Formulaire",
    formDescription: "Description du Formulaire",
    deviceType: "Type d'Appareil",
    fields: "Champs",
    addField: "Ajouter Champ",
    fieldName: "Nom du Champ",
    fieldType: "Type de Champ",
    required: "Obligatoire",
    optional: "Optionnel",
    placeholder: "Texte d'Aide",
    options: "Options",
    fieldOrder: "Ordre du Champ",
    textField: "Champ Texte",
    numberField: "Champ Numérique",
    textareaField: "Zone de Texte",
    selectField: "Liste Déroulante",
    checkboxField: "Case à Cocher",
    formCreated: "Formulaire créé avec succès",
    formUpdated: "Formulaire mis à jour avec succès",
    formDeleted: "Formulaire supprimé avec succès",
    noForms: "Aucun formulaire personnalisé trouvé",
    noFields: "Aucun champ ajouté encore",
    selectCustomForm: "Sélectionner Formulaire Personnalisé",
    noCustomForm: "Aucun formulaire personnalisé",
    customFormCompleted: "Formulaire personnalisé complété",
    enterFieldName: "Entrer le nom du champ",
    enterPlaceholder: "Entrer le texte d'aide",
    enterOptions: "Entrer les options (séparées par des virgules)",
    moveUp: "Monter",
    moveDown: "Descendre",
    removeField: "Supprimer Champ",
    previewForm: "Aperçu du Formulaire",
    saveForm: "Enregistrer Formulaire",
    formBuilder: "Constructeur de Formulaire",
    dragToReorder: "Glisser pour réorganiser les champs",
  },

  invoices: {
    title: "Factures",
    description: "Générer et gérer les factures de réparation",
    generateInvoice: "Générer Facture",
    invoiceList: "Liste des Factures",
    invoice: "Facture",
    invoiceGenerator: "Générateur de Factures",
    selectRepairs: "Sélectionner les Réparations à Facturer",
    discountAmount: "Montant de Remise (TND)",
    subtotal: "Sous-total",
    discount: "Remise",
    total: "Total",
  },
};

export default translations;
