// Remove the Translation import
const translations = {
  app: {
    title: "Clinic Mobile",
    slogan: "مرحبا بك في Clinic Mobile",
  },
  common: {
    dashboard: "لوحة التحكم",
    newRepair: "إصلاح جديد",
    search: "بحث",
    stock: "المخزون ونقطة البيع",
    lottery: "القرعة",
    searchRepairs: "البحث عن الإصلاحات",
    viewDetails: "عرض التفاصيل",
    processing: "جاري المعالجة...",
    submit: "إرسال",
    update: "تحديث",
    delete: "حذف",
    cancel: "إلغاء",
    areYouSure: "هل أنت متأكد؟",
    deleteConfirmation: "هل أنت متأكد أنك تريد حذف هذا الإصلاح؟",
    back: "عودة إلى",
    loading: "تحميل...",
    print: "طباعة",
    download: "تحميل",
    balance: "الرصيد المتبقي",
    error: "خطأ!",
    pageNotFound: "عفواً! الصفحة غير موجودة",
    returnHome: "العودة إلى الصفحة الرئيسية",
    copied: "تم النسخ إلى الحافظة",
    call: "اتصال",
    startScanning: "بدأ المسح",
    stopScanning: "إيقاف المسح",
    scannerActive: "الماسح نشط وجاهز",
    scannerInactive: "الماسح غير نشط",
    logo: "الشعار",
    repairShop: "ورشة الإصلاح",
    tel: "هاتف",
    phone: "الهاتف",
    clear: "مسح",
    edit: "تعديل",
    actions: "الإجراءات",
    details: "التفاصيل",
    user: "المستخدم",
    date: "التاريخ",
    premium: "متقدم",
  },
  repair: {
    createNew: "إنشاء إصلاح جديد",
    customerInfo: "تفاصيل الزبون",
    customerName: "اسم الزبون",
    customerPhone: "رقم الهاتف  ",
    phoneModel: "موديل الجهاز",
    stat: "الحالة",
    problemDescription: "وصف المشكلة",
    observations: "ملاحظات",
    observationsOptional: "(اختياري)",
    addObservation: "إضافة ملاحظة",
    noObservations: "لا توجد ملاحظات حتى الآن",
    observationAdded: "تمت إضافة الملاحظة بنجاح",
    observationAddError: "فشل في إضافة الملاحظة",
    repairPrice: "سعر التصليح",
    initialPrice: "السعر الأولي",
    totalPrice: "السعر الإجمالي",
    paymentStatus: "حالة الدفع",
    downPayment: "دفعة أولى",
    priceModifications: "تعديلات السعر",
    priceModified: "تم تعديل السعر",
    addPriceModification: "إضافة تعديل السعر",
    priceModificationAdded: "تمت إضافة تعديل السعر بنجاح",
    priceModificationAddError: "فشل في إضافة تعديل السعر",
    amount: "المبلغ",
    reason: "السبب",
    submitRepair: "تسجيل طلب الإصلاح",
    successCreated: "تم إنشاء طلب الإصلاح بنجاح!",
    failedCreated: "فشل إنشاء طلب الإصلاح.",
    successUpdated: "تم تحديث طلب الإصلاح بنجاح!",
    notFound: "لم يتم العثور على طلب الإصلاح.",
    active: "نشط",
    completed: "مكتمل",
    returned: "مرتجع",
    all: "الكل",
    noActiveRepairs: "لا توجد إصلاحات نشطة",
    noCompletedRepairs: "لا توجد إصلاحات مكتملة",
    noReturnedRepairs: "لا توجد إصلاحات مرتجعة",
    noRepairsFound: "لا توجد إصلاحات",
    createFirstRepair: "إنشاء أول إصلاح لك",
    addedAgo: "أضيف منذ {{time}}",
    viewDetails: "عرض التفاصيل",
    scanQrCode: "امسح رمز الاستجابة السريعة",
    scanBarcode: "امسح الرمز الشريطي باستخدام الماسح الضوئي",
    barcodeSticker: "ملصق الرمز الشريطي",
    qrCode: "رمز الاستجابة السريعة",
    selectCodeType: "اختر نوع الرمز",
    selectCodeTypeDescription:
      "اختر نوع الرمز الذي سيتم تضمينه في التذكرة المطبوعة",
    repairDetails: "تفاصيل الإصلاح",
    repairTimeline: "الجدول الزمني للإصلاح",
    statusChanged: "تم تغيير الحالة إلى",
    updateStatus: "تحديث الحالة",
    deleteRepair: "حذف الإصلاح",
    deleteConfirmation: "هل أنت متأكد أنك تريد حذف هذا الإصلاح؟",
    enterDeleteCode: "أدخل رمز الحذف",
    invalidDeleteCode: "رمز الحذف غير صحيح. يرجى المحاولة مرة أخرى.",
    successDeleted: "تم حذف الإصلاح بنجاح!",
    thankYou: "شكرا لتعاملك معنا!",
    bringReceipt: "يرجى إحضار هذه الوثيقة عند استلام الجهاز.",
    responsibilityDisclaimer:
      "ملاحظة: نحن غير مسؤولين إذا فقدت هاتفك أو تعطل في فترة تتجاوز 30 يوماً",
    repairTicket: "تذكرة إصلاح",
    ticketNumber: "رقم التذكرة",
    repairNumber: "رقم الإصلاح",
    printTicket: "طباعة التذكرة",
    status: {
      pending: "قيد الانتظار",
      inProgress: "قيد التنفيذ",
      completed: "مكتمل",
      returned: "مرتجع",
      cancelled: "ملغي",
    },
    analytics: {
      title: "تحليلات الدفع",
      totalAmountPaid: "المبلغ الكلي المدفوع",
      averageAmountPaid: "المبلغ المتوسط المدفوع",
      monthlyRevenue: "الدخل الشهري",
    },
    payment: {
      paid: "مدفوع",
      partial: "مدفوع جزئيا",
      unpaid: "غير مدفوع",
    },
    addError: "حدث خطأ أثناء إضافة الإصلاح.",
    fetchError: "حدث خطأ أثناء جلب الإصلاحات.",
    updateError: "حدث خطأ أثناء تحديث الإصلاح.",
    deleteError: "حدث خطأ أثناء حذف الإصلاح.",
    importError: "حدث خطأ أثناء استيراد الإصلاحات.",
    noRepairFoundWithTicket:
      "لم يتم العثور على إصلاح بهذا الرقم. يرجى التحقق والمحاولة مرة أخرى.",
    noTicketNumberForBarcode: "لا يوجد رقم تذكرة متاح لإنشاء الرمز الشريطي.",
  },
  searchRepairs: {
    title: "البحث عن الإصلاحات",
    noResults: "لم يتم العثور على إصلاحات",
    adjustFilters: "حاول تعديل البحث أو المرشحات",
  },
  filters: {
    title: "الفلاتر",
    slogan: "تصفية النتائج",
    status: "الحالة",
    payment: "الدفع",
    clear: "مسح التصفية",
    anyStatus: "أي حالة",
    anyPaymentStatus: "أي حالة دفع",
    clearAll: "مسح جميع المرشحات",
    dateFrom: "من تاريخ",
    dateTo: "إلى تاريخ",
  },
  importExport: {
    import: "استيراد",
    export: "تصدير",
    chooseFile: "اختر ملف",
    importRepairs: "استيراد الإصلاحات",
    exportRepairs: "تصدير الإصلاحات",
    downloadTemplate: "تنزيل النموذج",
    importedSuccess: "تم استيراد الإصلاحات بنجاح!",
    exportedSuccess: "تم تصدير الإصلاحات بنجاح!",
    invalidFile: "تنسيق ملف غير صالح. يرجى استخدام ملف JSON صالح.",
    importConfirmation:
      "هل أنت متأكد أنك تريد استيراد هذه الإصلاحات؟ سيؤدي ذلك إلى استبدال أي بيانات موجودة.",
    successImport: "تم استيراد الإصلاحات بنجاح!",
    failedImport: "فشل استيراد الإصلاحات.",
    successExport: "تم تصدير الإصلاحات بنجاح!",
    failedExport: "فشل تصدير الإصلاحات.",
  },
  analytics: {
    title: "تحليلات الدفع",
    totalAmountPaid: "المبلغ الكلي المدفوع",
    averageAmountPaid: "المبلغ المتوسط المدفوع",
    monthlyRevenue: "الدخل الشهري",
  },
  repairAnalytics: {
    title: "إحصائيات الإصلاحات",
    totalRepairs: "إجمالي عدد الإصلاحات",
    completedRepairs: "الإصلاحات المكتملة",
    pendingRepairs: "الإصلاحات المعلقة",
    totalRevenue: "إجمالي الإيرادات",
    averageRepairPrice: "متوسط سعر الإصلاح",
  },
  auth: {
    login: "تسجيل الدخول",
    logout: "تسجيل الخروج",
    email: "البريد الإلكتروني",
    password: "كلمة المرور",
    loginToContinue: "تسجيل الدخول للمتابعة إلى حسابك",
    genericError: "حدث خطأ أثناء تسجيل الدخول",
    emailRequired: "البريد الإلكتروني مطلوب",
    passwordRequired: "كلمة المرور مطلوبة",
    invalidCredentials: "بريد إلكتروني أو كلمة مرور غير صالحة",
  },
  languageSwitcher: {
    language: "اللغة",
    english: "الإنجليزية",
    french: "الفرنسية",
    arabic: "العربية",
  },
  data: {
    export: "تصدير البيانات",
    import: "استيراد البيانات",
    successExport: "تم تصدير البيانات بنجاح",
    failedExport: "فشل في تصدير البيانات",
    successImport: "تم استيراد البيانات بنجاح",
    failedImport: "فشل في استيراد البيانات",
  },
  repairShop: {
    title: "ورشة الإصلاح",
    name: "اسم الورشة",
    address: "العنوان",
    phone: "رقم الهاتف",
    fetchError: "فشل في جلب معلومات ورشة الإصلاح",
  },
  scanner: {
    henexScanner: "الماسح المخصص",
    cameraScanner: "ماسح الكاميرا",
    barcode: "الرمز الشريطي",
    henexInstructions: "الماسح المخصص نشط. قم بمسح رمز شريطي أو رمز QR مباشرة",
    scannerInstructions: "اضغط على زر الماسح لمسح الرمز",
    scanPlaceholder: "مسح رمز...",
    scanSuccess: "تم مسح الرمز بنجاح!",
    scanError: "خطأ في مسح الرمز. يرجى المحاولة مرة أخرى.",
    autoScanEnabled: "المسح التلقائي مفعل",
    readyToScan: "جاهز للمسح",
    scanningActive: "الماسح نشط",
  },
  shortcuts: {
    title: "اختصارات لوحة المفاتيح",
    description: "استخدم اختصارات لوحة المفاتيح هذه للتنقل بسرعة",
    keyboard: "اختصارات",
    showShortcuts: "عرض اختصارات لوحة المفاتيح",
    tip: "اضغط على ? في أي وقت لعرض مربع المساعدة هذا",
    goToDashboard: "الذهاب إلى لوحة التحكم (Alt+H)",
    newRepair: "إنشاء إصلاح جديد (Alt+N)",
    printCurrentRepair: "طباعة الإصلاح الحالي (Alt+P)",
    help: "عرض اختصارات لوحة المفاتيح (?)",
    navigatedToDashboard: "تم الانتقال إلى لوحة التحكم",
    creatingNewRepair: "إنشاء إصلاح جديد",
    printing: "جاري الطباعة...",
  },
  theme: {
    light: "فاتح",
    dark: "داكن",
    system: "النظام",
    toggleDarkMode: "تبديل الوضع المظلم",
  },
  lottery: {
    title: "قرعة الفائز المحظوظ",
    selectDateRange: "اختر الفترة الزمنية",
    startDate: "تاريخ البداية",
    endDate: "تاريخ النهاية",
    runLottery: "تشغيل القرعة",
    spinning: "جاري الدوران...",
    winner: "الفائز!",
    congratulations: "تهانينا!",
    winningTicket: "التذكرة الفائزة",
    customer: "العميل",
    noRepairsInRange: "لم يتم العثور على إصلاحات مكتملة في الفترة المحددة",
    selectDatesFirst: "يرجى اختيار فترة زمنية أولاً",
    invalidDateRange: "يجب أن يكون تاريخ النهاية بعد تاريخ البداية",
    totalEntries: "إجمالي المشاركات (الإصلاحات المكتملة)",
    entries: "إصلاحات مكتملة",
    spinWheel: "أدر العجلة!",
    newLottery: "قرعة جديدة",
    ticketNumber: "تذكرة رقم",
    eligibilityNote: "فقط الإصلاحات المكتملة التي لها أرقام تذاكر مؤهلة للقرعة",
    soundOn: "الصوت مفعل",
    soundOff: "الصوت معطل",
  },
  dashboard: {
    title: "لوحة المعلومات",
    editLayout: "تعديل التخطيط",
    saveLayout: "حفظ التخطيط",
    reset: "إعادة تعيين",
    addWidget: "إضافة أداة",
    widgetAdded: "تمت إضافة الأداة بنجاح",
    widgetRemoved: "تمت إزالة الأداة",
    dashboardReset: "تمت إعادة تعيين لوحة المعلومات إلى الإعدادات الافتراضية",
    widgetType: "نوع الأداة",
    widgetTitle: "عنوان الأداة",
    selectWidgetType: "اختر نوع الأداة",
    addWidgetDescription: "اختر نوع الأداة وخصص عنوانها",
    totalRepairs: "إجمالي الإصلاحات",
    repairs: "إصلاحات",
    noRepairsFound: "لم يتم العثور على إصلاحات",
    unknownDevice: "جهاز غير معروف",
    goodMorning: "صباح الخير",
    goodAfternoon: "مساء الخير",
    goodEvening: "مساء الخير",
    user: "المستخدم",
    welcomeMessage:
      "مرحبًا بك في لوحة المعلومات المخصصة. هنا يمكنك رؤية نظرة عامة على ورشة الإصلاح الخاصة بك.",
    widgets: {
      welcome: {
        title: "مرحبًا",
        defaultTitle: "مرحبًا",
      },
      "repair-status": {
        title: "حالة الإصلاح",
        defaultTitle: "حالة الإصلاح",
      },
      "recent-repairs": {
        title: "الإصلاحات الأخيرة",
        defaultTitle: "الإصلاحات الأخيرة",
      },
      "repairs-by-status": {
        title: "الإصلاحات حسب الحالة",
        defaultTitle: "الإصلاحات حسب الحالة",
      },
      "repairs-by-device": {
        title: "الإصلاحات حسب الجهاز",
        defaultTitle: "الإصلاحات حسب الجهاز",
      },
      "payment-status": {
        title: "حالة الدفع",
        defaultTitle: "حالة الدفع",
      },
      income: {
        title: "الدخل",
        defaultTitle: "الدخل",
      },
    },
    paid: "مدفوع",
    unpaid: "غير مدفوع",
    partial: "مدفوع جزئيًا",
    noPaymentData: "لا توجد بيانات دفع متاحة",
    monthlyIncome: "الدخل الشهري",
    totalIncome: "إجمالي الدخل",
    income: "الدخل",
    increase: "زيادة",
    decrease: "انخفاض",
    allTime: "كل الوقت",
  },
  stock: {
    title: "إدارة المخزون ونقطة البيع",
    pos: "نقطة البيع",
    allCategories: "كل الفئات",
    products: "المنتجات",
    categories: "الفئات",
    sales: "المبيعات",
    inventory: "المخزون",
    reports: "التقارير",

    // Product Management
    addProduct: "إضافة منتج",
    editProduct: "تعديل منتج",
    deleteProduct: "حذف منتج",
    productName: "اسم المنتج",
    productDescription: "الوصف",
    sku: "رمز المنتج",
    barcode: "الباركود",
    category: "الفئة",
    price: "السعر",
    cost: "التكلفة",
    stock: "المخزون",
    minStock: "الحد الأدنى للمخزون",
    maxStock: "الحد الأقصى للمخزون",
    isActive: "نشط",
    image: "الصورة",

    // Categories
    addCategory: "إضافة فئة",
    editCategory: "تعديل فئة",
    categoryName: "اسم الفئة",
    categoryDescription: "وصف الفئة",

    // POS Interface
    cart: "السلة",
    addToCart: "إضافة إلى السلة",
    removeFromCart: "إزالة",
    clearCart: "إفراغ السلة",
    quantity: "الكمية",
    unitPrice: "سعر الوحدة",
    discount: "خصم",
    subtotal: "المجموع الفرعي",
    tax: "الضريبة",
    total: "المجموع",

    // Payment
    payment: "الدفع",
    paymentMethod: "طريقة الدفع",
    cash: "نقدي",
    card: "بطاقة",
    mobile: "دفع محمول",
    mixed: "دفع مختلط",
    amountPaid: "المبلغ المدفوع",
    change: "الباقي",
    processSale: "معالجة البيع",

    // Customer
    customer: "العميل",
    customerName: "اسم العميل",
    customerPhone: "هاتف العميل",

    // Sales
    saleNumber: "بيع #",
    saleDate: "تاريخ البيع",
    saleTotal: "إجمالي البيع",
    viewSale: "عرض البيع",

    // Inventory
    lowStock: "مخزون منخفض",
    outOfStock: "نفد من المخزون",
    stockLevel: "مستوى المخزون",
    stockMovement: "حركة المخزون",
    stockIn: "دخول مخزون",
    stockOut: "خروج مخزون",
    adjustment: "تعديل",

    // Messages
    productAdded: "تمت إضافة المنتج بنجاح",
    productUpdated: "تم تحديث المنتج بنجاح",
    productDeleted: "تم حذف المنتج بنجاح",
    categoryAdded: "تمت إضافة الفئة بنجاح",
    categoryUpdated: "تم تحديث الفئة بنجاح",
    categoryDeleted: "تم حذف الفئة بنجاح",
    saleCompleted: "تمت المبيعة بنجاح",
    insufficientStock: "مخزون غير كافي",
    invalidQuantity: "كمية غير صالحة",
    fetchError: "خطأ في جلب البيانات",
    addError: "خطأ في إضافة العنصر",
    updateError: "خطأ في تحديث العنصر",
    deleteError: "خطأ في حذف العنصر",
    saleError: "خطأ في معالجة البيع",
    stockAdjusted: "تم تعديل المخزون بنجاح",
    adjustError: "خطأ في تعديل المخزون",

    // Touch Interface
    touchMode: "وضع اللمس",
    gridView: "عرض الشبكة",
    listView: "عرض القائمة",
    searchProducts: "البحث عن المنتجات...",

    // Barcode Scanner
    scan: "مسح",
    scanBarcode: "مسح الرمز الشريطي",
    barcodeScanner: "ماسح الرمز الشريطي",
    manualEntry: "إدخال يدوي",
    cameraScanner: "ماسح الكاميرا",
    enterBarcode: "أدخل الرمز الشريطي",
    addToCart: "إضافة إلى السلة",
    productNotFound: "المنتج غير موجود",
    productInactive: "المنتج غير نشط",
    productOutOfStock: "المنتج نفد من المخزون",
    barcodeAdded: "تمت إضافة المنتج إلى السلة عبر الرمز الشريطي",
    scannerActive: "الماسح نشط - جاهز للمسح",
    scanning: "جاري المسح",
    scannerInstructions: "المسح التلقائي للرمز الشريطي نشط:",
    useBarcodeScannerDevice: "استخدم ماسح الرمز",
    typeBarcodeDirect: "اكتب الرمز مباشرة",
    automaticCartAdd: "إضافة تلقائية للسلة",

    // Sales Ticket & Printing
    salesTicket: "تذكرة البيع",
    printTicket: "طباعة التذكرة",
    print: "طباعة",
    saleNumber: "رقم البيع",
    date: "التاريخ",
    items: "العناصر",
    discount: "خصم",
    paid: "مدفوع",
    partial: "جزئي",
    pending: "معلق",
    thankYouVisit: "شكراً لزيارتكم!",
    keepTicket: "يرجى الاحتفاظ بهذه التذكرة",
    printInstructions1: "محسن للطابعات الحرارية 80 مم",
    printInstructions2: "تأكد من أن الطابعة متصلة وجاهزة",
    printInstructions3: "انقر طباعة لإنشاء الإيصال",

    // Sales History
    todaysSales: "مبيعات اليوم",
    todaysRevenue: "إيرادات اليوم",
    totalSales: "إجمالي المبيعات",
    noSalesYet: "لا توجد مبيعات بعد",
    walkInCustomer: "عميل عادي",
    saleId: "معرف البيع",
    totalValue: "القيمة الإجمالية",
    noLowStockItems: "لا توجد عناصر منخفضة المخزون",
    noOutOfStockItems: "لا توجد عناصر نفدت من المخزون",

    // Barcode Scanner
    cameraStarted: "تم تشغيل الكاميرا. وجه الكاميرا نحو الباركود للمسح.",
    cameraError: "لا يمكن الوصول إلى الكاميرا. يرجى استخدام الإدخال اليدوي.",
    manualEntry: "يدوي",
    cameraScanner: "كاميرا",
    enterBarcodeManually: "إدخال الباركود يدوياً",
    enterOrScanBarcode: "أدخل أو امسح الباركود...",
    pointCameraAtBarcode: "وجه الكاميرا نحو الباركود للمسح",
    orTypeBarcodeHere: "أو اكتب الباركود هنا...",
    cameraNotStarted: "لم يتم تشغيل الكاميرا",
    startCamera: "تشغيل الكاميرا",

    // Product Management
    showInactive: "إظهار غير النشط",
    hideInactive: "إخفاء غير النشط",
    inactive: "غير نشط",
    product: "المنتج",
    sku: "رمز المنتج",
    price: "السعر",
    stock: "المخزون",
    status: "الحالة",
    actions: "الإجراءات",
    tryAdjustingSearch: "حاول تعديل مصطلحات البحث",
    getStartedAddProduct: "ابدأ بإضافة منتجك الأول",

    // POS Interface Messages
    cartIsEmpty: "السلة فارغة",
    insufficientPayment: "مبلغ الدفع غير كافي",
    saleFailed: "فشلت العملية",
    unknownError: "خطأ غير معروف",
    saleProcessedSuccessfully: "تمت معالجة البيع بنجاح!",

    // Quick Setup
    userOrShopNotFound: "لم يتم العثور على المستخدم أو الورشة",
    errorCheckingSetup: "خطأ في فحص الإعداد",
    errorCreatingSampleData: "خطأ في إنشاء البيانات التجريبية",
    sampleDataCreatedSuccessfully: "تم إنشاء البيانات التجريبية بنجاح!",
    checking: "جاري الفحص...",
    checkSetup: "فحص الإعداد",
    createSampleData: "إنشاء بيانات تجريبية",

    // Product Form
    priceAndCostMustBePositive: "يجب أن يكون السعر والتكلفة أرقام موجبة",
    enterProductName: "أدخل اسم المنتج",
    enterSKU: "أدخل رمز المنتج",
    enterProductDescription: "أدخل وصف المنتج",
    enterBarcode: "أدخل الباركود",
    searchProducts: "البحث عن المنتجات...",

    // Stock Validation Messages
    productNoLongerExists: "المنتج لم يعد موجوداً",
    productNoLongerActive: "المنتج لم يعد نشطاً",
    insufficientStockDetailed:
      "مخزون غير كافي لـ {{productName}}. متوفر: {{available}}، مطلوب: {{required}}",

    // Advanced POS Features
    holdOrder: "تعليق الطلب",
    heldOrders: "الطلبات المعلقة",
    recallOrder: "استدعاء الطلب",
    deleteOrder: "حذف الطلب",
    orderHeld: "تم تعليق الطلب بنجاح",
    orderRecalled: "تم استدعاء الطلب بنجاح",
    orderDeleted: "تم حذف الطلب بنجاح",
    applyDiscount: "تطبيق خصم",
    orderDiscount: "خصم الطلب",
    itemDiscount: "خصم المنتج",
    discountType: "نوع الخصم",
    percentage: "نسبة مئوية",
    fixedAmount: "مبلغ ثابت",
    discountValue: "قيمة الخصم",
    discountApplied: "تم تطبيق الخصم بنجاح",
    notes: "ملاحظات",
    addNotes: "إضافة ملاحظات",
    orderNotes: "ملاحظات الطلب",
    noHeldOrders: "لا توجد طلبات معلقة",
    heldOn: "معلق في",
    orderTotal: "إجمالي الطلب",
    discountAmount: "مبلغ الخصم",
    cart: "السلة",
    customer: "العميل",
    customerName: "اسم العميل",
    customerPhone: "هاتف العميل",
    payment: "الدفع",
    subtotal: "المجموع الفرعي",
    tax: "الضريبة",
    total: "المجموع",
    paymentMethod: "طريقة الدفع",
    cash: "نقدي",
    card: "بطاقة",
    mobile: "محمول",
    amountPaid: "المبلغ المدفوع",
    change: "الباقي",
    processSale: "معالجة البيع",
    insufficientStock: "مخزون غير كافي",

    // Password Protection
    confirmProductCreation: "تأكيد إنشاء المنتج",
    confirmProductUpdate: "تأكيد تحديث المنتج",
    confirmProductDeletion: "تأكيد حذف المنتج",
    confirmCategoryCreation: "تأكيد إنشاء الفئة",
    confirmCategoryUpdate: "تأكيد تحديث الفئة",
    confirmCategoryDeletion: "تأكيد حذف الفئة",
    productCreationWarning:
      "هذا الإجراء سينشئ منتجاً جديداً في مخزونك. يرجى إدخال رمز الوصول للمتابعة.",
    productUpdateWarning:
      "هذا الإجراء سيعدل منتجاً موجوداً في مخزونك. يرجى إدخال رمز الوصول للمتابعة.",
    productDeletionWarning:
      "هذا الإجراء سيحذف المنتج نهائياً من مخزونك. لا يمكن التراجع عن هذا الإجراء. يرجى إدخال رمز الوصول للمتابعة.",
    categoryCreationWarning:
      "هذا الإجراء سينشئ فئة جديدة في مخزونك. يرجى إدخال رمز الوصول للمتابعة.",
    categoryUpdateWarning:
      "هذا الإجراء سيعدل فئة موجودة في مخزونك. يرجى إدخال رمز الوصول للمتابعة.",
    categoryDeletionWarning:
      "هذا الإجراء سيحذف الفئة نهائياً من مخزونك. لا يمكن التراجع عن هذا الإجراء. يرجى إدخال رمز الوصول للمتابعة.",
    updateProduct: "تحديث المنتج",
    updateCategory: "تحديث الفئة",
    deleteCategory: "حذف الفئة",
    cannotDeleteCategory:
      "لا يمكن حذف الفئة. تحتوي على {{count}} منتجات. يرجى نقل أو حذف المنتجات أولاً.",

    // Daily Summary
    dailySummary: "الملخص اليومي",
    totalRevenue: "إجمالي الإيرادات",
    transactions: "المعاملات",
    avgTransaction: "متوسط المعاملة",
    itemsSold: "العناصر المباعة",
    paymentBreakdown: "طرق الدفع",
    peakHour: "ساعة الذروة",
    printSummary: "طباعة الملخص",
    exportSummary: "تصدير الملخص",
    selectDateRange: "اختيار الفترة الزمنية",
    today: "اليوم",
    yesterday: "أمس",
    thisWeek: "هذا الأسبوع",
    thisMonth: "هذا الشهر",
    customRange: "فترة مخصصة",
    from: "من",
    to: "إلى",
    apply: "تطبيق",

    // Empty States
    emptyCart: {
      title: "سلتك فارغة",
      subtitle: "أضف منتجات للبدء",
      action: "تصفح المنتجات",
      tip: "امسح رمز شريطي أو انقر على المنتجات لإضافتها",
    },
    emptyProducts: {
      title: "لم يتم العثور على منتجات",
      subtitle: "حاول تعديل البحث أو مرشح الفئة",
      action: "مسح المرشحات",
      tip: "تأكد من إضافة المنتجات إلى مخزونك",
    },
  },
};

export default translations;
