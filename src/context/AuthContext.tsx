import React, { createContext, useContext, useState, useEffect } from "react";
import { Session, User } from "@supabase/supabase-js";
import { supabase } from "@/integrations/supabase/client";
import { useNavigate } from "react-router-dom";

export interface UserRole {
  id: string;
  user_id: string;
  repair_shop_id: string;
  role: "administrator" | "technician" | "receptionist" | "cashier";
}

interface AuthContextType {
  session: Session | null;
  user: User | null;
  loading: boolean;
  roles: UserRole[];
  hasPermission: (permission: string) => boolean;
  signIn: (
    email: string,
    password: string
  ) => Promise<{
    error: any | null;
    success: boolean;
  }>;
  signOut: () => Promise<void>;
  logUserAction: (action: string, details?: any) => Promise<void>;
  checkSessionHealth: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [roles, setRoles] = useState<UserRole[]>([]);
  const [loading, setLoading] = useState(true);
  const navigate = useNavigate();

  useEffect(() => {
    let isComponentMounted = true;

    // Set up auth state listener
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!isComponentMounted) return;

      console.log("Auth state change:", event, session?.user?.email);

      setSession(session);
      setUser(session?.user ?? null);

      if (event === "SIGNED_OUT") {
        navigate("/login");
        setRoles([]);
        setLoading(false);
      } else if (event === "TOKEN_REFRESHED") {
        console.log("Token refreshed successfully");
        // Session is already updated, just ensure roles are current
        if (session?.user) {
          await fetchUserRoles(session.user.id);
        }
      } else if (session?.user) {
        // Fetch user roles for new sessions
        await fetchUserRoles(session.user.id);
      } else {
        setLoading(false);
      }
    });

    // Check for existing session
    const initializeAuth = async () => {
      try {
        const {
          data: { session },
          error,
        } = await supabase.auth.getSession();

        if (error) {
          console.error("Failed to get session:", error);
          setLoading(false);
          return;
        }

        if (!isComponentMounted) return;

        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          await fetchUserRoles(session.user.id);
        } else {
          setLoading(false);
        }
      } catch (error) {
        console.error("Exception getting session:", error);
        if (isComponentMounted) {
          setLoading(false);
        }
      }
    };

    initializeAuth();

    // Handle visibility change to refresh session when tab becomes active
    const handleVisibilityChange = async () => {
      if (!document.hidden && isComponentMounted) {
        try {
          const {
            data: { session },
            error,
          } = await supabase.auth.getSession();
          if (!error && session && session.user) {
            // Only update if we don't already have a session or if the session changed
            if (!user || user.id !== session.user.id) {
              setSession(session);
              setUser(session.user);
              await fetchUserRoles(session.user.id);
            }
          } else if (!session && user) {
            // Session expired while tab was inactive
            console.log("Session expired, signing out");
            setSession(null);
            setUser(null);
            setRoles([]);
            navigate("/login");
          }
        } catch (error) {
          console.error(
            "Error refreshing session on visibility change:",
            error
          );
        }
      }
    };

    document.addEventListener("visibilitychange", handleVisibilityChange);

    return () => {
      isComponentMounted = false;
      subscription.unsubscribe();
      document.removeEventListener("visibilitychange", handleVisibilityChange);
    };
  }, [navigate]);

  const fetchUserRoles = async (userId: string, retryCount = 0) => {
    try {
      // Add timeout to prevent hanging
      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error("Timeout")), 5000)
      );

      const queryPromise = supabase
        .from("user_repair_shops")
        .select("*")
        .eq("user_id", userId);

      const { data, error } = (await Promise.race([
        queryPromise,
        timeoutPromise,
      ])) as any;

      if (!error && data) {
        setRoles(data as UserRole[]);
        console.log(`Fetched ${data.length} roles for user`);
      } else if (error) {
        console.error("Error fetching user roles:", error);

        // Retry once on network errors
        if (
          retryCount === 0 &&
          (error.message.includes("fetch") || error.message.includes("network"))
        ) {
          console.log("Retrying role fetch...");
          setTimeout(() => fetchUserRoles(userId, 1), 1000);
          return;
        }

        setRoles([]);
      }
    } catch (error) {
      console.error("Exception fetching user roles:", error);

      // Retry once on timeout or network errors
      if (retryCount === 0) {
        console.log("Retrying role fetch after exception...");
        setTimeout(() => fetchUserRoles(userId, 1), 1000);
        return;
      }

      setRoles([]);
    } finally {
      // Always ensure loading is set to false
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        return { error, success: false };
      }

      navigate("/");
      return { error: null, success: true };
    } catch (error) {
      return { error, success: false };
    }
  };

  const signOut = async () => {
    try {
      await logUserAction("logout");
    } catch (error) {
      console.error("Error logging user action:", error);
    }
    await supabase.auth.signOut();
  };

  // Log user actions for audit trail
  const logUserAction = async (action: string, details: any = {}) => {
    if (!user) return;

    try {
      // Check if the table exists first
      const { error: checkError } = await supabase
        .from("user_action_logs")
        .select("id")
        .limit(1)
        .maybeSingle();

      // Only try to insert if the table exists
      if (!checkError || !checkError.message.includes("does not exist")) {
        await supabase.from("user_action_logs").insert({
          user_id: user.id,
          action,
          details,
        });
      }
    } catch (error) {
      console.error("Failed to log user action:", error);
      // Continue execution even if logging fails
    }
  };

  // Check session health and refresh if needed
  const checkSessionHealth = async () => {
    try {
      const {
        data: { session },
        error,
      } = await supabase.auth.getSession();

      if (error) {
        console.error("Session health check failed:", error);
        return false;
      }

      if (!session && user) {
        // Session expired
        console.log("Session expired during health check");
        setSession(null);
        setUser(null);
        setRoles([]);
        navigate("/login");
        return false;
      }

      return !!session;
    } catch (error) {
      console.error("Exception during session health check:", error);
      return false;
    }
  };

  // Check if user has a specific permission based on their role
  const hasPermission = (permission: string): boolean => {
    if (!user || roles.length === 0) return false;

    // Define role-based permissions
    const rolePermissions: Record<string, string[]> = {
      administrator: [
        "manage_users",
        "manage_repairs",
        "manage_stock",
        "view_stock",
        "view_reports",
        "manage_settings",
        "process_payments",
        "track_time",
      ],
      technician: ["manage_repairs", "track_time", "view_stock"],
      receptionist: ["manage_repairs", "view_stock", "view_reports"],
      cashier: ["process_payments", "view_stock", "view_reports"],
    };

    // Check if any of the user's roles have the required permission
    return roles.some((role) => {
      const permissions = rolePermissions[role.role] || [];
      return permissions.includes(permission);
    });
  };

  // Set up periodic session health checks
  useEffect(() => {
    if (!user) return;

    const healthCheckInterval = setInterval(() => {
      checkSessionHealth();
    }, 5 * 60 * 1000); // Check every 5 minutes

    return () => clearInterval(healthCheckInterval);
  }, [user]);

  return (
    <AuthContext.Provider
      value={{
        session,
        user,
        roles,
        loading,
        signIn,
        signOut,
        hasPermission,
        logUserAction,
        checkSessionHealth,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
};
