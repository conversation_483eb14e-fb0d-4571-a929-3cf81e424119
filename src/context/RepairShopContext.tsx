import React, { createContext, useContext, useState, useEffect } from "react";
import { RepairShop, UserRepairShop } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

interface RepairShopContextType {
  repairShop: RepairShop | null;
  userRepairShop: UserRepairShop | null;
  loading: boolean;
  refreshRepairShop: () => Promise<void>;
}

const RepairShopContext = createContext<RepairShopContextType | undefined>(
  undefined
);

export const RepairShopProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [repairShop, setRepairShop] = useState<RepairShop | null>(null);
  const [userRepairShop, setUserRepairShop] = useState<UserRepairShop | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const { user } = useAuth();
  const { t } = useTranslation();

  // Fetch repair shop when user changes
  useEffect(() => {
    if (user) {
      refreshRepairShop();

      // Set up real-time subscription for repair shop changes
      const repairShopSubscription = supabase
        .channel("repair-shop-changes")
        .on(
          "postgres_changes",
          { event: "*", schema: "public", table: "repair_shops" },
          (payload) => {
            console.log("Repair shop update received:", payload);
            refreshRepairShop();
          }
        )
        .subscribe();

      // Set up real-time subscription for user-repair shop relationship changes
      const userRepairShopSubscription = supabase
        .channel("user-repair-shop-changes")
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "user_repair_shops",
            filter: `user_id=eq.${user.id}`,
          },
          (payload) => {
            console.log(
              "User-repair shop relationship update received:",
              payload
            );
            refreshRepairShop();
          }
        )
        .subscribe();

      // Clean up subscriptions when component unmounts or dependencies change
      return () => {
        supabase.removeChannel(repairShopSubscription);
        supabase.removeChannel(userRepairShopSubscription);
      };
    } else {
      setRepairShop(null);
      setUserRepairShop(null);
      setLoading(false);
    }
  }, [user]);

  const refreshRepairShop = async () => {
    if (!user) return;

    setLoading(true);
    try {
      // First, check if the user is already associated with the repair shop
      const { data: userData, error: userError } = await supabase
        .from("user_repair_shops")
        .select(
          `
          *,
          repair_shop:repair_shop_id(*)
        `
        )
        .eq("user_id", user.id)
        .maybeSingle();

      if (userError) {
        throw userError;
      }

      if (userData) {
        // User is already associated with a repair shop
        const processedUserRepairShop: UserRepairShop = {
          id: userData.id,
          userId: userData.user_id,
          repairShopId: userData.repair_shop_id,
          role: userData.role as "owner" | "admin" | "technician",
          createdAt: new Date(userData.created_at),
          repairShop: userData.repair_shop
            ? {
                id: userData.repair_shop.id,
                name: userData.repair_shop.name,
                address: userData.repair_shop.address,
                phone: userData.repair_shop.phone,
                createdAt: new Date(userData.repair_shop.created_at),
              }
            : undefined,
        };

        setUserRepairShop(processedUserRepairShop);
        setRepairShop(processedUserRepairShop.repairShop || null);
      } else {
        // User is not associated with a repair shop yet, get the default shop
        const { data: shopData, error: shopError } = await supabase
          .from("repair_shops")
          .select("*")
          .limit(1)
          .single();

        if (shopError && shopError.code !== "PGRST116") {
          // PGRST116 is the error code for no rows returned
          throw shopError;
        }

        if (shopData) {
          // Found a repair shop, associate the user with it
          const shop: RepairShop = {
            id: shopData.id,
            name: shopData.name,
            address: shopData.address,
            phone: shopData.phone,
            createdAt: new Date(shopData.created_at),
          };

          // Create the user-repair shop relationship
          const { data: relationData, error: relationError } = await supabase
            .from("user_repair_shops")
            .insert({
              user_id: user.id,
              repair_shop_id: shop.id,
              role: "technician", // Default role for new users
            })
            .select()
            .single();

          if (relationError) {
            throw relationError;
          }

          const newUserRepairShop: UserRepairShop = {
            id: relationData.id,
            userId: relationData.user_id,
            repairShopId: relationData.repair_shop_id,
            role: relationData.role as "owner" | "admin" | "technician",
            createdAt: new Date(relationData.created_at),
            repairShop: shop,
          };

          setUserRepairShop(newUserRepairShop);
          setRepairShop(shop);
        } else {
          // No repair shop exists yet, this should be handled by an admin
          console.warn("No repair shop found in the system");
          setRepairShop(null);
          setUserRepairShop(null);
        }
      }
    } catch (error) {
      console.error("Error fetching repair shop:", error);
      toast.error(t("repairShop.fetchError"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <RepairShopContext.Provider
      value={{
        repairShop,
        userRepairShop,
        loading,
        refreshRepairShop,
      }}
    >
      {children}
    </RepairShopContext.Provider>
  );
};

export const useRepairShopContext = () => {
  const context = useContext(RepairShopContext);
  if (context === undefined) {
    throw new Error(
      "useRepairShopContext must be used within a RepairShopProvider"
    );
  }
  return context;
};
