import React, { createContext, useContext, useState, useEffect } from "react";
import { RepairShop, UserRepairShop } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

interface RepairShopContextType {
  repairShop: RepairShop | null;
  userRepairShop: UserRepairShop | null;
  loading: boolean;
  refreshRepairShop: () => Promise<void>;
}

const RepairShopContext = createContext<RepairShopContextType | undefined>(
  undefined
);

export const RepairShopProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [repairShop, setRepairShop] = useState<RepairShop | null>(null);
  const [userRepairShop, setUserRepairShop] = useState<UserRepairShop | null>(
    null
  );
  const [loading, setLoading] = useState(true);
  const { user, loading: authLoading } = useAuth();
  const { t } = useTranslation();

  // Fetch repair shop when user changes
  useEffect(() => {
    // Don't do anything while auth is still loading
    if (authLoading) {
      return;
    }
    
    if (user) {
      // Try to load from localStorage first
      const cachedRepairShop = localStorage.getItem(`repairShop_${user.id}`);
      const cachedUserRepairShop = localStorage.getItem(`userRepairShop_${user.id}`);
      
      if (cachedRepairShop && cachedUserRepairShop) {
        try {
          const shop = JSON.parse(cachedRepairShop);
          const userShop = JSON.parse(cachedUserRepairShop);
          shop.createdAt = new Date(shop.createdAt);
          userShop.createdAt = new Date(userShop.createdAt);
          userShop.repairShop = shop;
          
          setRepairShop(shop);
          setUserRepairShop(userShop);
          setLoading(false);
          console.log("Loaded repair shop from cache");
          return;
        } catch (error) {
          console.error("Error parsing cached data:", error);
          localStorage.removeItem(`repairShop_${user.id}`);
          localStorage.removeItem(`userRepairShop_${user.id}`);
        }
      }
      
      refreshRepairShop();
    } else {
      // Only clear when auth is done loading and user is actually null (logged out)
      setRepairShop(null);
      setUserRepairShop(null);
      setLoading(false);
      // Clear cache on actual logout (not during auth loading)
      if (!authLoading) {
        Object.keys(localStorage).forEach(key => {
          if (key.startsWith('repairShop_') || key.startsWith('userRepairShop_')) {
            localStorage.removeItem(key);
          }
        });
      }
    }
  }, [user, authLoading]);

  const refreshRepairShop = async () => {
    if (!user) {
      setLoading(false);
      return;
    }

    setLoading(true);
    try {
      // Get user's repair shop association (simplified query)
      const { data: userShopData, error: userShopError } = await supabase
        .from("user_repair_shops")
        .select("*")
        .eq("user_id", user.id)
        .single();
        
      if (userShopError || !userShopData) {
        console.error("No repair shop association found:", userShopError);
        setLoading(false);
        return;
      }

      // Get the repair shop details separately
      const { data: shopData, error: shopError } = await supabase
        .from("repair_shops")
        .select("*")
        .single();
        
      if (shopError || !shopData) {
        console.error("Error fetching repair shop:", shopError);
        setLoading(false);
        return;
      }
      
      const shop: RepairShop = {
        id: shopData.id,
        name: shopData.name,
        address: shopData.address,
        phone: shopData.phone,
        createdAt: new Date(shopData.created_at),
      };

      const userShop: UserRepairShop = {
        id: userShopData.id,
        userId: userShopData.user_id,
        repairShopId: userShopData.repair_shop_id,
        role: userShopData.role as "administrator" | "technician" | "receptionist" | "cashier",
        createdAt: new Date(userShopData.created_at),
        repairShop: shop,
      };

      setRepairShop(shop);
      setUserRepairShop(userShop);
      
      // Cache the data
      localStorage.setItem(`repairShop_${user.id}`, JSON.stringify(shop));
      localStorage.setItem(`userRepairShop_${user.id}`, JSON.stringify({
        id: userShopData.id,
        userId: userShopData.user_id,
        repairShopId: userShopData.repair_shop_id,
        role: userShopData.role,
        createdAt: new Date(userShopData.created_at),
      }));
    } catch (error) {
      console.error("Error in refreshRepairShop:", error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <RepairShopContext.Provider
      value={{
        repairShop,
        userRepairShop,
        loading,
        refreshRepairShop,
      }}
    >
      {children}
    </RepairShopContext.Provider>
  );
};

export const useRepairShopContext = () => {
  const context = useContext(RepairShopContext);
  if (context === undefined) {
    throw new Error(
      "useRepairShopContext must be used within a RepairShopProvider"
    );
  }
  return context;
};
