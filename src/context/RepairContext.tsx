import React, {
  createContext,
  useContext,
  useState,
  useEffect,
  useCallback,
  useRef,
} from "react";
import {
  RepairItem,
  RepairFormData,
  Observation,
  PriceModification,
  StatusHistory,
} from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";
import {
  generateRepairCompletionEmailTemplate,
  sendEmail,
  isValidEmail,
} from "@/utils/emailService";

interface RepairContextType {
  repairs: RepairItem[];
  loading: boolean;
  hasMore: boolean;
  totalCount: number;
  addRepair: (data: RepairFormData) => Promise<RepairItem>;
  getRepairById: (
    id: string,
    forceRefresh?: boolean
  ) => Promise<RepairItem | undefined>;
  getRepairByTicketNumber: (
    ticketNumber: string,
    forceRefresh?: boolean
  ) => Promise<RepairItem | undefined>;
  updateRepair: (id: string, data: Partial<RepairItem>) => Promise<void>;
  searchRepairs: (query: string) => RepairItem[];
  deleteRepair: (id: string) => Promise<void>;
  importRepairs: (repairs: RepairItem[]) => Promise<void>;
  refreshRepairs: () => Promise<void>;
  loadMoreRepairs: () => Promise<void>;
}

const RepairContext = createContext<RepairContextType | undefined>(undefined);

export const RepairProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [repairs, setRepairs] = useState<RepairItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);
  const [currentPage, setCurrentPage] = useState(0);
  const { user } = useAuth();
  const { repairShop } = useRepairShopContext();
  const { t } = useTranslation();

  // Pagination configuration
  const PAGE_SIZE = 500; // Load more repairs initially (increased from 100)
  const ENABLE_PAGINATION = false; // Disable pagination to ensure all repairs are accessible

  // Note: Set ENABLE_PAGINATION = true when you have 1000+ repairs and want pagination
  // For most repair shops, loading all repairs at once provides better UX

  // Debounce timer ref to prevent excessive refreshes
  const debounceTimerRef = useRef<NodeJS.Timeout | null>(null);

  const refreshRepairs = useCallback(async () => {
    if (!user || !repairShop) return;

    setLoading(true);
    const startTime = performance.now();
    try {
      console.log("Fetching repairs from database...");

      // Query with optional pagination
      let query = supabase
        .from("repairs")
        .select("*, repair_shop:repair_shop_id(*)")
        .eq("repair_shop_id", repairShop.id)
        .order("created_at", { ascending: false });

      // Only apply pagination if enabled
      if (ENABLE_PAGINATION) {
        query = query.range(0, PAGE_SIZE - 1);
      }

      const { data, error } = await query;

      if (error) {
        console.error("Database error:", error);
        throw error;
      }

      console.log("Raw data from database:", data?.length || 0, "repairs");

      // Convert string dates to Date objects and ensure correct types
      const processedRepairs = data.map((repair: any) => ({
        id: repair.id,
        customerName: repair.customer_name,
        customerPhone: repair.customer_phone,
        customerEmail: (repair as any).customer_email || undefined,
        phoneModel: repair.phone_model,
        problemDescription: repair.problem_description,
        repairPrice: repair.repair_price,
        paymentStatus: repair.payment_status as "paid" | "partial" | "unpaid",
        downPayment: repair.down_payment,
        createdAt: new Date(repair.created_at),
        completedAt: repair.completed_at
          ? new Date(repair.completed_at)
          : undefined,
        status: repair.status as
          | "pending"
          | "inProgress"
          | "completed"
          | "cancelled"
          | "returned",
        userId: repair.user_id,
        repairShopId: repair.repair_shop_id,
        ticketNumber: repair.ticket_number,
        repairShop: repair.repair_shop
          ? {
              id: repair.repair_shop.id,
              name: repair.repair_shop.name,
              address: repair.repair_shop.address,
              phone: repair.repair_shop.phone,
              createdAt: new Date(repair.repair_shop.created_at),
            }
          : undefined,
        // Safe JSON parsing that handles both string and object types
        observations: (() => {
          try {
            if (repair.observations) {
              // If it's already an object, use it directly
              if (typeof repair.observations === "object") {
                return repair.observations.map((obs: any) => ({
                  id: obs.id,
                  text: obs.text,
                  createdAt: new Date(obs.created_at),
                  userId: obs.user_id,
                }));
              }
              // If it's a string, parse it
              if (
                typeof repair.observations === "string" &&
                repair.observations.trim() !== ""
              ) {
                return JSON.parse(repair.observations).map((obs: any) => ({
                  id: obs.id,
                  text: obs.text,
                  createdAt: new Date(obs.created_at),
                  userId: obs.user_id,
                }));
              }
            }
          } catch (error) {
            console.error("Error parsing observations JSON:", error);
          }
          return [];
        })(),
        // Safe price modifications parsing
        priceModifications: (() => {
          try {
            if (repair.price_modifications) {
              // If it's already an object, use it directly
              if (typeof repair.price_modifications === "object") {
                return repair.price_modifications.map((mod: any) => ({
                  id: mod.id,
                  amount: mod.amount,
                  reason: mod.reason,
                  createdAt: new Date(mod.created_at),
                  userId: mod.user_id,
                }));
              }
              // If it's a string, parse it
              if (
                typeof repair.price_modifications === "string" &&
                repair.price_modifications.trim() !== ""
              ) {
                return JSON.parse(repair.price_modifications).map(
                  (mod: any) => ({
                    id: mod.id,
                    amount: mod.amount,
                    reason: mod.reason,
                    createdAt: new Date(mod.created_at),
                    userId: mod.user_id,
                  })
                );
              }
            }
          } catch (error) {
            console.error("Error parsing price modifications JSON:", error);
          }
          return [];
        })(),
      }));

      setRepairs(processedRepairs);
      setCurrentPage(0);

      // Set pagination state based on whether pagination is enabled
      let totalRepairCount = 0;
      if (ENABLE_PAGINATION) {
        setHasMore(processedRepairs.length === PAGE_SIZE);
        // Get total count for pagination info
        const { count } = await supabase
          .from("repairs")
          .select("*", { count: "exact", head: true })
          .eq("repair_shop_id", repairShop.id);
        totalRepairCount = count || 0;
        setTotalCount(totalRepairCount);
      } else {
        // No pagination - we have all repairs
        setHasMore(false);
        totalRepairCount = processedRepairs.length;
        setTotalCount(totalRepairCount);
      }

      const endTime = performance.now();
      console.log(
        `Repairs loaded in ${(endTime - startTime).toFixed(2)}ms (${
          processedRepairs.length
        }/${totalRepairCount} repairs)`
      );
    } catch (error) {
      console.error("Error fetching repairs:", error);
      toast.error(t("repair.fetchError"));
    } finally {
      setLoading(false);
    }
  }, [user, repairShop, t]);

  // Load more repairs for pagination
  const loadMoreRepairs = useCallback(async () => {
    if (!user || !repairShop || !hasMore || loading || !ENABLE_PAGINATION)
      return;

    try {
      const nextPage = currentPage + 1;
      const startRange = nextPage * PAGE_SIZE;
      const endRange = startRange + PAGE_SIZE - 1;

      console.log(
        `Loading more repairs: page ${nextPage}, range ${startRange}-${endRange}`
      );

      const { data, error } = await supabase
        .from("repairs")
        .select("*, repair_shop:repair_shop_id(*)")
        .eq("repair_shop_id", repairShop.id)
        .order("created_at", { ascending: false })
        .range(startRange, endRange);

      if (error) throw error;

      if (data && data.length > 0) {
        // Process new repairs same way as initial load
        const processedRepairs = data.map((repair: any) => ({
          id: repair.id,
          customerName: repair.customer_name,
          customerPhone: repair.customer_phone,
          customerEmail: repair.customer_email || undefined,
          phoneModel: repair.phone_model,
          problemDescription: repair.problem_description,
          repairPrice: repair.repair_price,
          paymentStatus: repair.payment_status as "paid" | "partial" | "unpaid",
          downPayment: repair.down_payment,
          createdAt: new Date(repair.created_at),
          completedAt: repair.completed_at
            ? new Date(repair.completed_at)
            : undefined,
          status: repair.status as
            | "pending"
            | "inProgress"
            | "completed"
            | "cancelled"
            | "returned",
          userId: repair.user_id,
          repairShopId: repair.repair_shop_id,
          ticketNumber: repair.ticket_number,
          repairShop: repair.repair_shop
            ? {
                id: repair.repair_shop.id,
                name: repair.repair_shop.name,
                address: repair.repair_shop.address,
                phone: repair.repair_shop.phone,
                createdAt: new Date(repair.repair_shop.created_at),
              }
            : undefined,
          // Use same safe JSON parsing as main function
          observations: (() => {
            try {
              if (repair.observations) {
                if (typeof repair.observations === "object") {
                  return repair.observations.map((obs: any) => ({
                    id: obs.id,
                    text: obs.text,
                    createdAt: new Date(obs.created_at),
                    userId: obs.user_id,
                  }));
                }
                if (
                  typeof repair.observations === "string" &&
                  repair.observations.trim() !== ""
                ) {
                  return JSON.parse(repair.observations).map((obs: any) => ({
                    id: obs.id,
                    text: obs.text,
                    createdAt: new Date(obs.created_at),
                    userId: obs.user_id,
                  }));
                }
              }
            } catch (error) {
              console.error("Error parsing observations JSON:", error);
            }
            return [];
          })(),
          priceModifications: (() => {
            try {
              if (repair.price_modifications) {
                if (typeof repair.price_modifications === "object") {
                  return repair.price_modifications.map((mod: any) => ({
                    id: mod.id,
                    amount: mod.amount,
                    reason: mod.reason,
                    createdAt: new Date(mod.created_at),
                    userId: mod.user_id,
                  }));
                }
                if (
                  typeof repair.price_modifications === "string" &&
                  repair.price_modifications.trim() !== ""
                ) {
                  return JSON.parse(repair.price_modifications).map(
                    (mod: any) => ({
                      id: mod.id,
                      amount: mod.amount,
                      reason: mod.reason,
                      createdAt: new Date(mod.created_at),
                      userId: mod.user_id,
                    })
                  );
                }
              }
            } catch (error) {
              console.error("Error parsing price modifications JSON:", error);
            }
            return [];
          })(),
        }));

        // Append to existing repairs
        setRepairs((prev) => [...prev, ...processedRepairs]);
        setCurrentPage(nextPage);
        setHasMore(data.length === PAGE_SIZE);

        console.log(
          `Loaded ${data.length} more repairs (total: ${
            repairs.length + data.length
          })`
        );
      } else {
        setHasMore(false);
      }
    } catch (error) {
      console.error("Error loading more repairs:", error);
      toast.error(t("repair.loadMoreError"));
    }
  }, [user, repairShop, hasMore, loading, currentPage, repairs.length, t]);

  // Debounced refresh function to prevent excessive API calls
  const debouncedRefreshRepairs = useCallback(() => {
    // Clear existing timer
    if (debounceTimerRef.current) {
      clearTimeout(debounceTimerRef.current);
    }

    // Set new timer
    debounceTimerRef.current = setTimeout(() => {
      refreshRepairs();
    }, 100); // Reduced to 100ms for better responsiveness
  }, [refreshRepairs]);

  // Cleanup debounce timer on unmount
  useEffect(() => {
    return () => {
      if (debounceTimerRef.current) {
        clearTimeout(debounceTimerRef.current);
      }
    };
  }, []);

  // Fetch repairs when user or repair shop changes
  useEffect(() => {
    if (user && repairShop) {
      console.log("Starting initial repair data load...");
      refreshRepairs();

      // Set up real-time subscription for repairs
      const repairsSubscription = supabase
        .channel("repairs-changes")
        .on(
          "postgres_changes",
          {
            event: "*",
            schema: "public",
            table: "repairs",
            filter: `repair_shop_id=eq.${repairShop.id}`,
          },
          (payload) => {
            // Use debounced refresh to prevent excessive API calls
            debouncedRefreshRepairs();
          }
        )
        .subscribe();

      // Clean up subscription when component unmounts or dependencies change
      return () => {
        supabase.removeChannel(repairsSubscription);
      };
    } else {
      setRepairs([]);
      setLoading(false);
    }
  }, [user, repairShop, debouncedRefreshRepairs]);

  const addRepair = async (data: RepairFormData): Promise<RepairItem> => {
    if (!user) throw new Error("User must be logged in");
    if (!repairShop) throw new Error("No repair shop available");

    try {
      const newRepair: any = {
        customer_name: data.customerName,
        customer_phone: data.customerPhone,
        phone_model: data.phoneModel,
        problem_description: data.problemDescription,
        repair_price: data.repairPrice,
        payment_status: data.paymentStatus,
        down_payment: data.downPayment,
        status: "pending" as const,
        user_id: user.id,
        repair_shop_id: repairShop.id,
      };

      // Only add customer_email if it's provided (for backward compatibility)
      if (data.customerEmail) {
        newRepair.customer_email = data.customerEmail;
      }

      // Add other required fields
      newRepair.observations = (() => {
        try {
          if (data.observations && data.observations.length > 0) {
            return JSON.stringify(
              data.observations.map((text) => ({
                id: crypto.randomUUID(),
                text,
                created_at: new Date().toISOString(),
                user_id: user.id,
              }))
            );
          }
        } catch (error) {
          console.error("Error stringifying observations during add:", error);
        }
        return "[]";
      })();

      newRepair.price_modifications = "[]"; // Initialize with empty array

      // Add status_history
      try {
        newRepair.status_history = JSON.stringify([
          {
            id: crypto.randomUUID(),
            status: "pending",
            created_at: new Date().toISOString(),
            user_id: user.id,
          },
        ]);
      } catch (error) {
        console.log("Status history column might not exist yet, skipping");
      }

      const { data: insertedData, error } = await supabase
        .from("repairs")
        .insert(newRepair)
        .select("*, repair_shop:repair_shop_id(*)")
        .single();

      if (error) {
        throw error;
      }

      // Convert to our app's RepairItem format
      const processedRepair: RepairItem = {
        id: insertedData.id,
        customerName: insertedData.customer_name,
        customerPhone: insertedData.customer_phone,
        customerEmail: insertedData.customer_email || undefined,
        phoneModel: insertedData.phone_model,
        problemDescription: insertedData.problem_description,
        repairPrice: insertedData.repair_price,
        paymentStatus: insertedData.payment_status as
          | "paid"
          | "partial"
          | "unpaid",
        downPayment: insertedData.down_payment,
        createdAt: new Date(insertedData.created_at),
        completedAt: insertedData.completed_at
          ? new Date(insertedData.completed_at)
          : undefined,
        status: insertedData.status as
          | "pending"
          | "inProgress"
          | "completed"
          | "cancelled"
          | "returned",
        userId: insertedData.user_id,
        repairShopId: insertedData.repair_shop_id,
        ticketNumber: insertedData.ticket_number,
        repairShop: insertedData.repair_shop
          ? {
              id: insertedData.repair_shop.id,
              name: insertedData.repair_shop.name,
              address: insertedData.repair_shop.address,
              phone: insertedData.repair_shop.phone,
              createdAt: new Date(insertedData.repair_shop.created_at),
            }
          : undefined,
        observations: (() => {
          try {
            if (
              insertedData.observations &&
              insertedData.observations.trim() !== ""
            ) {
              return JSON.parse(insertedData.observations).map((obs: any) => ({
                id: obs.id,
                text: obs.text,
                createdAt: new Date(obs.created_at),
                userId: obs.user_id,
              }));
            }
          } catch (error) {
            console.error("Error parsing observations JSON:", error);
          }
          return [];
        })(),
        statusHistory: (() => {
          try {
            if (
              insertedData.status_history &&
              insertedData.status_history.trim() !== ""
            ) {
              return JSON.parse(insertedData.status_history).map(
                (entry: any) => ({
                  id: entry.id,
                  status: entry.status,
                  createdAt: new Date(entry.created_at),
                  userId: entry.user_id,
                })
              );
            }
          } catch (error) {
            console.error("Error parsing status history JSON:", error);
          }
          return [];
        })(),
      };

      setRepairs([processedRepair, ...repairs]);
      return processedRepair;
    } catch (error) {
      console.error("Error adding repair:", error);
      toast.error(t("repair.addError"));
      throw error;
    }
  };

  const getRepairById = useCallback(
    async (
      id: string,
      forceRefresh = false
    ): Promise<RepairItem | undefined> => {
      if (!user) return undefined;

      // First, try to get from cached repairs unless forced refresh
      if (!forceRefresh) {
        const cachedRepair = repairs.find((repair) => repair.id === id);
        if (cachedRepair) {
          console.log("Using cached repair data for ID:", id);
          return cachedRepair;
        }
      }

      // If not cached or forced refresh, fetch from Supabase
      console.log("Fetching repair from database for ID:", id);
      try {
        // Optimized query: Only select needed fields, no joins
        const { data, error } = await supabase
          .from("repairs")
          .select(
            `
            id,
            customer_name,
            customer_phone,
            customer_email,
            phone_model,
            problem_description,
            repair_price,
            payment_status,
            down_payment,
            status,
            ticket_number,
            created_at,
            completed_at,
            user_id,
            repair_shop_id,
            observations,
            status_history,
            price_modifications
          `
          )
          .eq("id", id)
          .single();

        if (error) {
          throw error;
        }

        if (!data) {
          return undefined;
        }

        // Convert to our app's RepairItem format
        return {
          id: data.id,
          customerName: data.customer_name,
          customerPhone: data.customer_phone,
          customerEmail: (data as any).customer_email || undefined,
          phoneModel: data.phone_model,
          problemDescription: data.problem_description,
          repairPrice: data.repair_price,
          paymentStatus: data.payment_status as "paid" | "partial" | "unpaid",
          downPayment: data.down_payment,
          createdAt: new Date(data.created_at),
          completedAt: data.completed_at
            ? new Date(data.completed_at)
            : undefined,
          status: data.status as
            | "pending"
            | "inProgress"
            | "completed"
            | "cancelled"
            | "returned",
          userId: data.user_id,
          repairShopId: data.repair_shop_id,
          ticketNumber: data.ticket_number,
          repairShop: data.repair_shop
            ? {
                id: data.repair_shop.id,
                name: data.repair_shop.name,
                address: data.repair_shop.address,
                phone: data.repair_shop.phone,
                createdAt: new Date(data.repair_shop.created_at),
              }
            : undefined,
          observations: (() => {
            try {
              if (data.observations && data.observations.trim() !== "") {
                return JSON.parse(data.observations).map((obs: any) => ({
                  id: obs.id,
                  text: obs.text,
                  createdAt: new Date(obs.created_at),
                  userId: obs.user_id,
                }));
              }
            } catch (error) {
              console.error("Error parsing observations JSON:", error);
            }
            return [];
          })(),
          priceModifications: (() => {
            try {
              if (
                data.price_modifications &&
                data.price_modifications.trim() !== ""
              ) {
                return JSON.parse(data.price_modifications).map((mod: any) => ({
                  id: mod.id,
                  amount: mod.amount,
                  reason: mod.reason,
                  createdAt: new Date(mod.created_at),
                  userId: mod.user_id,
                }));
              }
            } catch (error) {
              console.error("Error parsing price modifications JSON:", error);
            }
            return [];
          })(),
          statusHistory: (() => {
            try {
              if (data.status_history && data.status_history.trim() !== "") {
                return JSON.parse(data.status_history).map((entry: any) => ({
                  id: entry.id,
                  status: entry.status,
                  createdAt: new Date(entry.created_at),
                  userId: entry.user_id,
                }));
              }
            } catch (error) {
              console.error("Error parsing status history JSON:", error);
            }
            return [];
          })(),
        };
      } catch (error) {
        console.error("Error fetching repair:", error);
        return undefined;
      }
    },
    [user, repairs]
  );

  const getRepairByTicketNumber = useCallback(
    async (
      ticketNumber: string,
      forceRefresh = false
    ): Promise<RepairItem | undefined> => {
      if (!user || !repairShop) return undefined;

      // For all numeric inputs, we simply remove leading zeros to get the actual ticket number
      const cleanTicketNumber = ticketNumber.replace(/^0+/, "");

      console.log("Original ticket number for lookup:", ticketNumber);
      console.log("Cleaned ticket number:", cleanTicketNumber);

      // Also keep the original ticket number for fallback
      const originalTicketNumber = ticketNumber;

      // First, try to find in cached repairs unless forced refresh
      if (!forceRefresh) {
        const cachedRepair = repairs.find(
          (repair) =>
            repair.ticketNumber === cleanTicketNumber ||
            repair.ticketNumber === originalTicketNumber ||
            repair.ticketNumber?.toString().includes(cleanTicketNumber)
        );
        if (cachedRepair) {
          console.log("Using cached repair data for ticket:", ticketNumber);
          return cachedRepair;
        }
      }

      // If not cached or forced refresh, fetch from Supabase
      console.log("Fetching repair from database for ticket:", ticketNumber);
      try {
        console.log(
          "Searching for repair with cleaned ticket number:",
          cleanTicketNumber
        );

        // Try with the cleaned ticket number (most likely to match)
        let { data, error } = await supabase
          .from("repairs")
          .select("*, repair_shop:repair_shop_id(*)")
          .eq("repair_shop_id", repairShop.id)
          .eq("ticket_number", cleanTicketNumber)
          .single();

        // If no match with cleaned number, try with the original ticket number
        if ((!data || error) && cleanTicketNumber !== originalTicketNumber) {
          console.log(
            "No match with cleaned number, trying original:",
            originalTicketNumber
          );
          const result = await supabase
            .from("repairs")
            .select("*, repair_shop:repair_shop_id(*)")
            .eq("repair_shop_id", repairShop.id)
            .eq("ticket_number", originalTicketNumber)
            .single();

          data = result.data;
          error = result.error;
        }

        // If still no match, try a broader search for any repair with this ticket number
        if (!data || error) {
          console.log(
            "No exact match found, trying broader search for ticket number containing:",
            cleanTicketNumber
          );

          // Get all repairs and filter locally
          const { data: allRepairs, error: allRepairsError } = await supabase
            .from("repairs")
            .select("*, repair_shop:repair_shop_id(*)")
            .eq("repair_shop_id", repairShop.id);

          if (!allRepairsError && allRepairs && allRepairs.length > 0) {
            // Try to find a repair where the ticket number contains our cleaned number
            const matchingRepair = allRepairs.find(
              (repair) =>
                repair.ticket_number &&
                repair.ticket_number.toString().includes(cleanTicketNumber)
            );

            if (matchingRepair) {
              console.log(
                "Found repair with partial ticket number match:",
                matchingRepair.ticket_number
              );
              data = matchingRepair;
              error = null;
            }
          }
        }

        if (error && error.code !== "PGRST116") {
          // PGRST116 is "no rows returned" which is expected if no match
          throw error;
        }

        if (!data) {
          console.log("No repair found with ticket number:", cleanTicketNumber);
          return undefined;
        }

        // Convert to our app's RepairItem format
        return {
          id: data.id,
          customerName: data.customer_name,
          customerPhone: data.customer_phone,
          customerEmail: (data as any).customer_email || undefined,
          phoneModel: data.phone_model,
          problemDescription: data.problem_description,
          repairPrice: data.repair_price,
          paymentStatus: data.payment_status as "paid" | "partial" | "unpaid",
          downPayment: data.down_payment,
          createdAt: new Date(data.created_at),
          completedAt: data.completed_at
            ? new Date(data.completed_at)
            : undefined,
          status: data.status as
            | "pending"
            | "inProgress"
            | "completed"
            | "cancelled"
            | "returned",
          userId: data.user_id,
          repairShopId: data.repair_shop_id,
          ticketNumber: data.ticket_number,
          repairShop: data.repair_shop
            ? {
                id: data.repair_shop.id,
                name: data.repair_shop.name,
                address: data.repair_shop.address,
                phone: data.repair_shop.phone,
                createdAt: new Date(data.repair_shop.created_at),
              }
            : undefined,
          observations: (() => {
            try {
              if (data.observations && data.observations.trim() !== "") {
                return JSON.parse(data.observations).map((obs: any) => ({
                  id: obs.id,
                  text: obs.text,
                  createdAt: new Date(obs.created_at),
                  userId: obs.user_id,
                }));
              }
            } catch (error) {
              console.error("Error parsing observations JSON:", error);
            }
            return [];
          })(),
          priceModifications: (() => {
            try {
              if (
                data.price_modifications &&
                data.price_modifications.trim() !== ""
              ) {
                return JSON.parse(data.price_modifications).map((mod: any) => ({
                  id: mod.id,
                  amount: mod.amount,
                  reason: mod.reason,
                  createdAt: new Date(mod.created_at),
                  userId: mod.user_id,
                }));
              }
            } catch (error) {
              console.error("Error parsing price modifications JSON:", error);
            }
            return [];
          })(),
          statusHistory: (() => {
            try {
              if (data.status_history && data.status_history.trim() !== "") {
                return JSON.parse(data.status_history).map((entry: any) => ({
                  id: entry.id,
                  status: entry.status,
                  createdAt: new Date(entry.created_at),
                  userId: entry.user_id,
                }));
              }
            } catch (error) {
              console.error("Error parsing status history JSON:", error);
            }
            return [];
          })(),
        };
      } catch (error) {
        console.error("Error fetching repair by ticket number:", error);
        return undefined;
      }
    },
    [user, repairShop, repairs]
  );

  const updateRepair = async (
    id: string,
    data: Partial<RepairItem>
  ): Promise<void> => {
    if (!user) return;

    try {
      // Convert from app's RepairItem format to database format
      const dbData: any = {};

      if (data.customerName !== undefined)
        dbData.customer_name = data.customerName;
      if (data.customerPhone !== undefined)
        dbData.customer_phone = data.customerPhone;
      if (data.customerEmail !== undefined) {
        (dbData as any).customer_email = data.customerEmail || null;
      }
      if (data.phoneModel !== undefined) dbData.phone_model = data.phoneModel;
      if (data.problemDescription !== undefined)
        dbData.problem_description = data.problemDescription;
      if (data.repairPrice !== undefined)
        dbData.repair_price = data.repairPrice;
      if (data.paymentStatus !== undefined)
        dbData.payment_status = data.paymentStatus;
      if (data.downPayment !== undefined)
        dbData.down_payment = data.downPayment;
      if (data.status !== undefined) dbData.status = data.status;
      if (data.completedAt !== undefined)
        dbData.completed_at = data.completedAt;
      // Handle status history updates
      if (data.statusHistory !== undefined) {
        // If we're adding new status history entries
        if (Array.isArray(data.statusHistory)) {
          try {
            // Get the current repair to merge status history
            const currentRepair = await getRepairById(id);
            if (currentRepair) {
              const currentStatusHistory = currentRepair.statusHistory || [];

              // Combine current and new status history
              const allStatusHistory = [
                ...currentStatusHistory,
                ...data.statusHistory,
              ];

              // Convert to database format
              try {
                dbData.status_history = JSON.stringify(
                  allStatusHistory.map((entry) => ({
                    id: entry.id,
                    status: entry.status,
                    created_at: entry.createdAt.toISOString(),
                    user_id: entry.userId,
                  }))
                );
              } catch (error) {
                console.error("Error stringifying status history:", error);
                dbData.status_history = "[]";
              }
            }
          } catch (error) {
            console.log(
              "Status history column might not exist yet, skipping update"
            );
            // Remove status_history from dbData to avoid errors
            delete dbData.status_history;
          }
        }
      }

      if (data.observations !== undefined) {
        // If we're adding new observations
        if (Array.isArray(data.observations)) {
          // Get the current repair to merge observations
          const currentRepair = await getRepairById(id);
          if (currentRepair) {
            const currentObservations = currentRepair.observations || [];

            // Create new observation objects
            const newObservations = data.observations.map((obs) => {
              // If it's a string, it's a new observation
              if (typeof obs === "string") {
                return {
                  id: crypto.randomUUID(),
                  text: obs,
                  createdAt: new Date(),
                  userId: user.id,
                };
              }
              // If it's already an Observation object, keep it as is
              return obs;
            });

            // Combine current and new observations
            const allObservations = [
              ...currentObservations,
              ...newObservations,
            ];

            // Convert to database format
            try {
              dbData.observations = JSON.stringify(
                allObservations.map((obs) => ({
                  id: obs.id,
                  text: obs.text,
                  created_at: obs.createdAt.toISOString(),
                  user_id: obs.userId,
                }))
              );
            } catch (error) {
              console.error("Error stringifying observations:", error);
              dbData.observations = "[]";
            }
          }
        }
      }

      if (data.priceModifications !== undefined) {
        // If we're adding new price modifications
        if (Array.isArray(data.priceModifications)) {
          // Get the current repair to merge price modifications
          const currentRepair = await getRepairById(id);
          if (currentRepair) {
            const currentPriceModifications =
              currentRepair.priceModifications || [];

            // Create new price modification objects
            const newPriceModifications = data.priceModifications.map((mod) => {
              // If it has amount and reason properties, it's a new price modification
              if (
                typeof mod === "object" &&
                "amount" in mod &&
                "reason" in mod
              ) {
                return {
                  id: crypto.randomUUID(),
                  amount: mod.amount,
                  reason: mod.reason,
                  createdAt: new Date(),
                  userId: user.id,
                };
              }
              // If it's already a PriceModification object, keep it as is
              return mod;
            });

            // Combine current and new price modifications
            const allPriceModifications = [
              ...currentPriceModifications,
              ...newPriceModifications,
            ];

            // Convert to database format
            try {
              dbData.price_modifications = JSON.stringify(
                allPriceModifications.map((mod) => ({
                  id: mod.id,
                  amount: mod.amount,
                  reason: mod.reason,
                  created_at: mod.createdAt.toISOString(),
                  user_id: mod.userId,
                }))
              );
            } catch (error) {
              console.error("Error stringifying price modifications:", error);
              dbData.price_modifications = "[]";
            }
          }
        }
      }

      const { error } = await supabase
        .from("repairs")
        .update(dbData)
        .eq("id", id);

      if (error) {
        throw error;
      }

      // Check if status changed to 'completed' and send email notification
      // Temporarily disabled until database migration is complete
      /*
      if (data.status === "completed") {
        const currentRepair = repairs.find((r) => r.id === id);
        if (
          currentRepair &&
          currentRepair.customerEmail &&
          isValidEmail(currentRepair.customerEmail)
        ) {
          try {
            const emailTemplate = generateRepairCompletionEmailTemplate(
              { ...currentRepair, ...data },
              repairShop
            );

            const emailResult = await sendEmail(
              currentRepair.customerEmail,
              emailTemplate
            );

            if (emailResult.success) {
              toast.success(t("repair.emailSentSuccess"));
            } else {
              console.error("Failed to send email:", emailResult.error);
              toast.warning(t("repair.emailSentError"));
            }
          } catch (emailError) {
            console.error("Error sending completion email:", emailError);
            toast.warning(t("repair.emailSentError"));
          }
        }
      }
      */

      // If we're updating observations or price modifications, refresh the repairs list to get the latest data
      if (data.observations || data.priceModifications) {
        await refreshRepairs();
      } else {
        // Otherwise, just update the local state
        setRepairs(
          repairs.map((repair) =>
            repair.id === id ? { ...repair, ...data } : repair
          )
        );
      }
    } catch (error) {
      console.error("Error updating repair:", error);
      toast.error(t("repair.updateError"));
    }
  };

  const searchRepairs = (query: string): RepairItem[] => {
    const lowercaseQuery = query.toLowerCase();
    return repairs.filter(
      (repair) =>
        repair.customerName.toLowerCase().includes(lowercaseQuery) ||
        repair.customerPhone.includes(lowercaseQuery) ||
        repair.phoneModel.toLowerCase().includes(lowercaseQuery)
    );
  };

  const deleteRepair = async (id: string): Promise<void> => {
    if (!user) return;

    try {
      const { error } = await supabase.from("repairs").delete().eq("id", id);

      if (error) {
        throw error;
      }

      // Update local state
      setRepairs(repairs.filter((repair) => repair.id !== id));
    } catch (error) {
      console.error("Error deleting repair:", error);
      toast.error(t("repair.deleteError"));
    }
  };

  const importRepairs = async (newRepairs: RepairItem[]): Promise<void> => {
    if (!user || !repairShop) return;

    try {
      // Convert to database format
      const dbRepairs = newRepairs.map((repair) => {
        const dbRepair: any = {
          customer_name: repair.customerName,
          customer_phone: repair.customerPhone,
          phone_model: repair.phoneModel,
          problem_description: repair.problemDescription,
          repair_price: repair.repairPrice,
          payment_status: repair.paymentStatus,
          down_payment: repair.downPayment,
          status: repair.status,
          created_at: repair.createdAt.toISOString(),
          completed_at: repair.completedAt
            ? repair.completedAt.toISOString()
            : null,
          user_id: user.id,
          repair_shop_id: repairShop.id,
          ticket_number: repair.ticketNumber,
        };

        // Only add customer_email if it exists (for backward compatibility)
        if (repair.customerEmail) {
          dbRepair.customer_email = repair.customerEmail;
        }

        return dbRepair;
      });

      const { error } = await supabase.from("repairs").insert(dbRepairs);

      if (error) {
        throw error;
      }

      // Refresh repairs after import
      await refreshRepairs();
    } catch (error) {
      console.error("Error importing repairs:", error);
      toast.error(t("repair.importError"));
    }
  };

  return (
    <RepairContext.Provider
      value={{
        repairs,
        loading,
        hasMore,
        totalCount,
        addRepair,
        getRepairById,
        getRepairByTicketNumber,
        updateRepair,
        searchRepairs,
        deleteRepair,
        importRepairs,
        refreshRepairs,
        loadMoreRepairs,
      }}
    >
      {children}
    </RepairContext.Provider>
  );
};

export const useRepairContext = () => {
  const context = useContext(RepairContext);
  if (context === undefined) {
    throw new Error("useRepairContext must be used within a RepairProvider");
  }
  return context;
};
