import React, { createContext, useContext, useState, useEffect } from "react";
import {
  Product,
  ProductCategory,
  Sale,
  StockMovement,
  ProductFormData,
  CategoryFormData,
  SaleFormData,
} from "@/types/stock";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

interface StockContextType {
  // Products
  products: Product[];
  categories: ProductCategory[];
  sales: Sale[];
  stockMovements: StockMovement[];
  loading: boolean;

  // Product operations
  addProduct: (data: ProductFormData) => Promise<Product>;
  updateProduct: (id: string, data: Partial<ProductFormData>) => Promise<void>;
  deleteProduct: (id: string) => Promise<void>;
  getProductById: (id: string) => Promise<Product | undefined>;

  // Category operations
  addCategory: (data: CategoryFormData) => Promise<ProductCategory>;
  updateCategory: (
    id: string,
    data: Partial<CategoryFormData>
  ) => Promise<void>;
  deleteCategory: (id: string) => Promise<void>;

  // Sales operations
  processSale: (data: SaleFormData) => Promise<Sale>;
  getSaleById: (id: string) => Promise<Sale | undefined>;

  // Stock operations
  adjustStock: (
    productId: string,
    quantity: number,
    reason: string
  ) => Promise<void>;

  // Refresh functions
  refreshProducts: () => Promise<void>;
  refreshCategories: () => Promise<void>;
  refreshSales: () => Promise<void>;
}

const StockContext = createContext<StockContextType | undefined>(undefined);

export const StockProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const { user } = useAuth();
  const { repairShop } = useRepairShopContext();
  const { t } = useTranslation();

  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<ProductCategory[]>([]);
  const [sales, setSales] = useState<Sale[]>([]);
  const [stockMovements, setStockMovements] = useState<StockMovement[]>([]);
  const [loading, setLoading] = useState(true);

  // Fetch data when user or repair shop changes
  useEffect(() => {
    if (user && repairShop) {
      refreshAll();
      setupRealtimeSubscriptions();
    } else {
      setProducts([]);
      setCategories([]);
      setSales([]);
      setStockMovements([]);
      setLoading(false);
    }
  }, [user, repairShop]);

  const refreshAll = async () => {
    try {
      await Promise.all([
        refreshCategories(),
        refreshProducts(),
        refreshSales(),
      ]);
    } catch (error) {
      console.error("Error refreshing stock data:", error);
    }
    setLoading(false);
  };

  const setupRealtimeSubscriptions = () => {
    if (!repairShop) return;

    // Products subscription
    const productsSubscription = supabase
      .channel("products-changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "products",
          filter: `repair_shop_id=eq.${repairShop.id}`,
        },
        () => refreshProducts()
      )
      .subscribe();

    // Categories subscription
    const categoriesSubscription = supabase
      .channel("categories-changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "product_categories",
          filter: `repair_shop_id=eq.${repairShop.id}`,
        },
        () => refreshCategories()
      )
      .subscribe();

    // Sales subscription
    const salesSubscription = supabase
      .channel("sales-changes")
      .on(
        "postgres_changes",
        {
          event: "*",
          schema: "public",
          table: "sales",
          filter: `repair_shop_id=eq.${repairShop.id}`,
        },
        () => refreshSales()
      )
      .subscribe();

    return () => {
      supabase.removeChannel(productsSubscription);
      supabase.removeChannel(categoriesSubscription);
      supabase.removeChannel(salesSubscription);
    };
  };

  const refreshCategories = async () => {
    if (!user || !repairShop) return;

    try {
      const { data, error } = await supabase
        .from("product_categories")
        .select("*")
        .eq("repair_shop_id", repairShop.id)
        .order("name");

      if (error) throw error;

      const processedCategories: ProductCategory[] = data.map((cat) => ({
        id: cat.id,
        name: cat.name,
        description: cat.description || undefined,
        icon: cat.icon || undefined,
        color: cat.color || undefined,
        createdAt: new Date(cat.created_at),
        repairShopId: cat.repair_shop_id,
      }));

      setCategories(processedCategories);
    } catch (error) {
      console.error("Error fetching categories:", error);
      toast.error(t("stock.fetchError"));
    }
  };

  const refreshProducts = async () => {
    if (!user || !repairShop) return;

    try {
      const { data, error } = await supabase
        .from("products")
        .select(
          `
          *,
          category:product_categories(*)
        `
        )
        .eq("repair_shop_id", repairShop.id)
        .order("name");

      if (error) throw error;

      const processedProducts: Product[] = data.map((product) => ({
        id: product.id,
        name: product.name,
        description: product.description || undefined,
        sku: product.sku,
        barcode: product.barcode || undefined,
        categoryId: product.category_id,
        category: product.category
          ? {
              id: product.category.id,
              name: product.category.name,
              description: product.category.description || undefined,
              icon: product.category.icon || undefined,
              color: product.category.color || undefined,
              createdAt: new Date(product.category.created_at),
              repairShopId: product.category.repair_shop_id,
            }
          : undefined,
        price: product.price,
        cost: product.cost,
        stockQuantity: product.stock_quantity,
        minStockLevel: product.min_stock_level,
        maxStockLevel: product.max_stock_level || undefined,
        isActive: product.is_active,
        imageUrl: product.image_url || undefined,
        createdAt: new Date(product.created_at),
        updatedAt: new Date(product.updated_at),
        repairShopId: product.repair_shop_id,
      }));

      setProducts(processedProducts);
    } catch (error) {
      console.error("Error fetching products:", error);
      toast.error(t("stock.fetchError"));
    }
  };

  const refreshSales = async () => {
    if (!user || !repairShop) return;

    try {
      const { data, error } = await supabase
        .from("sales")
        .select(
          `
          *,
          sale_items(
            *,
            product:products(*)
          )
        `
        )
        .eq("repair_shop_id", repairShop.id)
        .order("created_at", { ascending: false });

      if (error) throw error;

      const processedSales: Sale[] = data.map((sale) => ({
        id: sale.id,
        saleNumber: sale.sale_number,
        customerId: sale.customer_id || undefined,
        customerName: sale.customer_name || undefined,
        customerPhone: sale.customer_phone || undefined,
        items: sale.sale_items.map((item: any) => ({
          id: item.id,
          productId: item.product_id,
          product: item.product
            ? {
                id: item.product.id,
                name: item.product.name,
                description: item.product.description || undefined,
                sku: item.product.sku,
                barcode: item.product.barcode || undefined,
                categoryId: item.product.category_id,
                price: item.product.price,
                cost: item.product.cost,
                stockQuantity: item.product.stock_quantity,
                minStockLevel: item.product.min_stock_level,
                maxStockLevel: item.product.max_stock_level || undefined,
                isActive: item.product.is_active,
                imageUrl: item.product.image_url || undefined,
                createdAt: new Date(item.product.created_at),
                updatedAt: new Date(item.product.updated_at),
                repairShopId: item.product.repair_shop_id,
              }
            : undefined,
          quantity: item.quantity,
          unitPrice: item.unit_price,
          discount: item.discount,
          totalPrice: item.total_price,
        })),
        subtotal: sale.subtotal,
        taxAmount: sale.tax_amount,
        discountAmount: sale.discount_amount,
        totalAmount: sale.total_amount,
        paymentMethod: sale.payment_method as
          | "cash"
          | "card"
          | "mobile"
          | "mixed",
        paymentStatus: sale.payment_status as "paid" | "partial" | "pending",
        amountPaid: sale.amount_paid,
        changeAmount: sale.change_amount,
        notes: sale.notes || undefined,
        createdAt: new Date(sale.created_at),
        userId: sale.user_id,
        repairShopId: sale.repair_shop_id,
      }));

      setSales(processedSales);
    } catch (error) {
      console.error("Error fetching sales:", error);
      toast.error(t("stock.fetchError"));
    }
  };

  const addCategory = async (
    data: CategoryFormData
  ): Promise<ProductCategory> => {
    if (!user || !repairShop) throw new Error("User or repair shop not found");

    try {
      const { data: insertedData, error } = await supabase
        .from("product_categories")
        .insert({
          name: data.name,
          description: data.description,
          icon: data.icon,
          color: data.color,
          repair_shop_id: repairShop.id,
        })
        .select()
        .single();

      if (error) throw error;

      const newCategory: ProductCategory = {
        id: insertedData.id,
        name: insertedData.name,
        description: insertedData.description || undefined,
        icon: insertedData.icon || undefined,
        color: insertedData.color || undefined,
        createdAt: new Date(insertedData.created_at),
        repairShopId: insertedData.repair_shop_id,
      };

      setCategories([...categories, newCategory]);
      toast.success(t("stock.categoryAdded"));
      return newCategory;
    } catch (error) {
      console.error("Error adding category:", error);
      toast.error(t("stock.addError"));
      throw error;
    }
  };

  const addProduct = async (data: ProductFormData): Promise<Product> => {
    if (!user || !repairShop) throw new Error("User or repair shop not found");

    try {
      const { data: insertedData, error } = await supabase
        .from("products")
        .insert({
          name: data.name,
          description: data.description,
          sku: data.sku,
          barcode:
            data.barcode && data.barcode.trim() !== "" ? data.barcode : null,
          category_id: data.categoryId,
          price: data.price,
          cost: data.cost,
          stock_quantity: data.stockQuantity,
          min_stock_level: data.minStockLevel,
          max_stock_level: data.maxStockLevel,
          is_active: data.isActive,
          image_url: data.imageUrl,
          repair_shop_id: repairShop.id,
        })
        .select()
        .single();

      if (error) throw error;

      const newProduct: Product = {
        id: insertedData.id,
        name: insertedData.name,
        description: insertedData.description || undefined,
        sku: insertedData.sku,
        barcode: insertedData.barcode || undefined,
        categoryId: insertedData.category_id,
        price: insertedData.price,
        cost: insertedData.cost,
        stockQuantity: insertedData.stock_quantity,
        minStockLevel: insertedData.min_stock_level,
        maxStockLevel: insertedData.max_stock_level || undefined,
        isActive: insertedData.is_active,
        imageUrl: insertedData.image_url || undefined,
        createdAt: new Date(insertedData.created_at),
        updatedAt: new Date(insertedData.updated_at),
        repairShopId: insertedData.repair_shop_id,
      };

      setProducts([...products, newProduct]);
      toast.success(t("stock.productAdded"));
      return newProduct;
    } catch (error) {
      console.error("Error adding product:", error);
      toast.error(t("stock.addError"));
      throw error;
    }
  };

  const updateProduct = async (
    id: string,
    data: Partial<ProductFormData>
  ): Promise<void> => {
    try {
      const { error } = await supabase
        .from("products")
        .update({
          name: data.name,
          description: data.description,
          sku: data.sku,
          barcode:
            data.barcode && data.barcode.trim() !== "" ? data.barcode : null,
          category_id: data.categoryId,
          price: data.price,
          cost: data.cost,
          stock_quantity: data.stockQuantity,
          min_stock_level: data.minStockLevel,
          max_stock_level: data.maxStockLevel,
          is_active: data.isActive,
          image_url: data.imageUrl,
        })
        .eq("id", id);

      if (error) throw error;

      await refreshProducts();
      toast.success(t("stock.productUpdated"));
    } catch (error) {
      console.error("Error updating product:", error);
      toast.error(t("stock.updateError"));
      throw error;
    }
  };

  const deleteProduct = async (id: string): Promise<void> => {
    try {
      const { error } = await supabase.from("products").delete().eq("id", id);

      if (error) throw error;

      setProducts(products.filter((p) => p.id !== id));
      toast.success(t("stock.productDeleted"));
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error(t("stock.deleteError"));
      throw error;
    }
  };

  const getProductById = async (id: string): Promise<Product | undefined> => {
    try {
      const { data, error } = await supabase
        .from("products")
        .select(
          `
          *,
          category:product_categories(*)
        `
        )
        .eq("id", id)
        .single();

      if (error) throw error;

      return {
        id: data.id,
        name: data.name,
        description: data.description || undefined,
        sku: data.sku,
        barcode: data.barcode || undefined,
        categoryId: data.category_id,
        category: data.category
          ? {
              id: data.category.id,
              name: data.category.name,
              description: data.category.description || undefined,
              icon: data.category.icon || undefined,
              color: data.category.color || undefined,
              createdAt: new Date(data.category.created_at),
              repairShopId: data.category.repair_shop_id,
            }
          : undefined,
        price: data.price,
        cost: data.cost,
        stockQuantity: data.stock_quantity,
        minStockLevel: data.min_stock_level,
        maxStockLevel: data.max_stock_level || undefined,
        isActive: data.is_active,
        imageUrl: data.image_url || undefined,
        createdAt: new Date(data.created_at),
        updatedAt: new Date(data.updated_at),
        repairShopId: data.repair_shop_id,
      };
    } catch (error) {
      console.error("Error fetching product:", error);
      return undefined;
    }
  };

  const updateCategory = async (
    id: string,
    data: Partial<CategoryFormData>
  ): Promise<void> => {
    try {
      const { error } = await supabase
        .from("product_categories")
        .update({
          name: data.name,
          description: data.description,
          icon: data.icon,
          color: data.color,
        })
        .eq("id", id);

      if (error) throw error;

      await refreshCategories();
      toast.success(t("stock.categoryUpdated"));
    } catch (error) {
      console.error("Error updating category:", error);
      toast.error(t("stock.updateError"));
      throw error;
    }
  };

  const deleteCategory = async (id: string): Promise<void> => {
    try {
      const { error } = await supabase
        .from("product_categories")
        .delete()
        .eq("id", id);

      if (error) throw error;

      setCategories(categories.filter((c) => c.id !== id));
      toast.success(t("stock.categoryDeleted"));
    } catch (error) {
      console.error("Error deleting category:", error);
      toast.error(t("stock.deleteError"));
      throw error;
    }
  };

  const processSale = async (data: SaleFormData): Promise<Sale> => {
    if (!user || !repairShop) {
      throw new Error("User or repair shop not found");
    }

    try {
      // Calculate totals
      const subtotal = data.items.reduce(
        (sum, item) => sum + (item.unitPrice - item.discount) * item.quantity,
        0
      );
      const totalAmount = subtotal - data.discountAmount;
      const changeAmount = Math.max(0, data.amountPaid - totalAmount);

      // Create sale
      const saleInsertData = {
        customer_name: data.customerName,
        customer_phone: data.customerPhone,
        subtotal,
        tax_amount: 0, // No tax
        discount_amount: data.discountAmount,
        total_amount: totalAmount,
        payment_method: data.paymentMethod,
        payment_status: data.amountPaid >= totalAmount ? "paid" : "partial",
        amount_paid: data.amountPaid,
        change_amount: changeAmount,
        notes: data.notes,
        user_id: user.id,
        repair_shop_id: repairShop.id,
      };

      const { data: saleData, error: saleError } = await supabase
        .from("sales")
        .insert(saleInsertData)
        .select()
        .single();

      if (saleError) {
        throw saleError;
      }

      // Create sale items
      const saleItems = data.items.map((item) => ({
        sale_id: saleData.id,
        product_id: item.productId,
        quantity: item.quantity,
        unit_price: item.unitPrice,
        discount: item.discount,
        total_price: (item.unitPrice - item.discount) * item.quantity,
      }));

      const { error: itemsError } = await supabase
        .from("sale_items")
        .insert(saleItems);

      if (itemsError) {
        throw itemsError;
      }

      await refreshSales();
      await refreshProducts(); // Refresh to update stock quantities
      toast.success(t("stock.saleCompleted"));

      return {
        id: saleData.id,
        saleNumber: saleData.sale_number,
        customerName: saleData.customer_name || undefined,
        customerPhone: saleData.customer_phone || undefined,
        items: [], // Will be populated by refresh
        subtotal,
        taxAmount: 0, // No tax
        discountAmount: data.discountAmount,
        totalAmount,
        paymentMethod: data.paymentMethod,
        paymentStatus: saleData.payment_status as
          | "paid"
          | "partial"
          | "pending",
        amountPaid: data.amountPaid,
        changeAmount,
        notes: data.notes,
        createdAt: new Date(saleData.created_at),
        userId: saleData.user_id,
        repairShopId: saleData.repair_shop_id,
      };
    } catch (error) {
      console.error("Error processing sale:", error);
      toast.error(t("stock.saleError"));
      throw error;
    }
  };

  const getSaleById = async (id: string): Promise<Sale | undefined> => {
    try {
      const { data, error } = await supabase
        .from("sales")
        .select(
          `
          *,
          sale_items(
            *,
            product:products(*)
          )
        `
        )
        .eq("id", id)
        .single();

      if (error) throw error;

      return {
        id: data.id,
        saleNumber: data.sale_number,
        customerId: data.customer_id || undefined,
        customerName: data.customer_name || undefined,
        customerPhone: data.customer_phone || undefined,
        items: data.sale_items.map((item: any) => ({
          id: item.id,
          productId: item.product_id,
          product: item.product
            ? {
                id: item.product.id,
                name: item.product.name,
                description: item.product.description || undefined,
                sku: item.product.sku,
                barcode: item.product.barcode || undefined,
                categoryId: item.product.category_id,
                price: item.product.price,
                cost: item.product.cost,
                stockQuantity: item.product.stock_quantity,
                minStockLevel: item.product.min_stock_level,
                maxStockLevel: item.product.max_stock_level || undefined,
                isActive: item.product.is_active,
                imageUrl: item.product.image_url || undefined,
                createdAt: new Date(item.product.created_at),
                updatedAt: new Date(item.product.updated_at),
                repairShopId: item.product.repair_shop_id,
              }
            : undefined,
          quantity: item.quantity,
          unitPrice: item.unit_price,
          discount: item.discount,
          totalPrice: item.total_price,
        })),
        subtotal: data.subtotal,
        taxAmount: data.tax_amount,
        discountAmount: data.discount_amount,
        totalAmount: data.total_amount,
        paymentMethod: data.payment_method as
          | "cash"
          | "card"
          | "mobile"
          | "mixed",
        paymentStatus: data.payment_status as "paid" | "partial" | "pending",
        amountPaid: data.amount_paid,
        changeAmount: data.change_amount,
        notes: data.notes || undefined,
        createdAt: new Date(data.created_at),
        userId: data.user_id,
        repairShopId: data.repair_shop_id,
      };
    } catch (error) {
      console.error("Error fetching sale:", error);
      return undefined;
    }
  };

  const adjustStock = async (
    productId: string,
    quantity: number,
    reason: string
  ): Promise<void> => {
    if (!user || !repairShop) throw new Error("User or repair shop not found");

    try {
      // Update product stock
      const { error: updateError } = await supabase
        .from("products")
        .update({
          stock_quantity: quantity,
        })
        .eq("id", productId);

      if (updateError) throw updateError;

      // Create stock movement record
      const { error: movementError } = await supabase
        .from("stock_movements")
        .insert({
          product_id: productId,
          type: "adjustment",
          quantity,
          reason,
          user_id: user.id,
          repair_shop_id: repairShop.id,
        });

      if (movementError) throw movementError;

      await refreshProducts();
      toast.success(t("stock.stockAdjusted"));
    } catch (error) {
      console.error("Error adjusting stock:", error);
      toast.error(t("stock.adjustError"));
      throw error;
    }
  };

  return (
    <StockContext.Provider
      value={{
        products,
        categories,
        sales,
        stockMovements,
        loading,
        addProduct,
        updateProduct,
        deleteProduct,
        getProductById,
        addCategory,
        updateCategory,
        deleteCategory,
        processSale,
        getSaleById,
        adjustStock,
        refreshProducts,
        refreshCategories,
        refreshSales,
      }}
    >
      {children}
    </StockContext.Provider>
  );
};

export const useStock = () => {
  const context = useContext(StockContext);
  if (context === undefined) {
    throw new Error("useStock must be used within a StockProvider");
  }
  return context;
};
