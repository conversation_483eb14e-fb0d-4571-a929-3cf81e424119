import React, { createContext, useContext, useState, useEffect } from "react";
import { StoreBranch } from "@/types";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { toast } from "sonner";

interface BranchContextType {
  branches: StoreBranch[];
  currentBranch: StoreBranch | null;
  loading: boolean;
  fetchBranches: () => Promise<void>;
  createBranch: (
    branchData: Omit<
      StoreBranch,
      "id" | "createdAt" | "updatedAt" | "repairShopId"
    >
  ) => Promise<boolean>;
  updateBranch: (
    id: string,
    branchData: Partial<StoreBranch>
  ) => Promise<boolean>;
  deleteBranch: (id: string) => Promise<boolean>;
  assignUserToBranch: (userId: string, branchId: string) => Promise<boolean>;
  getUserBranch: (userId: string) => Promise<StoreBranch | null>;
}

const BranchContext = createContext<BranchContextType | undefined>(undefined);

export const BranchProvider: React.FC<{ children: React.ReactNode }> = ({
  children,
}) => {
  const [branches, setBranches] = useState<StoreBranch[]>([]);
  const [currentBranch, setCurrentBranch] = useState<StoreBranch | null>(null);
  const [loading, setLoading] = useState(true);
  const { user, roles } = useAuth();
  const { repairShop } = useRepairShopContext();

  // Fetch branches when repair shop changes
  useEffect(() => {
    if (repairShop) {
      fetchBranches();
      fetchCurrentUserBranch();
    }
  }, [repairShop]);

  const fetchBranches = async () => {
    if (!repairShop) return;

    try {
      setLoading(true);
      const { data, error } = await supabase.rpc("get_repair_shop_branches", {
        p_repair_shop_id: repairShop.id,
      });

      if (error) {
        console.error("Error fetching branches:", error);
        toast.error("Failed to fetch store branches");
        return;
      }

      // Transform the data to match the StoreBranch interface
      const transformedBranches = (data || []).map((branch: any) => ({
        id: branch.id,
        name: branch.name,
        code: branch.code,
        address: branch.address,
        phone: branch.phone,
        email: branch.email,
        managerName: branch.manager_name,
        isActive: branch.is_active, // Map database field to interface property
        repairShopId: repairShop.id,
        createdAt: new Date(branch.created_at),
        updatedAt: new Date(branch.updated_at),
      }));

      setBranches(transformedBranches);
    } catch (error) {
      console.error("Exception fetching branches:", error);
      toast.error("Failed to fetch store branches");
    } finally {
      setLoading(false);
    }
  };

  const fetchCurrentUserBranch = async () => {
    if (!user || !repairShop) return;

    try {
      const { data, error } = await supabase.rpc("get_user_branch_info", {
        p_user_id: user.id,
      });

      if (error) {
        console.error("Error fetching user branch:", error);
        return;
      }

      if (data && data.length > 0) {
        const branchData = data[0];
        setCurrentBranch({
          id: branchData.branch_id,
          name: branchData.branch_name,
          code: branchData.branch_code,
          repairShopId: branchData.repair_shop_id,
          isActive: true,
          createdAt: new Date(),
          updatedAt: new Date(),
        });
      }
    } catch (error) {
      console.error("Exception fetching user branch:", error);
    }
  };

  const createBranch = async (
    branchData: Omit<
      StoreBranch,
      "id" | "createdAt" | "updatedAt" | "repairShopId"
    >
  ): Promise<boolean> => {
    if (!repairShop) {
      toast.error("No repair shop selected");
      return false;
    }

    try {
      const { data, error } = await supabase
        .from("store_branches")
        .insert({
          name: branchData.name,
          code: branchData.code,
          address: branchData.address,
          phone: branchData.phone,
          email: branchData.email,
          manager_name: branchData.managerName,
          is_active: branchData.isActive,
          repair_shop_id: repairShop.id,
        })
        .select()
        .single();

      if (error) {
        console.error("Error creating branch:", error);
        toast.error("Failed to create branch");
        return false;
      }

      toast.success("Branch created successfully");
      await fetchBranches();
      return true;
    } catch (error) {
      console.error("Exception creating branch:", error);
      toast.error("Failed to create branch");
      return false;
    }
  };

  const updateBranch = async (
    id: string,
    branchData: Partial<StoreBranch>
  ): Promise<boolean> => {
    try {
      const updateData: any = { ...branchData };
      if (branchData.managerName !== undefined) {
        updateData.manager_name = branchData.managerName;
        delete updateData.managerName;
      }
      if (branchData.isActive !== undefined) {
        updateData.is_active = branchData.isActive;
        delete updateData.isActive;
      }

      const { error } = await supabase
        .from("store_branches")
        .update(updateData)
        .eq("id", id);

      if (error) {
        console.error("Error updating branch:", error);
        toast.error("Failed to update branch");
        return false;
      }

      toast.success("Branch updated successfully");
      await fetchBranches();
      return true;
    } catch (error) {
      console.error("Exception updating branch:", error);
      toast.error("Failed to update branch");
      return false;
    }
  };

  const deleteBranch = async (id: string): Promise<boolean> => {
    try {
      const { error } = await supabase
        .from("store_branches")
        .delete()
        .eq("id", id);

      if (error) {
        console.error("Error deleting branch:", error);
        toast.error("Failed to delete branch");
        return false;
      }

      toast.success("Branch deleted successfully");
      await fetchBranches();
      return true;
    } catch (error) {
      console.error("Exception deleting branch:", error);
      toast.error("Failed to delete branch");
      return false;
    }
  };

  const assignUserToBranch = async (
    userId: string,
    branchId: string
  ): Promise<boolean> => {
    if (!repairShop) {
      toast.error("No repair shop selected");
      return false;
    }

    try {
      const { data, error } = await supabase.rpc("assign_user_to_branch", {
        p_user_id: userId,
        p_repair_shop_id: repairShop.id,
        p_branch_id: branchId,
      });

      if (error || !data) {
        console.error("Error assigning user to branch:", error);
        toast.error("Failed to assign user to branch");
        return false;
      }

      toast.success("User assigned to branch successfully");
      return true;
    } catch (error) {
      console.error("Exception assigning user to branch:", error);
      toast.error("Failed to assign user to branch");
      return false;
    }
  };

  const getUserBranch = async (userId: string): Promise<StoreBranch | null> => {
    try {
      const { data, error } = await supabase.rpc("get_user_branch_info", {
        p_user_id: userId,
      });

      if (error || !data || data.length === 0) {
        return null;
      }

      const branchData = data[0];
      return {
        id: branchData.branch_id,
        name: branchData.branch_name,
        code: branchData.branch_code,
        repairShopId: branchData.repair_shop_id,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      };
    } catch (error) {
      console.error("Exception getting user branch:", error);
      return null;
    }
  };

  return (
    <BranchContext.Provider
      value={{
        branches,
        currentBranch,
        loading,
        fetchBranches,
        createBranch,
        updateBranch,
        deleteBranch,
        assignUserToBranch,
        getUserBranch,
      }}
    >
      {children}
    </BranchContext.Provider>
  );
};

export const useBranchContext = () => {
  const context = useContext(BranchContext);
  if (context === undefined) {
    throw new Error("useBranchContext must be used within a BranchProvider");
  }
  return context;
};
