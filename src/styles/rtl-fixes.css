/* RTL Layout Fixes */

/* Fix for react-grid-layout in RTL mode */
html[dir="rtl"] .react-grid-layout {
  direction: ltr !important;
}

/* Fix for dashboard widgets in RTL mode */
html[dir="rtl"] .dashboard-widget {
  direction: ltr !important;
}

/* Fix for charts in RTL mode */
html[dir="rtl"] .recharts-wrapper {
  direction: ltr !important;
}

/* Fix for grid layout in RTL mode */
html[dir="rtl"] .grid {
  direction: ltr !important;
}

/* Fix for flex layout in RTL mode */
html[dir="rtl"] .flex {
  direction: inherit;
}

/* Fix for text alignment in RTL mode */
html[dir="rtl"] .dashboard-widget .text-left {
  text-align: right !important;
}

html[dir="rtl"] .dashboard-widget .text-right {
  text-align: left !important;
}

/* Fix for margins and paddings in RTL mode */
html[dir="rtl"] .dashboard-widget .mr-1,
html[dir="rtl"] .dashboard-widget .mr-2,
html[dir="rtl"] .dashboard-widget .mr-3,
html[dir="rtl"] .dashboard-widget .mr-4 {
  margin-right: 0 !important;
  margin-left: 0.25rem !important;
}

html[dir="rtl"] .dashboard-widget .ml-1,
html[dir="rtl"] .dashboard-widget .ml-2,
html[dir="rtl"] .dashboard-widget .ml-3,
html[dir="rtl"] .dashboard-widget .ml-4 {
  margin-left: 0 !important;
  margin-right: 0.25rem !important;
}

/* Fix for widget content in RTL mode */
html[dir="rtl"] .dashboard-widget .card-content {
  direction: rtl !important;
}

/* Fix for widget headers in RTL mode */
html[dir="rtl"] .dashboard-widget .card-header {
  direction: rtl !important;
}

/* Fix for widget titles in RTL mode */
html[dir="rtl"] .dashboard-widget .card-title {
  direction: rtl !important;
}
