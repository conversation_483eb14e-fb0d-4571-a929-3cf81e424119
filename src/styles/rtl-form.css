/* RTL Form Styles */

/* Form labels in RTL mode */
html[dir="rtl"] .form-label {
  text-align: right;
}

/* Form inputs in RTL mode */
html[dir="rtl"] .form-input,
html[dir="rtl"] .form-textarea,
html[dir="rtl"] .form-select {
  text-align: right;
  direction: rtl;
}

/* Form messages in RTL mode */
html[dir="rtl"] .form-message {
  text-align: right;
}

/* Form buttons in RTL mode */
html[dir="rtl"] .form-button {
  direction: rtl;
}

/* Form grid in RTL mode */
html[dir="rtl"] .grid {
  direction: rtl;
}

/* Form flex containers in RTL mode */
html[dir="rtl"] .flex {
  direction: inherit;
}

/* Form placeholders in RTL mode */
html[dir="rtl"] input::placeholder,
html[dir="rtl"] textarea::placeholder {
  text-align: right;
  direction: rtl;
}

/* Form select content in RTL mode */
html[dir="rtl"] .select-content {
  text-align: right;
  direction: rtl;
}

/* Form select items in RTL mode */
html[dir="rtl"] .select-item {
  text-align: right;
  direction: rtl;
}

/* Form select trigger in RTL mode */
html[dir="rtl"] .select-trigger {
  text-align: right;
  direction: rtl;
}

/* Form select value in RTL mode */
html[dir="rtl"] .select-value {
  text-align: right;
  direction: rtl;
}

/* Form observations in RTL mode */
html[dir="rtl"] .observation-item {
  flex-direction: row-reverse;
}

/* Form observation buttons in RTL mode */
html[dir="rtl"] .observation-button {
  margin-right: 0;
  margin-left: 0.5rem;
}

/* Form observation text in RTL mode */
html[dir="rtl"] .observation-text {
  text-align: right;
}

/* Form submit button in RTL mode */
html[dir="rtl"] .submit-button {
  direction: rtl;
}
