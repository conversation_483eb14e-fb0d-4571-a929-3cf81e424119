@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;

    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;

    --radius: 0.5rem;

    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Premium Theme Variables */
  .premium {
    --primary: 45 93% 47%; /* Gold */
    --primary-foreground: 0 0% 100%;
    --accent: 43 74% 66%; /* Light gold */
    --accent-foreground: 222.2 47.4% 11.2%;
    --premium-gold: 45 93% 47%;
    --premium-gold-light: 48 100% 67%;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;

    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;

    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;

    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;

    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;

    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;

    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;

    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;

    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
    --sidebar-background: 240 5.9% 10%;
    --sidebar-foreground: 240 4.8% 95.9%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 240 3.7% 15.9%;
    --sidebar-accent-foreground: 240 4.8% 95.9%;
    --sidebar-border: 240 3.7% 15.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }

  /* Premium Dark Theme */
  .dark.premium {
    --primary: 48 100% 67%; /* Lighter gold for dark mode */
    --primary-foreground: 222.2 84% 4.9%;
    --accent: 45 93% 47%;
    --accent-foreground: 210 40% 98%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

/* Premium Component Styles */
@layer components {
  .premium .premium-card {
    @apply shadow-lg border-2 transition-all duration-300;
    border-color: hsl(var(--premium-gold) / 0.3);
  }

  .premium .premium-card:hover {
    @apply shadow-xl;
    transform: translateY(-2px);
    border-color: hsl(var(--premium-gold));
  }

  .premium .premium-button {
    @apply bg-primary text-primary-foreground font-semibold transition-all duration-300;
  }

  .premium .premium-button:hover {
    transform: translateY(-1px);
    box-shadow: 0 8px 25px hsl(var(--premium-gold) / 0.3);
  }

  .premium .premium-badge {
    @apply inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-primary text-primary-foreground;
  }

  .premium .premium-border {
    border: 1px solid hsl(var(--premium-gold));
  }

  .premium .premium-glow {
    box-shadow: 0 0 20px hsl(var(--premium-gold) / 0.2);
  }

  .premium .premium-sidebar {
    @apply bg-card/95 backdrop-blur-sm;
    border-right: 1px solid hsl(var(--premium-gold) / 0.2);
  }

  .premium .premium-header {
    @apply bg-card/95 backdrop-blur-sm;
    border-bottom: 1px solid hsl(var(--premium-gold) / 0.2);
  }
}