/**
 * Feature configuration for the repair shop application
 * Set features to false to disable them throughout the application
 */

export interface FeatureConfig {
  // Stock & POS Features
  stockManagement: boolean;
  pointOfSale: boolean;
  productCatalog: boolean;
  inventoryTracking: boolean;
  salesReporting: boolean;

  // Barcode Features
  barcodeScanning: boolean;
  barcodeGeneration: boolean;
  barcodePrinting: boolean;
  barcodeStickers: boolean;

  // Scanner Features
  cameraScanner: boolean;
  henexScanner: boolean;
  universalScanner: boolean;

  // Navigation Features
  stockNavigation: boolean;
}

/**
 * Default feature configuration
 * Modify these values to enable/disable features
 */
export const FEATURE_CONFIG: FeatureConfig = {
  // Stock & POS Features - ENABLED
  stockManagement: true,
  pointOfSale: true,
  productCatalog: true,
  inventoryTracking: true,
  salesReporting: true,

  // Barcode Features - ENABLED
  barcodeScanning: true,
  barcodeGeneration: true,
  barcodePrinting: true,
  barcodeStickers: true,

  // Scanner Features - ENABLED
  cameraScanner: true,
  henexScanner: true,
  universalScanner: true,

  // Navigation Features - ENABLED
  stockNavigation: true,
};

/**
 * Helper functions to check feature availability
 */
export const isFeatureEnabled = (feature: keyof FeatureConfig): boolean => {
  return FEATURE_CONFIG[feature];
};

export const isStockEnabled = (): boolean => {
  return FEATURE_CONFIG.stockManagement;
};

export const isBarcodeEnabled = (): boolean => {
  return (
    FEATURE_CONFIG.barcodeScanning ||
    FEATURE_CONFIG.barcodeGeneration ||
    FEATURE_CONFIG.barcodePrinting ||
    FEATURE_CONFIG.barcodeStickers
  );
};

export const isScannerEnabled = (): boolean => {
  return (
    FEATURE_CONFIG.cameraScanner ||
    FEATURE_CONFIG.henexScanner ||
    FEATURE_CONFIG.universalScanner
  );
};
