import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import {
  Smartphone,
  Laptop,
  Monitor,
  HardDrive,
  Battery,
  Hash,
  Palette,
  FileText,
  Star,
} from "lucide-react";
import { OneTimeItemFormData, ProductFormData } from "@/types/stock";
import { toast } from "sonner";

interface OneTimeItemFormDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onSubmit: (productData: ProductFormData) => Promise<void>;
  categories: Array<{ id: string; name: string }>;
  initialData?: ProductFormData;
  mode: "add" | "edit";
}

const deviceTypeIcons = {
  iphone: Smartphone,
  android: Smartphone,
  mac: Monitor,
  pc: Monitor,
  laptop: Laptop,
  other: HardDrive,
};

const conditionColors = {
  excellent: "bg-green-100 text-green-800 border-green-200",
  good: "bg-blue-100 text-blue-800 border-blue-200",
  fair: "bg-yellow-100 text-yellow-800 border-yellow-200",
  poor: "bg-red-100 text-red-800 border-red-200",
};

const OneTimeItemFormDialog: React.FC<OneTimeItemFormDialogProps> = ({
  open,
  onOpenChange,
  onSubmit,
  categories,
  initialData,
  mode,
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);

  // Product form data
  const [formData, setFormData] = useState<ProductFormData>({
    name: "",
    description: "",
    sku: "",
    barcode: "",
    categoryId: "",
    price: 0,
    cost: 0,
    stockQuantity: 1,
    minStockLevel: 0,
    maxStockLevel: 1,
    isActive: true,
    imageUrl: "",
    isOneTimeItem: true,
    oneTimeItemData: {
      serialNumber: "",
      batteryPercentage: undefined,
      condition: "good",
      deviceType: "iphone",
      model: "",
      storage: "",
      color: "",
      notes: "",
    },
  });

  // Reset form when dialog opens/closes or initial data changes
  useEffect(() => {
    if (open) {
      if (initialData && mode === "edit") {
        setFormData(initialData);
      } else {
        // Generate SKU for new one-time items
        const timestamp = Date.now().toString().slice(-6);
        setFormData({
          name: "",
          description: "",
          sku: `OTI-${timestamp}`,
          barcode: "",
          categoryId: "",
          price: 0,
          cost: 0,
          stockQuantity: 1,
          minStockLevel: 0,
          maxStockLevel: 1,
          isActive: true,
          imageUrl: "",
          isOneTimeItem: true,
          oneTimeItemData: {
            serialNumber: "",
            batteryPercentage: undefined,
            condition: "good",
            deviceType: "iphone",
            model: "",
            storage: "",
            color: "",
            notes: "",
          },
        });
      }
    }
  }, [open, initialData, mode]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error(t("stock.nameRequired"));
      return;
    }

    if (!formData.categoryId) {
      toast.error(t("stock.categoryRequired"));
      return;
    }

    if (!formData.oneTimeItemData?.condition) {
      toast.error(t("stock.conditionRequired"));
      return;
    }

    if (!formData.oneTimeItemData?.deviceType) {
      toast.error(t("stock.deviceTypeRequired"));
      return;
    }

    // Validate battery percentage if provided
    if (
      formData.oneTimeItemData?.batteryPercentage !== undefined &&
      (formData.oneTimeItemData.batteryPercentage < 0 ||
        formData.oneTimeItemData.batteryPercentage > 100)
    ) {
      toast.error(t("stock.invalidBatteryPercentage"));
      return;
    }

    setLoading(true);
    try {
      await onSubmit(formData);
      onOpenChange(false);
      toast.success(
        mode === "add"
          ? t("stock.oneTimeItemAdded")
          : t("stock.oneTimeItemUpdated")
      );
    } catch (error) {
      console.error("Error submitting one-time item:", error);
      toast.error(t("stock.submitError"));
    } finally {
      setLoading(false);
    }
  };

  const updateFormData = (field: keyof ProductFormData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const updateOneTimeItemData = (
    field: keyof OneTimeItemFormData,
    value: any
  ) => {
    setFormData((prev) => ({
      ...prev,
      oneTimeItemData: {
        ...prev.oneTimeItemData!,
        [field]: value,
      },
    }));
  };

  const DeviceIcon =
    deviceTypeIcons[formData.oneTimeItemData?.deviceType || "other"];

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <DeviceIcon className="h-5 w-5" />
            {mode === "add"
              ? t("stock.addOneTimeItem")
              : t("stock.editOneTimeItem")}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Product Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold border-b pb-2">
              {t("stock.basicInformation")}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">{t("stock.productName")} *</Label>
                <Input
                  id="name"
                  value={formData.name}
                  onChange={(e) => updateFormData("name", e.target.value)}
                  placeholder={t("stock.enterProductName")}
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="sku">{t("stock.sku")} *</Label>
                <Input
                  id="sku"
                  value={formData.sku}
                  onChange={(e) => updateFormData("sku", e.target.value)}
                  placeholder="OTI-123456"
                  required
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="category">{t("stock.category")} *</Label>
                <Select
                  value={formData.categoryId}
                  onValueChange={(value) => updateFormData("categoryId", value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder={t("stock.selectCategory")} />
                  </SelectTrigger>
                  <SelectContent>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="barcode">{t("stock.barcode")}</Label>
                <Input
                  id="barcode"
                  value={formData.barcode || ""}
                  onChange={(e) => updateFormData("barcode", e.target.value)}
                  placeholder={t("stock.enterBarcode")}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">{t("stock.description")}</Label>
              <Textarea
                id="description"
                value={formData.description || ""}
                onChange={(e) => updateFormData("description", e.target.value)}
                placeholder={t("stock.enterDescription")}
                rows={2}
              />
            </div>
          </div>

          {/* Device Specific Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold border-b pb-2 flex items-center gap-2">
              <DeviceIcon className="h-5 w-5" />
              {t("stock.deviceInformation")}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="deviceType">{t("stock.deviceType")} *</Label>
                <Select
                  value={formData.oneTimeItemData?.deviceType}
                  onValueChange={(value) =>
                    updateOneTimeItemData("deviceType", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="iphone">
                      <div className="flex items-center gap-2">
                        <Smartphone className="h-4 w-4" />
                        iPhone
                      </div>
                    </SelectItem>
                    <SelectItem value="android">
                      <div className="flex items-center gap-2">
                        <Smartphone className="h-4 w-4" />
                        Android Phone
                      </div>
                    </SelectItem>
                    <SelectItem value="mac">
                      <div className="flex items-center gap-2">
                        <Monitor className="h-4 w-4" />
                        Mac
                      </div>
                    </SelectItem>
                    <SelectItem value="pc">
                      <div className="flex items-center gap-2">
                        <Monitor className="h-4 w-4" />
                        PC
                      </div>
                    </SelectItem>
                    <SelectItem value="laptop">
                      <div className="flex items-center gap-2">
                        <Laptop className="h-4 w-4" />
                        Laptop
                      </div>
                    </SelectItem>
                    <SelectItem value="other">
                      <div className="flex items-center gap-2">
                        <HardDrive className="h-4 w-4" />
                        Other
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="condition">{t("stock.condition")} *</Label>
                <Select
                  value={formData.oneTimeItemData?.condition}
                  onValueChange={(value) =>
                    updateOneTimeItemData("condition", value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="excellent">
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-green-600" />
                        <span>{t("stock.excellent")}</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="good">
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-blue-600" />
                        <span>{t("stock.good")}</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="fair">
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-yellow-600" />
                        <span>{t("stock.fair")}</span>
                      </div>
                    </SelectItem>
                    <SelectItem value="poor">
                      <div className="flex items-center gap-2">
                        <Star className="h-4 w-4 text-red-600" />
                        <span>{t("stock.poor")}</span>
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label htmlFor="serialNumber">
                  <div className="flex items-center gap-2">
                    <Hash className="h-4 w-4" />
                    {formData.oneTimeItemData?.deviceType === "iphone" ||
                    formData.oneTimeItemData?.deviceType === "android"
                      ? t("stock.imei")
                      : t("stock.serialNumber")}
                  </div>
                </Label>
                <Input
                  id="serialNumber"
                  value={formData.oneTimeItemData?.serialNumber || ""}
                  onChange={(e) =>
                    updateOneTimeItemData("serialNumber", e.target.value)
                  }
                  placeholder={
                    formData.oneTimeItemData?.deviceType === "iphone" ||
                    formData.oneTimeItemData?.deviceType === "android"
                      ? t("stock.enterImei")
                      : t("stock.enterSerialNumber")
                  }
                />
              </div>

              {(formData.oneTimeItemData?.deviceType === "iphone" ||
                formData.oneTimeItemData?.deviceType === "android" ||
                formData.oneTimeItemData?.deviceType === "laptop") && (
                <div className="space-y-2">
                  <Label htmlFor="batteryPercentage">
                    <div className="flex items-center gap-2">
                      <Battery className="h-4 w-4" />
                      {t("stock.batteryPercentage")}
                    </div>
                  </Label>
                  <Input
                    id="batteryPercentage"
                    type="number"
                    min="0"
                    max="100"
                    value={formData.oneTimeItemData?.batteryPercentage || ""}
                    onChange={(e) =>
                      updateOneTimeItemData(
                        "batteryPercentage",
                        e.target.value ? parseInt(e.target.value) : undefined
                      )
                    }
                    placeholder="85"
                  />
                </div>
              )}

              <div className="space-y-2">
                <Label htmlFor="model">{t("stock.model")}</Label>
                <Input
                  id="model"
                  value={formData.oneTimeItemData?.model || ""}
                  onChange={(e) =>
                    updateOneTimeItemData("model", e.target.value)
                  }
                  placeholder={
                    formData.oneTimeItemData?.deviceType === "iphone"
                      ? "iPhone 15 Pro"
                      : formData.oneTimeItemData?.deviceType === "android"
                      ? "Samsung Galaxy S24"
                      : formData.oneTimeItemData?.deviceType === "mac"
                      ? "MacBook Pro M3"
                      : t("stock.enterModel")
                  }
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="storage">
                  <div className="flex items-center gap-2">
                    <HardDrive className="h-4 w-4" />
                    {t("stock.storage")}
                  </div>
                </Label>
                <Input
                  id="storage"
                  value={formData.oneTimeItemData?.storage || ""}
                  onChange={(e) =>
                    updateOneTimeItemData("storage", e.target.value)
                  }
                  placeholder="256GB"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="color">
                  <div className="flex items-center gap-2">
                    <Palette className="h-4 w-4" />
                    {t("stock.color")}
                  </div>
                </Label>
                <Input
                  id="color"
                  value={formData.oneTimeItemData?.color || ""}
                  onChange={(e) =>
                    updateOneTimeItemData("color", e.target.value)
                  }
                  placeholder={t("stock.enterColor")}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="notes">
                <div className="flex items-center gap-2">
                  <FileText className="h-4 w-4" />
                  {t("stock.notes")}
                </div>
              </Label>
              <Textarea
                id="notes"
                value={formData.oneTimeItemData?.notes || ""}
                onChange={(e) => updateOneTimeItemData("notes", e.target.value)}
                placeholder={t("stock.enterNotes")}
                rows={3}
              />
            </div>
          </div>

          {/* Pricing Information */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold border-b pb-2">
              {t("stock.pricingInformation")}
            </h3>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="cost">{t("stock.cost")} (TND)</Label>
                <Input
                  id="cost"
                  type="number"
                  step="0.001"
                  min="0"
                  value={formData.cost}
                  onChange={(e) =>
                    updateFormData("cost", parseFloat(e.target.value) || 0)
                  }
                  placeholder="0.000"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="price">{t("stock.sellingPrice")} (TND) *</Label>
                <Input
                  id="price"
                  type="number"
                  step="0.001"
                  min="0"
                  value={formData.price}
                  onChange={(e) =>
                    updateFormData("price", parseFloat(e.target.value) || 0)
                  }
                  placeholder="0.000"
                  required
                />
              </div>
            </div>

            {formData.price > 0 && formData.cost > 0 && (
              <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
                <div className="flex justify-between items-center text-sm">
                  <span>{t("stock.profit")}:</span>
                  <span className="font-medium text-green-600">
                    {(formData.price - formData.cost).toFixed(3)} TND
                  </span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  <span>{t("stock.margin")}:</span>
                  <span className="font-medium">
                    {(
                      ((formData.price - formData.cost) / formData.price) *
                      100
                    ).toFixed(1)}
                    %
                  </span>
                </div>
              </div>
            )}
          </div>

          {/* Status */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="isActive"
                checked={formData.isActive}
                onCheckedChange={(checked) =>
                  updateFormData("isActive", checked)
                }
              />
              <Label htmlFor="isActive">{t("stock.activeProduct")}</Label>
            </div>
          </div>

          {/* Preview */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold border-b pb-2">
              {t("stock.preview")}
            </h3>
            <div className="p-4 border rounded-lg bg-gray-50 dark:bg-gray-800">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <DeviceIcon className="h-5 w-5" />
                    <h4 className="font-semibold">
                      {formData.name || t("stock.productName")}
                    </h4>
                    <Badge className="text-xs">{t("stock.oneTimeItem")}</Badge>
                  </div>
                  <div className="space-y-1 text-sm text-gray-600 dark:text-gray-400">
                    <p>SKU: {formData.sku}</p>
                    {formData.oneTimeItemData?.model && (
                      <p>Model: {formData.oneTimeItemData.model}</p>
                    )}
                    {formData.oneTimeItemData?.serialNumber && (
                      <p>
                        {formData.oneTimeItemData.deviceType === "iphone" ||
                        formData.oneTimeItemData.deviceType === "android"
                          ? "IMEI"
                          : "Serial"}
                        : {formData.oneTimeItemData.serialNumber}
                      </p>
                    )}
                    {formData.oneTimeItemData?.storage && (
                      <p>Storage: {formData.oneTimeItemData.storage}</p>
                    )}
                    {formData.oneTimeItemData?.color && (
                      <p>Color: {formData.oneTimeItemData.color}</p>
                    )}
                    {formData.oneTimeItemData?.batteryPercentage && (
                      <p>
                        Battery: {formData.oneTimeItemData.batteryPercentage}%
                      </p>
                    )}
                  </div>
                </div>
                <div className="text-right">
                  <div className="text-lg font-bold text-green-600">
                    {formData.price.toFixed(3)} TND
                  </div>
                  <Badge
                    className={`text-xs ${
                      conditionColors[
                        formData.oneTimeItemData?.condition || "good"
                      ]
                    }`}
                  >
                    {t(
                      `stock.${formData.oneTimeItemData?.condition || "good"}`
                    )}
                  </Badge>
                </div>
              </div>
            </div>
          </div>
        </form>

        <DialogFooter>
          <Button
            type="button"
            variant="outline"
            onClick={() => onOpenChange(false)}
            disabled={loading}
          >
            {t("common.cancel")}
          </Button>
          <Button type="submit" onClick={handleSubmit} disabled={loading}>
            {loading
              ? t("common.saving")
              : mode === "add"
              ? t("stock.addItem")
              : t("stock.updateItem")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default OneTimeItemFormDialog;
