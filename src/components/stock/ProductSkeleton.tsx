import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

interface ProductSkeletonProps {
  touchMode?: boolean;
  cardPadding?: string;
}

export const ProductSkeleton: React.FC<ProductSkeletonProps> = ({ 
  touchMode = false, 
  cardPadding = "p-4" 
}) => {
  return (
    <Card className={`${touchMode ? "min-h-[120px]" : "min-h-[100px]"}`}>
      <CardContent className={cardPadding}>
        <div className="flex flex-col h-full">
          <div className="flex-1">
            <Skeleton className={`${touchMode ? "h-5" : "h-4"} w-3/4 mb-2`} />
            <Skeleton className={`${touchMode ? "h-4" : "h-3"} w-1/2`} />
          </div>
          <div className="flex justify-between items-center mt-2">
            <Skeleton className={`${touchMode ? "h-6" : "h-5"} w-20`} />
            <Skeleton className="h-6 w-12 rounded-full" />
          </div>
        </div>
      </CardContent>
    </Card>
  );
};