import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";

interface ItemDiscountFormProps {
  onApply: (discount: number, discountType: "percentage" | "fixed") => void;
  onCancel: () => void;
}

const ItemDiscountForm: React.FC<ItemDiscountFormProps> = ({ onApply, onCancel }) => {
  const { t } = useTranslation();
  const [discountType, setDiscountType] = useState<"percentage" | "fixed">("percentage");
  const [discountValue, setDiscountValue] = useState<number>(0);

  const handleApply = () => {
    if (discountValue > 0) {
      onApply(discountValue, discountType);
    }
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <label className="text-sm font-medium">{t("stock.discountType")}</label>
        <div className="flex gap-2">
          <Button
            variant={discountType === "percentage" ? "default" : "outline"}
            size="sm"
            onClick={() => setDiscountType("percentage")}
            className="flex-1"
          >
            {t("stock.percentage")}
          </Button>
          <Button
            variant={discountType === "fixed" ? "default" : "outline"}
            size="sm"
            onClick={() => setDiscountType("fixed")}
            className="flex-1"
          >
            {t("stock.fixedAmount")}
          </Button>
        </div>
      </div>
      
      <div className="space-y-2">
        <label className="text-sm font-medium">{t("stock.discountValue")}</label>
        <Input
          type="number"
          value={discountValue}
          onChange={(e) => setDiscountValue(Number(e.target.value))}
          placeholder={discountType === "percentage" ? "0-100" : "0.000"}
          min="0"
          max={discountType === "percentage" ? "100" : undefined}
          step={discountType === "percentage" ? "1" : "0.001"}
        />
      </div>
      
      <div className="flex gap-2">
        <Button
          variant="outline"
          onClick={onCancel}
          className="flex-1"
        >
          {t("common.cancel")}
        </Button>
        <Button
          onClick={handleApply}
          className="flex-1"
          disabled={discountValue <= 0}
        >
          {t("stock.applyDiscount")}
        </Button>
      </div>
    </div>
  );
};

export default ItemDiscountForm;
