import React, { useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Printer,
  Download,
  QrCode,
  RefreshCw,
  Save,
  AlertCircle,
} from "lucide-react";
import { Product } from "@/types/stock";
import { useStock } from "@/context/StockContext";
import { generateProductEAN13Barcode } from "@/utils/barcodeUtils";
import BarcodeSticker from "./BarcodeSticker";
import { toast } from "sonner";

interface BarcodeStickerDialogProps {
  product: Product;
  trigger?: React.ReactNode;
  onBarcodeGenerated?: (barcode: string) => void;
}

const BarcodeStickerDialog: React.FC<BarcodeStickerDialogProps> = ({
  product,
  trigger,
  onBarcodeGenerated,
}) => {
  const { t } = useTranslation();
  const { updateProduct } = useStock();
  const [open, setOpen] = useState(false);
  const [generatedBarcode, setGeneratedBarcode] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [stickerCount, setStickerCount] = useState(1);
  const stickerRef = useRef<HTMLDivElement>(null);

  const handlePrint = () => {
    if (!stickerRef.current) return;

    const printWindow = window.open("", "_blank");
    if (!printWindow) return;

    const stickerHTML = stickerRef.current.outerHTML;

    // Generate multiple stickers if count > 1
    let stickersHTML = "";
    for (let i = 0; i < stickerCount; i++) {
      stickersHTML += `
        <div class="sticker-item${
          i === stickerCount - 1 ? " last-sticker" : ""
        }" style="page-break-before: auto !important; break-before: auto !important;">
          <div class="sticker-content">
            ${stickerHTML}
          </div>
        </div>
      `;
    }

    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Barcode Sticker - ${product.name}</title>
          <style>
            * {
              margin: 0;
              padding: 0;
              box-sizing: border-box;
            }
            
            html, body {
              margin: 0;
              padding: 0;
              font-family: Arial, sans-serif;
              background: white;
              width: 100%;
              height: 100%;
              text-align: center;
            }
            
            .sticker-item {
              width: 100%;
              height: 100%;
              margin: 0;
              padding: 0;
              break-after: auto;
              break-inside: avoid;
              break-before: auto;
              page-break-after: auto;
              page-break-inside: avoid;
              page-break-before: auto;
              display: block;
              position: relative;
              overflow: hidden;
              transform: scale(1) !important;
            }
            
            .last-sticker {
              break-after: auto;
            }
            
            .sticker-content {
              width: 100%;
              height: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            
            @media print {
              html, body {
                margin: 0 !important;
                padding: 0 !important;
                width: 40mm !important;
                height: 20mm !important;
                overflow: hidden !important;
                background: white !important;
              }
              
              /* Let the printer handle the page size and orientation */
              @page {
                margin: 0;
                padding: 0;
              }
              
              .sticker-item {
                width: 40mm !important;
                height: 20mm !important;
                margin: 0 !important;
                padding: 0 !important;
                break-after: auto !important;
                break-inside: avoid !important;
                break-before: auto !important;
                page-break-after: auto !important;
                page-break-inside: avoid !important;
                page-break-before: auto !important;
                display: block !important;
                position: relative !important;
                overflow: hidden !important;
                transform: scale(1) !important;
              }
              
              .last-sticker {
                break-after: auto !important;
              }
              
              .sticker-content {
                width: 100% !important;
                height: 100% !important;
                display: flex !important;
                align-items: center !important;
                justify-content: center !important;
                margin: 0 !important;
                padding: 0 !important;
              }
              
              /* Ensure exact color reproduction */
              * {
                -webkit-print-color-adjust: exact !important;
                color-adjust: exact !important;
                print-color-adjust: exact !important;
              }
              
              /* Ensure stickers print at full size */
              .barcode-sticker {
                transform: scale(1) !important;
                zoom: 1 !important;
                -moz-transform: scale(1) !important;
                -webkit-transform: scale(1) !important;
              }
            }
          </style>
        </head>
        <body>
          ${stickersHTML}
          <script>
            window.onload = function() {
              // Small delay to ensure styles are applied
              setTimeout(function() {
                // Set print options programmatically if supported
                if (window.print) {
                  const mediaQueryList = window.matchMedia('print');
                  mediaQueryList.addListener(function(mql) {
                    if (!mql.matches) {
                      window.close();
                    }
                  });
                  window.print();
                  // Fallback for browsers that don't support the listener
                  window.onafterprint = function() {
                    window.close();
                  };
                }
              }, 100);
            };
          </script>
        </body>
      </html>
    `);
    printWindow.document.close();
  };

  const downloadSticker = () => {
    if (!stickerRef.current) return;

    // Create a canvas to render the sticker
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Set canvas size (40x20mm at 300 DPI)
    const dpi = 300;
    const mmToPx = dpi / 25.4;
    canvas.width = 40 * mmToPx;
    canvas.height = 20 * mmToPx;

    // Fill with white background
    ctx.fillStyle = "white";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Create a temporary div with the sticker content
    const tempDiv = document.createElement("div");
    tempDiv.innerHTML = stickerRef.current.outerHTML;
    tempDiv.style.position = "absolute";
    tempDiv.style.left = "-9999px";
    tempDiv.style.width = "40mm";
    tempDiv.style.height = "20mm";
    document.body.appendChild(tempDiv);

    // For a better implementation, you would use html2canvas library here
    // import html2canvas from 'html2canvas';
    // html2canvas(tempDiv, {
    //   width: canvas.width,
    //   height: canvas.height,
    //   scale: 1,
    //   useCORS: true,
    //   allowTaint: true
    // }).then(canvas => {
    //   // Download logic here
    // });

    // Simplified download for now
    const link = document.createElement("a");
    link.download = `barcode-sticker-${product.name
      .replace(/[^a-z0-9]/gi, "_")
      .toLowerCase()}.png`;

    // Convert canvas to blob and download
    canvas.toBlob((blob) => {
      if (blob) {
        const url = URL.createObjectURL(blob);
        link.href = url;
        link.click();
        URL.revokeObjectURL(url);
      }
    });

    document.body.removeChild(tempDiv);
  };

  const generateNewBarcode = async () => {
    try {
      setIsGenerating(true);
      const result = await generateProductEAN13Barcode(product.id);
      setGeneratedBarcode(result.barcode);
      toast.success(t("stock.barcodeGenerated"));
    } catch (error) {
      console.error("Error generating barcode:", error);
      toast.error(t("stock.failedToGenerateBarcode"));
    } finally {
      setIsGenerating(false);
    }
  };

  const saveBarcode = async () => {
    if (!generatedBarcode) {
      toast.error(t("stock.noBarcodeToSave"));
      return;
    }

    try {
      setIsSaving(true);
      await updateProduct(product.id, {
        name: product.name,
        description: product.description,
        sku: product.sku,
        barcode: generatedBarcode,
        categoryId: product.categoryId,
        price: product.price,
        cost: product.cost,
        stockQuantity: product.stockQuantity,
        minStockLevel: product.minStockLevel,
        maxStockLevel: product.maxStockLevel,
        isActive: product.isActive,
        imageUrl: product.imageUrl,
      });

      onBarcodeGenerated?.(generatedBarcode);
      toast.success(t("stock.barcodeSaved"));
      setOpen(false);
    } catch (error) {
      console.error("Error saving barcode:", error);
      toast.error(t("stock.failedToSaveBarcode"));
    } finally {
      setIsSaving(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        {trigger || (
          <Button variant="outline" size="sm">
            <QrCode className="h-4 w-4 mr-2" />
            {t("stock.printSticker")}
          </Button>
        )}
      </DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            {t("stock.barcodeSticker")} - {product.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Barcode Generation for products without barcodes */}
          {!product.barcode && !generatedBarcode && (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <AlertCircle className="h-4 w-4 text-amber-500" />
                  <span className="text-sm">
                    {t("stock.generateNewBarcode")}
                  </span>
                </div>
                <Button
                  onClick={generateNewBarcode}
                  disabled={isGenerating}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <QrCode className="h-4 w-4 mr-2" />
                      {t("stock.generateEAN13Barcode")}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Sticker Preview - Show if product has barcode or barcode was generated */}
          {(product.barcode || generatedBarcode) && (
            <Card>
              <CardContent className="p-4">
                <div className="space-y-4">
                  {/* Print Settings Info */}
                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      <strong>Print Settings:</strong> Set your printer to 40mm
                      x 20mm label size with no margins in portrait orientation.
                      For label printers, select "Continuous" or "Roll" mode
                      with no gaps between labels. Each sticker will print on a
                      separate label without leaving empty stickers in between.
                      For best results, use a dedicated label printer.
                    </AlertDescription>
                  </Alert>

                  {/* Sticker Count */}
                  <div className="flex items-center gap-4">
                    <Label
                      htmlFor="stickerCount"
                      className="text-sm font-medium"
                    >
                      {t("stock.copies")}
                    </Label>
                    <Input
                      id="stickerCount"
                      type="number"
                      min="1"
                      max="50"
                      value={stickerCount}
                      onChange={(e) =>
                        setStickerCount(
                          Math.max(1, parseInt(e.target.value) || 1)
                        )
                      }
                      className="w-20"
                    />
                  </div>

                  {/* Sticker Preview */}
                  <div className="flex justify-center bg-gray-50 p-4 rounded-lg">
                    <div className="border-2 border-dashed border-gray-300 p-2">
                      <BarcodeSticker
                        ref={stickerRef}
                        product={product}
                        barcode={generatedBarcode || product.barcode}
                      />
                    </div>
                  </div>

                  {/* Actions */}
                  <div className="flex flex-wrap gap-3">
                    <Button
                      onClick={handlePrint}
                      className="flex items-center gap-2"
                    >
                      <Printer className="h-4 w-4" />
                      {t("stock.printSticker")}
                    </Button>

                    <Button
                      onClick={downloadSticker}
                      variant="outline"
                      className="flex items-center gap-2"
                    >
                      <Download className="h-4 w-4" />
                      {t("stock.downloadSticker")}
                    </Button>

                    {/* Save barcode button - only show if barcode was generated and not saved yet */}
                    {generatedBarcode && !product.barcode && (
                      <Button
                        onClick={saveBarcode}
                        disabled={isSaving}
                        variant="secondary"
                        className="flex items-center gap-2"
                      >
                        {isSaving ? (
                          <>
                            <RefreshCw className="h-4 w-4 animate-spin" />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="h-4 w-4" />
                            {t("stock.saveBarcodeToProduct")}
                          </>
                        )}
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BarcodeStickerDialog;
