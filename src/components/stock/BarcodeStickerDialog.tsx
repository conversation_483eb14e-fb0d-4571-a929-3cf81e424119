import React, { useState, useRef } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  QrCode,
  Printer,
  Download,
  RefreshCw,
  Save,
  AlertCircle,
} from "lucide-react";
import { Product } from "@/types/stock";
import { useStock } from "@/context/StockContext";
import { generateProductEAN13Barcode } from "@/utils/barcodeUtils";
import BarcodeSticker from "./BarcodeSticker";
import { toast } from "sonner";

interface BarcodeStickerDialogProps {
  product: Product;
  trigger?: React.ReactNode;
  onBarcodeGenerated?: (barcode: string) => void;
}

const BarcodeStickerDialog: React.FC<BarcodeStickerDialogProps> = ({
  product,
  trigger,
  onBarcodeGenerated,
}) => {
  const { t } = useTranslation();
  const { updateProduct } = useStock();
  const [open, setOpen] = useState(false);
  const [generatedBarcode, setGeneratedBarcode] = useState<string>("");
  const [isGenerating, setIsGenerating] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [stickerCount, setStickerCount] = useState(1);
  const stickerRef = useRef<HTMLDivElement>(null);

  // Print function using browser's native print functionality
  const handlePrint = () => {
    if (!stickerRef.current) {
      toast.error("Sticker content not ready for printing");
      return;
    }

    // Get all the styles from the current document
    const styles = Array.from(document.styleSheets)
      .map((styleSheet) => {
        try {
          return Array.from(styleSheet.cssRules)
            .map((rule) => rule.cssText)
            .join("\n");
        } catch (e) {
          // Silently handle cross-origin stylesheet issues
          return "";
        }
      })
      .join("\n");

    // Create a new window with just the sticker content
    const printWindow = window.open("", "_blank", "width=800,height=600");
    if (!printWindow) {
      toast.error("Please allow popups to enable printing");
      return;
    }

    const stickerHTML = stickerRef.current.innerHTML;

    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>Barcode Sticker - ${product.name}</title>
          <meta charset="utf-8">
          <style>
            ${styles}

            @page {
              size: 2.5in 1.5in;
              margin: 0.1in;
            }

            @media print {
              body {
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                margin: 0;
                padding: 0;
              }
              .no-print {
                display: none !important;
              }
            }

            body {
              font-family: Arial, sans-serif;
              margin: 0;
              padding: 20px;
              background: white;
            }

            .print-container {
              width: 2.5in;
              height: 1.5in;
              border: 1px solid #ccc;
              padding: 0.1in;
              box-sizing: border-box;
              background: white;
              margin: 0 auto;
            }

            .print-button {
              margin: 20px auto;
              display: block;
              padding: 10px 20px;
              background: #007bff;
              color: white;
              border: none;
              border-radius: 4px;
              cursor: pointer;
            }
          </style>
        </head>
        <body>
          <button class="print-button no-print" onclick="window.print()">Print Sticker</button>
          <div class="print-container">
            ${stickerHTML}
          </div>
          <script>
            // Auto-focus for better UX
            window.focus();
          </script>
        </body>
      </html>
    `);

    printWindow.document.close();

    // Give the window time to load
    setTimeout(() => {
      printWindow.focus();
    }, 100);
  };

  const generateNewBarcode = async () => {
    try {
      setIsGenerating(true);
      const result = await generateProductEAN13Barcode(product.id);
      setGeneratedBarcode(result.barcode);
      toast.success(t("stock.barcodeGenerated"));
    } catch (error) {
      console.error("Error generating barcode:", error);
      toast.error(t("stock.failedToGenerateBarcode"));
    } finally {
      setIsGenerating(false);
    }
  };

  const saveBarcode = async () => {
    if (!generatedBarcode) {
      toast.error(t("stock.noBarcodeToSave"));
      return;
    }

    try {
      setIsSaving(true);
      await updateProduct(product.id, {
        name: product.name,
        description: product.description,
        sku: product.sku,
        barcode: generatedBarcode,
        categoryId: product.categoryId,
        price: product.price,
        cost: product.cost,
        stockQuantity: product.stockQuantity,
        minStockLevel: product.minStockLevel,
        maxStockLevel: product.maxStockLevel,
        isActive: product.isActive,
        imageUrl: product.imageUrl,
      });

      onBarcodeGenerated?.(generatedBarcode);
      toast.success(t("stock.barcodeSaved"));
      setOpen(false);
    } catch (error) {
      console.error("Error saving barcode:", error);
      toast.error(t("stock.failedToSaveBarcode"));
    } finally {
      setIsSaving(false);
    }
  };

  const downloadSticker = () => {
    if (!stickerRef.current) return;

    // Create a canvas to render the sticker
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    if (!ctx) return;

    // Set canvas size (2.5in x 1.5in at 300 DPI)
    canvas.width = 750; // 2.5 * 300
    canvas.height = 450; // 1.5 * 300

    // Convert the sticker div to canvas (simplified approach)
    // In a real implementation, you might want to use html2canvas library
    ctx.fillStyle = "white";
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Add text
    ctx.fillStyle = "black";
    ctx.font = "24px Arial";
    ctx.textAlign = "center";
    ctx.fillText(product.name, canvas.width / 2, 50);

    // Add price
    ctx.font = "18px Arial";
    ctx.fillText(
      `${product.price.toFixed(3)} TND`,
      canvas.width / 2,
      canvas.height - 30
    );

    // Download the canvas as image
    const link = document.createElement("a");
    link.download = `barcode-sticker-${product.sku}.png`;
    link.href = canvas.toDataURL();
    link.click();
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm" className="flex items-center gap-2">
      <QrCode className="h-4 w-4" />
      {t("stock.printSticker")}
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <QrCode className="h-5 w-5" />
            {t("stock.barcodeSticker")} - {product.name}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Product Info */}
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold">{product.name}</h3>
                  <p className="text-sm text-gray-600">{product.description}</p>
                  <p className="text-sm font-mono">SKU: {product.sku}</p>
                </div>
                <div className="text-right">
                  <p className="font-bold text-green-600">
                    {product.price.toFixed(3)} TND
                  </p>
                  {product.barcode ? (
                    <Badge variant="secondary">
                      {t("stock.productHasBarcode")}
                    </Badge>
                  ) : (
                    <Badge variant="outline">
                      {t("stock.productNoBarcode")}
                    </Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Barcode Generation for products without barcodes */}
          {!product.barcode && !generatedBarcode && (
            <Card>
              <CardContent className="p-4">
                <div className="flex items-center gap-2 mb-3">
                  <AlertCircle className="h-4 w-4 text-amber-500" />
                  <span className="text-sm">
                    {t("stock.generateNewBarcode")}
                  </span>
                </div>
                <Button
                  onClick={generateNewBarcode}
                  disabled={isGenerating}
                  className="w-full"
                >
                  {isGenerating ? (
                    <>
                      <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                      Generating...
                    </>
                  ) : (
                    <>
                      <QrCode className="h-4 w-4 mr-2" />
                      {t("stock.generateEAN13Barcode")}
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          )}

          {/* Sticker Preview - Show if product has barcode or barcode was generated */}
          {(product.barcode || generatedBarcode) && (
            <>
              <Card>
                <CardContent className="p-4">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="font-semibold">
                      {t("stock.stickerPreview")}
                    </h3>
                    <div className="flex items-center gap-2">
                      <Label htmlFor="stickerCount" className="text-sm">
                        {t("stock.copies")}:
                      </Label>
                      <Input
                        id="stickerCount"
                        type="number"
                        min="1"
                        max="50"
                        value={stickerCount}
                        onChange={(e) =>
                          setStickerCount(parseInt(e.target.value) || 1)
                        }
                        className="w-20"
                      />
                    </div>
                  </div>

                  <div className="flex justify-center">
                    <BarcodeSticker
                      ref={stickerRef}
                      product={product}
                      barcode={generatedBarcode || product.barcode}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Actions */}
              <div className="flex flex-wrap gap-3">
                <Button
                  onClick={handlePrint}
                  className="flex items-center gap-2"
                >
                  <Printer className="h-4 w-4" />
                  {t("stock.printSticker")}
                </Button>

                <Button
                  onClick={downloadSticker}
                  variant="outline"
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  {t("stock.downloadSticker")}
                </Button>

                {/* Save barcode button - only show if barcode was generated and not saved yet */}
                {generatedBarcode && !product.barcode && (
                  <Button
                    onClick={saveBarcode}
                    disabled={isSaving}
                    variant="secondary"
                    className="flex items-center gap-2"
                  >
                    {isSaving ? (
                      <>
                        <RefreshCw className="h-4 w-4 animate-spin" />
                        Saving...
                      </>
                    ) : (
                      <>
                        <Save className="h-4 w-4" />
                        {t("stock.saveBarcodeToProduct")}
                      </>
                    )}
                  </Button>
                )}
              </div>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BarcodeStickerDialog;
