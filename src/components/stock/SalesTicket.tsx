import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Sale, SaleItem } from "@/types/stock";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { useAuth } from "@/context/AuthContext";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { supabase } from "@/integrations/supabase/client";

interface SalesTicketProps {
  sale: Sale;
  saleItems: SaleItem[];
  onPrint?: () => void;
}

const SalesTicket: React.FC<SalesTicketProps> = ({ sale, saleItems }) => {
  const { t } = useTranslation();
  const { repairShop } = useRepairShopContext();
  const { user } = useAuth();
  const [userFullName, setUserFullName] = useState<string>("");

  useEffect(() => {
    const fetchUserFullName = async () => {
      if (!user || !repairShop) return;

      try {
        const { data, error } = await supabase
          .from("user_repair_shops")
          .select("full_name")
          .eq("user_id", user.id)
          .single();

        if (!error && data) {
          setUserFullName(data.full_name || user.email || "");
        }
      } catch (error) {
        console.error("Error fetching user full name:", error);
      }
    };

    fetchUserFullName();
  }, [user, repairShop]);

  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(3)} TND`;
  };

  const formatDate = (date: string | Date) => {
    const dateObj = typeof date === "string" ? new Date(date) : date;
    return dateObj.toLocaleString("fr-FR", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      hour12: false,
    });
  };

  return (
    <div className="max-w-sm mx-auto bg-white text-black p-4 font-mono text-sm print:p-2 print:text-xs">
      {/* Header */}
      <div className="text-center border-b-2 border-black pb-3 mb-3">
        {/* Store Logo */}
        <div className="mb-2">
          <img
            src="/logo-print.png"
            alt="Store Logo"
            className="h-12 w-auto mx-auto print:h-8"
            onError={(e) => {
              // Hide image if logo doesn't exist
              e.currentTarget.style.display = "none";
            }}
          />
        </div>

        {/* Store Name */}
        <h1 className="text-lg font-bold print:text-base">
          {repairShop?.name || "REPAIR SHOP"}
        </h1>

        {/* Store Address */}
        {repairShop?.address && (
          <p className="text-xs mt-1 print:text-xs">{repairShop.address}</p>
        )}

        {/* Store Phone */}
        {repairShop?.phone && (
          <p className="text-xs print:text-xs">Tel: {repairShop.phone}</p>
        )}
      </div>

      {/* Sale Info */}
      <div className="mb-3 text-xs">
        <div className="flex justify-between">
          <span>{t("stock.saleNumber")}:</span>
          <span className="font-bold">{sale.saleNumber}</span>
        </div>
        <div className="flex justify-between">
          <span>{t("stock.date")}:</span>
          <span>{formatDate(sale.createdAt)}</span>
        </div>
        {sale.customerName && (
          <div className="flex justify-between">
            <span>{t("stock.customer")}:</span>
            <span>{sale.customerName}</span>
          </div>
        )}
        {sale.customerPhone && (
          <div className="flex justify-between">
            <span>{t("stock.phone")}:</span>
            <span>{sale.customerPhone}</span>
          </div>
        )}
      </div>

      {/* Items */}
      <div className="border-t border-black pt-2 mb-3">
        <h3 className="font-bold text-center mb-2">{t("stock.items")}</h3>

        {saleItems.map((item, index) => (
          <div key={index} className="mb-2 text-xs">
            <div className="flex justify-between">
              <span className="flex-1 pr-2">
                {item.product?.name || "Product"}
              </span>
              <span>{item.quantity}x</span>
            </div>
            <div className="flex justify-between text-xs">
              <span>{formatCurrency(item.unitPrice)} each</span>
              <span className="font-bold">
                {formatCurrency(item.totalPrice)}
              </span>
            </div>
            {item.discount > 0 && (
              <div className="text-xs text-gray-600">
                Discount: -{formatCurrency(item.discount * item.quantity)}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Totals */}
      <div className="border-t-2 border-black pt-2 mb-3">
        <div className="flex justify-between text-xs">
          <span>{t("stock.subtotal")}:</span>
          <span>{formatCurrency(sale.subtotal)}</span>
        </div>

        {sale.discountAmount > 0 && (
          <div className="flex justify-between text-xs">
            <span>{t("stock.discount")}:</span>
            <span>-{formatCurrency(sale.discountAmount)}</span>
          </div>
        )}

        <div className="flex justify-between font-bold text-sm border-t border-black pt-1 mt-1">
          <span>{t("stock.total")}:</span>
          <span>{formatCurrency(sale.totalAmount)}</span>
        </div>
      </div>

      {/* Payment Info */}
      <div className="border-t border-black pt-2 mb-3 text-xs">
        <div className="flex justify-between">
          <span>{t("stock.paymentMethod")}:</span>
          <span className="capitalize">{t(`stock.${sale.paymentMethod}`)}</span>
        </div>

        <div className="flex justify-between">
          <span>{t("stock.amountPaid")}:</span>
          <span>{formatCurrency(sale.amountPaid)}</span>
        </div>

        {sale.changeAmount > 0 && (
          <div className="flex justify-between font-bold">
            <span>{t("stock.change")}:</span>
            <span>{formatCurrency(sale.changeAmount)}</span>
          </div>
        )}

        <div className="flex justify-between">
          <span>{t("stock.paymentStatus")}:</span>
          <span className="capitalize">{t(`stock.${sale.paymentStatus}`)}</span>
        </div>

        {userFullName && (
          <div className="flex justify-between mt-1">
            <span>{t("userManagement.cashier")}:</span>
            <span>{userFullName}</span>
          </div>
        )}
      </div>

      {/* Footer */}
      <div className="border-t-2 border-black pt-3 text-center text-xs">
        <p className="font-bold mb-1">{t("stock.thankYouVisit")}</p>
        <p className="text-xs">{t("stock.keepTicket")}</p>

        {/* QR Code placeholder - can be added later */}
        <div className="mt-2 text-xs">
          <p>Sale ID: {sale.id.slice(-8).toUpperCase()}</p>
        </div>
      </div>
    </div>
  );
};

export default SalesTicket;
