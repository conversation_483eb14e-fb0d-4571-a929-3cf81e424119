import React, { useState, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Scan, Camera, Keyboard, X } from "lucide-react";
import { useTranslation } from "react-i18next";
import { toast } from "sonner";

interface BarcodeScannerProps {
  onBarcodeScanned: (barcode: string) => void;
  touchMode?: boolean;
}

const BarcodeScanner: React.FC<BarcodeScannerProps> = ({
  onBarcodeScanned,
  touchMode = false,
}) => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const [scanMode, setScanMode] = useState<"camera" | "manual">("manual");
  const [manualBarcode, setManualBarcode] = useState("");
  const [isScanning, setIsScanning] = useState(false);
  const videoRef = useRef<HTMLVideoElement>(null);
  const streamRef = useRef<MediaStream | null>(null);

  // Cleanup camera stream when component unmounts or dialog closes
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, []);

  const stopCamera = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach((track) => track.stop());
      streamRef.current = null;
    }
    setIsScanning(false);
  };

  const startCamera = async () => {
    try {
      setIsScanning(true);

      // Request camera permission
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          facingMode: "environment", // Use back camera if available
          width: { ideal: 1280 },
          height: { ideal: 720 },
        },
      });

      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        videoRef.current.play();
      }

      toast.success(t("stock.cameraStarted"));
    } catch (error) {
      console.error("Error accessing camera:", error);
      toast.error(t("stock.cameraError"));
      setScanMode("manual");
      setIsScanning(false);
    }
  };

  const handleManualSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (manualBarcode.trim()) {
      onBarcodeScanned(manualBarcode.trim());
      setManualBarcode("");
      setIsOpen(false);
      toast.success("Barcode processed!");
    }
  };

  const handleDialogClose = () => {
    setIsOpen(false);
    stopCamera();
    setManualBarcode("");
    setScanMode("manual");
  };

  // Handle keyboard input for quick barcode entry
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter" && manualBarcode.trim()) {
      handleManualSubmit(e);
    }
  };

  const buttonSize = touchMode ? "lg" : "default";

  return (
    <Dialog open={isOpen} onOpenChange={handleDialogClose}>
      <DialogTrigger asChild>
        <Button
          size={buttonSize}
          className="flex items-center gap-2"
          variant="outline"
        >
          <Scan className="h-4 w-4" />
          {touchMode ? t("stock.scan") : t("stock.scanBarcode")}
        </Button>
      </DialogTrigger>

      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Scan className="h-5 w-5" />
            {t("stock.scanBarcode")}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-4">
          {/* Scan Mode Toggle */}
          <div className="flex gap-2">
            <Button
              variant={scanMode === "manual" ? "default" : "outline"}
              size="sm"
              onClick={() => {
                setScanMode("manual");
                stopCamera();
              }}
              className="flex-1"
            >
              <Keyboard className="h-4 w-4 mr-2" />
              {t("stock.manualEntry")}
            </Button>
            <Button
              variant={scanMode === "camera" ? "default" : "outline"}
              size="sm"
              onClick={() => {
                setScanMode("camera");
                startCamera();
              }}
              className="flex-1"
            >
              <Camera className="h-4 w-4 mr-2" />
              {t("stock.cameraScanner")}
            </Button>
          </div>

          {/* Manual Input Mode */}
          {scanMode === "manual" && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm">
                  {t("stock.enterBarcodeManually")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleManualSubmit} className="space-y-3">
                  <Input
                    type="text"
                    placeholder={t("stock.enterOrScanBarcode")}
                    value={manualBarcode}
                    onChange={(e) => setManualBarcode(e.target.value)}
                    onKeyDown={handleKeyDown}
                    autoFocus
                    className="font-mono"
                  />
                  <div className="flex gap-2">
                    <Button
                      type="submit"
                      disabled={!manualBarcode.trim()}
                      className="flex-1"
                    >
                      {t("stock.addToCart")}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setManualBarcode("")}
                    >
                      {t("common.clear")}
                    </Button>
                  </div>
                </form>
              </CardContent>
            </Card>
          )}

          {/* Camera Mode */}
          {scanMode === "camera" && (
            <Card>
              <CardHeader className="pb-3">
                <CardTitle className="text-sm flex items-center justify-between">
                  {t("stock.cameraScanner")}
                  {isScanning && (
                    <Button size="sm" variant="outline" onClick={stopCamera}>
                      <X className="h-4 w-4" />
                    </Button>
                  )}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {isScanning ? (
                  <div className="space-y-3">
                    <video
                      ref={videoRef}
                      className="w-full h-48 bg-black rounded-lg"
                      playsInline
                      muted
                    />
                    <p className="text-sm text-center text-gray-600">
                      {t("stock.pointCameraAtBarcode")}
                    </p>

                    {/* Manual input fallback while camera is active */}
                    <div className="border-t pt-3">
                      <Input
                        type="text"
                        placeholder={t("stock.orTypeBarcodeHere")}
                        value={manualBarcode}
                        onChange={(e) => setManualBarcode(e.target.value)}
                        onKeyDown={handleKeyDown}
                        className="font-mono text-sm"
                      />
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <Camera className="h-12 w-12 mx-auto text-gray-400 mb-3" />
                    <p className="text-sm text-gray-600 mb-3">
                      {t("stock.cameraNotStarted")}
                    </p>
                    <Button onClick={startCamera} size="sm">
                      {t("stock.startCamera")}
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Instructions */}
          <div className="text-xs text-gray-500 space-y-1">
            <p>• Use a barcode scanner device for fastest input</p>
            <p>• Camera scanning works with modern browsers</p>
            <p>• Manual input supports any barcode format</p>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default BarcodeScanner;
