import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Camera, Keyboard, Scan } from "lucide-react";
import QRScanner from "@/components/QRScanner";
import HenexScanner from "@/components/HenexScanner";

interface BarcodeScannerProps {
  onBarcodeScanned: (barcode: string) => void;
  touchMode?: boolean;
}

const BarcodeScanner: React.FC<BarcodeScannerProps> = ({
  onBarcodeScanned,
  touchMode = false,
}) => {
  const { t } = useTranslation();
  const [manualBarcode, setManualBarcode] = useState("");

  const handleManualSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (manualBarcode.trim()) {
      onBarcodeScanned(manualBarcode.trim());
      setManualBarcode("");
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Scan className="h-5 w-5" />
          {t("stock.barcodeScanner")}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="henex" className="w-full">
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="henex" className="flex items-center gap-2">
              <Scan className="h-4 w-4" />
              {touchMode ? t("scanner.henex") : t("scanner.henexScanner")}
            </TabsTrigger>
            <TabsTrigger value="camera" className="flex items-center gap-2">
              <Camera className="h-4 w-4" />
              {touchMode ? t("scanner.camera") : t("scanner.cameraScanner")}
            </TabsTrigger>
            <TabsTrigger value="manual" className="flex items-center gap-2">
              <Keyboard className="h-4 w-4" />
              {touchMode ? t("scanner.manual") : t("scanner.manualEntry")}
            </TabsTrigger>
          </TabsList>

          <TabsContent value="henex" className="mt-4">
            <div className="space-y-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t("scanner.henexInstructions")}
              </p>
              <HenexScanner
                onScan={onBarcodeScanned}
                autoStart={true}
                showStartButton={true}
              />
            </div>
          </TabsContent>

          <TabsContent value="camera" className="mt-4">
            <div className="space-y-4">
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t("scanner.cameraInstructions")}
              </p>
              <QRScanner onScan={onBarcodeScanned} />
            </div>
          </TabsContent>

          <TabsContent value="manual" className="mt-4">
            <form onSubmit={handleManualSubmit} className="space-y-4">
              <div>
                <Label htmlFor="manualBarcode">
                  {t("scanner.enterBarcode")}
                </Label>
                <Input
                  id="manualBarcode"
                  type="text"
                  value={manualBarcode}
                  onChange={(e) => setManualBarcode(e.target.value)}
                  placeholder={t("scanner.barcodePlaceholder")}
                  className="mt-1"
                />
              </div>
              <Button type="submit" disabled={!manualBarcode.trim()}>
                {t("scanner.addToCart")}
              </Button>
            </form>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
};

export default BarcodeScanner;
