import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Search,
  Edit,
  Trash2,
  Package,
  AlertTriangle,
  CheckCircle,
  Eye,
  EyeOff,
  QrCode,
} from "lucide-react";
import { Product } from "@/types/stock";
import { useStock } from "@/context/StockContext";
import ProductFormDialog from "./ProductFormDialog";
import BarcodeStickerDialog from "./BarcodeStickerDialog";
import StockPasswordDialog from "./StockPasswordDialog";
import { toast } from "sonner";

interface ProductManagementProps {
  viewMode: "grid" | "list";
  touchMode: boolean;
  onViewModeChange: (mode: "grid" | "list") => void;
}

const ProductManagement: React.FC<ProductManagementProps> = ({
  viewMode,
  touchMode,
}) => {
  const { t } = useTranslation();
  const { products, deleteProduct } = useStock();
  const [searchTerm, setSearchTerm] = useState("");
  const [showInactive, setShowInactive] = useState(false);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [productToDelete, setProductToDelete] = useState<string | null>(null);

  const filteredProducts = products.filter((product) => {
    const matchesSearch =
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.description?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = showInactive || product.isActive;

    return matchesSearch && matchesStatus;
  });

  const getStockStatus = (product: Product) => {
    if (product.stockQuantity === 0) {
      return {
        status: "out",
        color: "destructive",
        text: t("stock.outOfStock"),
      };
    } else if (product.stockQuantity <= product.minStockLevel) {
      return { status: "low", color: "warning", text: t("stock.lowStock") };
    } else {
      return { status: "good", color: "default", text: t("stock.stockLevel") };
    }
  };

  const handleDeleteProduct = (productId: string) => {
    setProductToDelete(productId);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = async () => {
    if (!productToDelete) return;

    try {
      await deleteProduct(productToDelete);
      toast.success(t("stock.productDeleted"));
    } catch (error) {
      console.error("Error deleting product:", error);
      toast.error(t("common.error"));
    } finally {
      setProductToDelete(null);
    }
  };

  const buttonSize = touchMode ? "lg" : "default";
  const cardPadding = touchMode ? "p-6" : "p-4";

  return (
    <div className="space-y-6">
      {/* Header Actions */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div className="flex items-center gap-4 flex-1">
          {/* Search */}
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder={t("stock.searchProducts")}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className={`pl-10 ${touchMode ? "h-12" : "h-10"}`}
            />
          </div>

          {/* Show Inactive Toggle */}
          <Button
            variant={showInactive ? "default" : "outline"}
            size={buttonSize}
            onClick={() => setShowInactive(!showInactive)}
            className="flex items-center gap-2"
          >
            {showInactive ? (
              <Eye className="h-4 w-4" />
            ) : (
              <EyeOff className="h-4 w-4" />
            )}
            <span className="hidden sm:inline">
              {showInactive ? t("stock.hideInactive") : t("stock.showInactive")}
            </span>
          </Button>
        </div>

        {/* Add Product Button */}
        <ProductFormDialog />
      </div>

      {/* Products Display */}
      {viewMode === "grid" ? (
        /* Grid View */
        <div
          className={`grid gap-4 ${
            touchMode
              ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
              : "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"
          }`}
        >
          {filteredProducts.map((product) => {
            const stockStatus = getStockStatus(product);
            return (
              <Card
                key={product.id}
                className={`${!product.isActive ? "opacity-60" : ""}`}
              >
                <CardContent className={cardPadding}>
                  <div className="space-y-3">
                    {/* Product Header */}
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <h3
                          className={`font-semibold ${
                            touchMode ? "text-base" : "text-sm"
                          } line-clamp-2`}
                        >
                          {product.name}
                        </h3>
                        <p
                          className={`text-gray-600 dark:text-gray-400 ${
                            touchMode ? "text-sm" : "text-xs"
                          } mt-1`}
                        >
                          {product.sku}
                        </p>
                      </div>
                      <div className="flex items-center gap-1">
                        {!product.isActive && (
                          <Badge variant="secondary" className="text-xs">
                            {t("stock.inactive")}
                          </Badge>
                        )}
                      </div>
                    </div>

                    {/* Description */}
                    <p
                      className={`text-gray-600 dark:text-gray-400 ${
                        touchMode ? "text-sm" : "text-xs"
                      } line-clamp-2`}
                    >
                      {product.description}
                    </p>

                    {/* Price & Stock */}
                    <div className="flex justify-between items-center">
                      <div>
                        <p
                          className={`font-bold text-green-600 ${
                            touchMode ? "text-lg" : "text-base"
                          }`}
                        >
                          {product.price.toFixed(3)} TND
                        </p>
                        <p
                          className={`text-gray-500 ${
                            touchMode ? "text-sm" : "text-xs"
                          }`}
                        >
                          Cost: {product.cost.toFixed(3)} TND
                        </p>
                      </div>
                      <div className="text-right">
                        <Badge
                          variant={
                            stockStatus.status === "out"
                              ? "destructive"
                              : stockStatus.status === "low"
                              ? "secondary"
                              : "default"
                          }
                          className="mb-1"
                        >
                          {product.stockQuantity}
                        </Badge>
                        <p
                          className={`text-gray-500 ${
                            touchMode ? "text-xs" : "text-xs"
                          }`}
                        >
                          Min: {product.minStockLevel}
                        </p>
                      </div>
                    </div>

                    {/* Stock Status Alert */}
                    {stockStatus.status !== "good" && (
                      <div
                        className={`flex items-center gap-2 p-2 rounded-lg ${
                          stockStatus.status === "out"
                            ? "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-400"
                            : "bg-yellow-50 dark:bg-yellow-900/20 text-yellow-700 dark:text-yellow-400"
                        }`}
                      >
                        <AlertTriangle className="h-4 w-4" />
                        <span className="text-xs font-medium">
                          {stockStatus.text}
                        </span>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="flex flex-col gap-2 pt-2">
                      <div className="flex gap-2">
                        <ProductFormDialog
                          product={product}
                          trigger={
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex-1"
                            >
                              <Edit className="h-3 w-3 mr-1" />
                              Edit
                            </Button>
                          }
                        />
                        <Button
                          variant="outline"
                          size="sm"
                          className="text-red-600 hover:text-red-700"
                          onClick={() => handleDeleteProduct(product.id)}
                        >
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>

                      {/* Barcode Sticker Button */}
                      <BarcodeStickerDialog
                        product={product}
                        onBarcodeGenerated={(_barcode) => {
                          // Refresh the product list to show the new barcode
                          window.location.reload();
                        }}
                        trigger={
                          <Button
                            variant={product.barcode ? "secondary" : "default"}
                            size="sm"
                            className="w-full flex items-center gap-2"
                          >
                            <QrCode className="h-3 w-3" />
                            {product.barcode
                              ? t("stock.printSticker")
                              : t("stock.generateSticker")}
                          </Button>
                        }
                      />
                    </div>
                  </div>
                </CardContent>
              </Card>
            );
          })}
        </div>
      ) : (
        /* List View */
        <Card>
          <CardContent className="p-0">
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead className="border-b">
                  <tr className="text-left">
                    <th className={`${touchMode ? "p-4" : "p-3"} font-medium`}>
                      {t("stock.product")}
                    </th>
                    <th className={`${touchMode ? "p-4" : "p-3"} font-medium`}>
                      {t("stock.sku")}
                    </th>
                    <th className={`${touchMode ? "p-4" : "p-3"} font-medium`}>
                      {t("stock.price")}
                    </th>
                    <th className={`${touchMode ? "p-4" : "p-3"} font-medium`}>
                      {t("stock.stock")}
                    </th>
                    <th className={`${touchMode ? "p-4" : "p-3"} font-medium`}>
                      {t("stock.status")}
                    </th>
                    <th className={`${touchMode ? "p-4" : "p-3"} font-medium`}>
                      {t("stock.actions")}
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {filteredProducts.map((product) => {
                    const stockStatus = getStockStatus(product);
                    return (
                      <tr
                        key={product.id}
                        className={`border-b hover:bg-gray-50 dark:hover:bg-gray-800/50 ${
                          !product.isActive ? "opacity-60" : ""
                        }`}
                      >
                        <td className={`${touchMode ? "p-4" : "p-3"}`}>
                          <div>
                            <h3 className="font-medium">{product.name}</h3>
                            <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-1">
                              {product.description}
                            </p>
                          </div>
                        </td>
                        <td
                          className={`${
                            touchMode ? "p-4" : "p-3"
                          } font-mono text-sm`}
                        >
                          {product.sku}
                        </td>
                        <td className={`${touchMode ? "p-4" : "p-3"}`}>
                          <div>
                            <p className="font-semibold text-green-600">
                              {product.price.toFixed(3)} TND
                            </p>
                            <p className="text-xs text-gray-500">
                              Cost: {product.cost.toFixed(3)} TND
                            </p>
                          </div>
                        </td>
                        <td className={`${touchMode ? "p-4" : "p-3"}`}>
                          <div className="flex items-center gap-2">
                            <Badge
                              variant={
                                stockStatus.status === "out"
                                  ? "destructive"
                                  : stockStatus.status === "low"
                                  ? "secondary"
                                  : "default"
                              }
                            >
                              {product.stockQuantity}
                            </Badge>
                            {stockStatus.status !== "good" && (
                              <AlertTriangle className="h-4 w-4 text-yellow-500" />
                            )}
                          </div>
                        </td>
                        <td className={`${touchMode ? "p-4" : "p-3"}`}>
                          <div className="flex items-center gap-2">
                            {product.isActive ? (
                              <CheckCircle className="h-4 w-4 text-green-500" />
                            ) : (
                              <Badge variant="secondary">
                                {t("stock.inactive")}
                              </Badge>
                            )}
                          </div>
                        </td>
                        <td className={`${touchMode ? "p-4" : "p-3"}`}>
                          <div className="flex gap-2">
                            <ProductFormDialog
                              product={product}
                              trigger={
                                <Button variant="outline" size="sm">
                                  <Edit className="h-3 w-3" />
                                </Button>
                              }
                            />

                            <BarcodeStickerDialog
                              product={product}
                              onBarcodeGenerated={(_barcode) => {
                                // Refresh the product list to show the new barcode
                                window.location.reload();
                              }}
                              trigger={
                                <Button
                                  variant={
                                    product.barcode ? "secondary" : "default"
                                  }
                                  size="sm"
                                  className="flex items-center gap-1"
                                >
                                  <QrCode className="h-3 w-3" />
                                  {touchMode && (
                                    <span className="hidden sm:inline">
                                      {product.barcode
                                        ? t("stock.printSticker")
                                        : t("stock.generateSticker")}
                                    </span>
                                  )}
                                </Button>
                              }
                            />

                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600 hover:text-red-700"
                              onClick={() => handleDeleteProduct(product.id)}
                            >
                              <Trash2 className="h-3 w-3" />
                            </Button>
                          </div>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Empty State */}
      {filteredProducts.length === 0 && (
        <Card>
          <CardContent className="text-center py-12">
            <Package className="h-12 w-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              No products found
            </h3>
            <p className="text-gray-600 dark:text-gray-400 mb-4">
              {searchTerm
                ? t("stock.tryAdjustingSearch")
                : t("stock.getStartedAddProduct")}
            </p>
            <ProductFormDialog />
          </CardContent>
        </Card>
      )}

      <StockPasswordDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        onConfirm={handleDeleteConfirm}
        title={t("stock.confirmProductDeletion")}
        description={t("stock.productDeletionWarning")}
        confirmButtonText={t("stock.deleteProduct")}
      />
    </div>
  );
};

export default ProductManagement;
