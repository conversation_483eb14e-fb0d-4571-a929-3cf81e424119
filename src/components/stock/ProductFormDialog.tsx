import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Plus, Package } from "lucide-react";
import { useStock } from "@/context/StockContext";
import { ProductFormData, Product } from "@/types/stock";
import { toast } from "sonner";
import StockPasswordDialog from "./StockPasswordDialog";

interface ProductFormDialogProps {
  product?: Product;
  trigger?: React.ReactNode;
  onSuccess?: () => void;
}

const ProductFormDialog: React.FC<ProductFormDialogProps> = ({
  product,
  trigger,
  onSuccess,
}) => {
  const { t } = useTranslation();
  const { categories, addProduct, updateProduct, loading } = useStock();
  const [open, setOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);
  const [pendingFormData, setPendingFormData] =
    useState<ProductFormData | null>(null);

  const [formData, setFormData] = useState<ProductFormData>({
    name: "",
    description: "",
    sku: "",
    barcode: "",
    categoryId: "",
    price: 0,
    cost: 0,
    stockQuantity: 0,
    minStockLevel: 0,
    maxStockLevel: undefined,
    isActive: true,
    imageUrl: "",
  });

  // Reset form when dialog opens/closes or product changes
  useEffect(() => {
    if (product) {
      setFormData({
        name: product.name,
        description: product.description || "",
        sku: product.sku,
        barcode: product.barcode || "",
        categoryId: product.categoryId,
        price: product.price,
        cost: product.cost,
        stockQuantity: product.stockQuantity,
        minStockLevel: product.minStockLevel,
        maxStockLevel: product.maxStockLevel,
        isActive: product.isActive,
        imageUrl: product.imageUrl || "",
      });
    } else {
      setFormData({
        name: "",
        description: "",
        sku: "",
        barcode: "",
        categoryId: "",
        price: 0,
        cost: 0,
        stockQuantity: 0,
        minStockLevel: 0,
        maxStockLevel: undefined,
        isActive: true,
        imageUrl: "",
      });
    }
  }, [product, open]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Product name is required");
      return;
    }

    if (!formData.sku.trim()) {
      toast.error("SKU is required");
      return;
    }

    if (!formData.categoryId) {
      toast.error("Category is required");
      return;
    }

    if (formData.price < 0 || formData.cost < 0) {
      toast.error(t("stock.priceAndCostMustBePositive"));
      return;
    }

    // Store form data and show password dialog
    setPendingFormData(formData);
    setPasswordDialogOpen(true);
  };

  const handlePasswordConfirm = async () => {
    if (!pendingFormData) return;

    setSubmitting(true);

    try {
      if (product) {
        await updateProduct(product.id, pendingFormData);
        toast.success(t("stock.productUpdated"));
      } else {
        await addProduct(pendingFormData);
        toast.success(t("stock.productAdded"));
      }

      setOpen(false);
      onSuccess?.();
    } catch (error) {
      console.error("Error saving product:", error);
      toast.error(t("common.error"));
    } finally {
      setSubmitting(false);
      setPendingFormData(null);
    }
  };

  const handleInputChange = (field: keyof ProductFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const defaultTrigger = (
    <Button className="flex items-center gap-2">
      <Plus className="h-4 w-4" />
      {t("stock.addProduct")}
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            {product ? t("stock.editProduct") : t("stock.addProduct")}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="name">{t("stock.productName")} *</Label>
              <Input
                id="name"
                value={formData.name}
                onChange={(e) => handleInputChange("name", e.target.value)}
                placeholder={t("stock.enterProductName")}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="sku">{t("stock.sku")} *</Label>
              <Input
                id="sku"
                value={formData.sku}
                onChange={(e) => handleInputChange("sku", e.target.value)}
                placeholder={t("stock.enterSKU")}
                required
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="description">{t("stock.productDescription")}</Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder={t("stock.enterProductDescription")}
              rows={3}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="barcode">{t("stock.barcode")}</Label>
              <Input
                id="barcode"
                value={formData.barcode}
                onChange={(e) => handleInputChange("barcode", e.target.value)}
                placeholder={t("stock.enterBarcode")}
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="category">{t("stock.category")} *</Label>
              <Select
                value={formData.categoryId}
                onValueChange={(value) =>
                  handleInputChange("categoryId", value)
                }
                required
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Pricing */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="price">{t("stock.price")} *</Label>
              <Input
                id="price"
                type="number"
                step="0.01"
                min="0"
                value={formData.price}
                onChange={(e) =>
                  handleInputChange("price", parseFloat(e.target.value) || 0)
                }
                placeholder="0.00"
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="cost">{t("stock.cost")} *</Label>
              <Input
                id="cost"
                type="number"
                step="0.01"
                min="0"
                value={formData.cost}
                onChange={(e) =>
                  handleInputChange("cost", parseFloat(e.target.value) || 0)
                }
                placeholder="0.00"
                required
              />
            </div>
          </div>

          {/* Stock Information */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="stockQuantity">{t("stock.stock")}</Label>
              <Input
                id="stockQuantity"
                type="number"
                min="0"
                value={formData.stockQuantity}
                onChange={(e) =>
                  handleInputChange(
                    "stockQuantity",
                    parseInt(e.target.value) || 0
                  )
                }
                placeholder="0"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="minStockLevel">{t("stock.minStock")}</Label>
              <Input
                id="minStockLevel"
                type="number"
                min="0"
                value={formData.minStockLevel}
                onChange={(e) =>
                  handleInputChange(
                    "minStockLevel",
                    parseInt(e.target.value) || 0
                  )
                }
                placeholder="0"
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="maxStockLevel">{t("stock.maxStock")}</Label>
              <Input
                id="maxStockLevel"
                type="number"
                min="0"
                value={formData.maxStockLevel || ""}
                onChange={(e) =>
                  handleInputChange(
                    "maxStockLevel",
                    e.target.value ? parseInt(e.target.value) : undefined
                  )
                }
                placeholder="Optional"
              />
            </div>
          </div>

          {/* Image URL */}
          <div className="space-y-2">
            <Label htmlFor="imageUrl">{t("stock.image")}</Label>
            <Input
              id="imageUrl"
              type="url"
              value={formData.imageUrl}
              onChange={(e) => handleInputChange("imageUrl", e.target.value)}
              placeholder="https://example.com/image.jpg"
            />
          </div>

          {/* Active Status */}
          <div className="flex items-center space-x-2">
            <Switch
              id="isActive"
              checked={formData.isActive}
              onCheckedChange={(checked) =>
                handleInputChange("isActive", checked)
              }
            />
            <Label htmlFor="isActive">{t("stock.isActive")}</Label>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={submitting || loading}>
              {submitting
                ? "Saving..."
                : product
                ? "Update Product"
                : "Add Product"}
            </Button>
          </div>
        </form>
      </DialogContent>

      <StockPasswordDialog
        open={passwordDialogOpen}
        onOpenChange={setPasswordDialogOpen}
        onConfirm={handlePasswordConfirm}
        title={
          product
            ? t("stock.confirmProductUpdate")
            : t("stock.confirmProductCreation")
        }
        description={
          product
            ? t("stock.productUpdateWarning")
            : t("stock.productCreationWarning")
        }
        confirmButtonText={
          product ? t("stock.updateProduct") : t("stock.addProduct")
        }
      />
    </Dialog>
  );
};

export default ProductFormDialog;
