import { useRepairShopContext } from "@/context/RepairShopContext";

interface DailySummaryReportProps {
  sales: any[];
  dateRange: { from: Date; to: Date };
  repairs?: any[];
}

export const generateDailySummaryHTML = ({
  sales,
  dateRange,
  repairs = [],
}: DailySummaryReportProps) => {
  const totalRevenue = sales.reduce((sum, sale) => sum + sale.totalAmount, 0);
  const totalTransactions = sales.length;
  const averageTransaction =
    totalTransactions > 0 ? totalRevenue / totalTransactions : 0;
  const totalItems = sales.reduce(
    (sum, sale) => sum + (sale.items?.length || 0),
    0
  );

  // Payment method breakdown
  const paymentMethods = sales.reduce((acc, sale) => {
    acc[sale.paymentMethod] = (acc[sale.paymentMethod] || 0) + sale.totalAmount;
    return acc;
  }, {});

  // Hourly breakdown
  const hourlyData = sales.reduce((acc, sale) => {
    const hour = new Date(sale.createdAt).getHours();
    acc[hour] = (acc[hour] || 0) + sale.totalAmount;
    return acc;
  }, {});

  const peakHour = Object.entries(hourlyData).reduce(
    (peak, [hour, amount]) =>
      amount > (peak.amount || 0) ? { hour: parseInt(hour), amount } : peak,
    {}
  );

  return `
    <!DOCTYPE html>
    <html>
    <head>
      <meta charset="utf-8">
      <title>Sales Summary Report</title>
      <style>
        body { font-family: Arial, sans-serif; margin: 20px; color: #000; font-weight: bold; }
        .header { text-align: center; margin-bottom: 30px; border-bottom: 3px solid #000; padding-bottom: 20px; }
        .shop-name { font-size: 24px; font-weight: 900; margin-bottom: 5px; }
        .report-title { font-size: 18px; color: #000; margin-bottom: 10px; font-weight: bold; }
        .date-range { font-size: 14px; color: #000; font-weight: bold; }
        .metrics { display: grid; grid-template-columns: repeat(4, 1fr); gap: 20px; margin: 30px 0; }
        .metric-card { text-align: center; padding: 20px; border: 2px solid #000; border-radius: 8px; }
        .metric-value { font-size: 28px; font-weight: 900; color: #000; margin-bottom: 5px; }
        .metric-label { font-size: 14px; color: #000; font-weight: bold; }
        .section { margin: 30px 0; }
        .section-title { font-size: 16px; font-weight: 900; margin-bottom: 15px; border-bottom: 2px solid #000; padding-bottom: 5px; }
        .payment-grid { display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; }
        .payment-item { padding: 15px; background: #f0f0f0; border: 1px solid #000; border-radius: 6px; text-align: center; }
        .payment-method { font-weight: 900; margin-bottom: 5px; text-transform: capitalize; }
        .payment-amount { font-size: 18px; color: #000; font-weight: bold; }
        .peak-hour { background: #f0f0f0; border: 1px solid #000; padding: 15px; border-radius: 6px; text-align: center; }
        .sales-table { width: 100%; border-collapse: collapse; margin-top: 15px; border: 2px solid #000; }
        .sales-table th, .sales-table td { padding: 10px; text-align: left; border-bottom: 1px solid #000; font-weight: bold; }
        .sales-table th { background: #f0f0f0; font-weight: 900; }
        .amount { color: #000; font-weight: 900; }
        @media print {
          body { margin: 0; }
          .no-print { display: none; }
        }
      </style>
    </head>
    <body>
      <div class="header">
        <div class="shop-name">Besmart Pro</div>
        <div class="report-title">Sales Summary Report</div>
        <div class="date-range">${dateRange.from.toLocaleDateString()} - ${dateRange.to.toLocaleDateString()}</div>
      </div>

      <div class="metrics">
        <div class="metric-card">
          <div class="metric-value">${totalRevenue.toFixed(3)}</div>
          <div class="metric-label">Total Revenue (TND)</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">${totalTransactions}</div>
          <div class="metric-label">Transactions</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">${averageTransaction.toFixed(3)}</div>
          <div class="metric-label">Avg Transaction (TND)</div>
        </div>
        <div class="metric-card">
          <div class="metric-value">${totalItems}</div>
          <div class="metric-label">Items Sold</div>
        </div>
      </div>

      <div class="section">
        <div class="section-title">Payment Methods</div>
        <div class="payment-grid">
          ${Object.entries(paymentMethods)
            .map(
              ([method, amount]) => `
            <div class="payment-item">
              <div class="payment-method">${method}</div>
              <div class="payment-amount">${(amount as number).toFixed(
                3
              )} TND</div>
            </div>
          `
            )
            .join("")}
        </div>
      </div>

      ${
        peakHour.hour !== undefined
          ? `
        <div class="section">
          <div class="section-title">Peak Hour</div>
          <div class="peak-hour">
            <strong>${peakHour.hour}:00 - ${peakHour.hour + 1}:00</strong>
            <span style="margin-left: 20px;">${(
              peakHour.amount as number
            ).toFixed(3)} TND</span>
          </div>
        </div>
      `
          : ""
      }

      <div class="section">
        <div class="section-title">Sales Details</div>
        <table class="sales-table">
          <thead>
            <tr>
              <th>Sale ID</th>
              <th>Customer</th>
              <th>Items</th>
              <th>Payment</th>
              <th>Amount</th>
              <th>Date</th>
            </tr>
          </thead>
          <tbody>
            ${sales
              .map(
                (sale) => `
              <tr>
                <td>${sale.saleNumber}</td>
                <td>${sale.customerName || "Walk-in Customer"}</td>
                <td>${sale.items?.length || 0}</td>
                <td style="text-transform: capitalize;">${
                  sale.paymentMethod
                }</td>
                <td class="amount">${sale.totalAmount.toFixed(3)} TND</td>
                <td>${new Date(sale.createdAt).toLocaleString()}</td>
              </tr>
            `
              )
              .join("")}
          </tbody>
        </table>
      </div>

      ${repairs.length > 0 ? `
        <div class="section">
          <div class="section-title">Repair Custom Forms</div>
          <table class="sales-table">
            <thead>
              <tr>
                <th>Repair ID</th>
                <th>Customer</th>
                <th>Device</th>
                <th>Custom Fields</th>
                <th>Date</th>
              </tr>
            </thead>
            <tbody>
              ${repairs.filter(repair => repair.customFormResponses?.responses && Object.keys(repair.customFormResponses.responses).length > 0)
                .map(repair => `
                <tr>
                  <td>${repair.ticketNumber || repair.id}</td>
                  <td>${repair.customerName}</td>
                  <td>${repair.phoneModel || 'N/A'}</td>
                  <td>
                    ${Object.entries(repair.customFormResponses.responses)
                      .map(([key, value]) => `<strong>${key}:</strong> ${value}`)
                      .join('<br>')}
                  </td>
                  <td>${new Date(repair.createdAt).toLocaleDateString()}</td>
                </tr>
              `).join('')}
            </tbody>
          </table>
        </div>
      ` : ''}

      <div style="margin-top: 40px; text-align: center; color: #666; font-size: 12px;">
        Generated on ${new Date().toLocaleString()}
      </div>
    </body>
    </html>
  `;
};

export const printDailySummaryReport = (props: DailySummaryReportProps) => {
  const htmlContent = generateDailySummaryHTML(props);
  const printWindow = window.open("", "_blank");
  if (printWindow) {
    printWindow.document.write(htmlContent);
    printWindow.document.close();
    printWindow.focus();
    setTimeout(() => {
      printWindow.print();
      printWindow.close();
    }, 250);
  }
};