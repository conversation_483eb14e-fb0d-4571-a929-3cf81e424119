import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ShoppingCart,
  Plus,
  Minus,
  Trash2,
  Search,
  CreditCard,
  Banknote,
  Smartphone,
  Calculator,
  User,
  Phone,
  Percent,
  DollarSign,
  Save,
  Clock,
  FileText,
  X,
  Edit3,
  Pause,
  Play,
} from "lucide-react";
import { CartItem, Product, HeldOrder, OrderDiscount } from "@/types/stock";
import { useStock } from "@/context/StockContext";
import { toast } from "sonner";
import BarcodeScanner from "./BarcodeScanner";
import ScannerInstructions from "./ScannerInstructions";
import { printSalesTicket } from "./PrintSalesTicket";
import ItemDiscountForm from "./ItemDiscountForm";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { EmptyState } from "@/components/ui/empty-state";
import { ProductSkeleton } from "./ProductSkeleton";
import { QuickActionsBar } from "./QuickActionsBar";
import { Package, ShoppingBag, Loader2 } from "lucide-react";

interface POSInterfaceProps {
  viewMode: "grid" | "list";
  touchMode: boolean;
  onViewModeChange: (mode: "grid" | "list") => void;
}

// Mock data - replace with actual data from context/API
const mockProducts: Product[] = [
  {
    id: "1",
    name: "iPhone 15 Pro",
    description: "Latest iPhone model",
    sku: "IP15P-128",
    price: 999,
    cost: 800,
    stockQuantity: 5,
    minStockLevel: 2,
    isActive: true,
    categoryId: "phones",
    createdAt: new Date(),
    updatedAt: new Date(),
    repairShopId: "shop1",
  },
  {
    id: "2",
    name: "Phone Case - Clear",
    description: "Transparent protective case",
    sku: "CASE-CLR",
    price: 25,
    cost: 10,
    stockQuantity: 50,
    minStockLevel: 10,
    isActive: true,
    categoryId: "accessories",
    createdAt: new Date(),
    updatedAt: new Date(),
    repairShopId: "shop1",
  },
  {
    id: "3",
    name: "USB-C Cable",
    description: "Fast charging cable",
    sku: "CABLE-USBC",
    price: 15,
    cost: 5,
    stockQuantity: 100,
    minStockLevel: 20,
    isActive: true,
    categoryId: "accessories",
    createdAt: new Date(),
    updatedAt: new Date(),
    repairShopId: "shop1",
  },
];

const POSInterface: React.FC<POSInterfaceProps> = ({ viewMode, touchMode }) => {
  const { t } = useTranslation();
  const {
    products,
    categories,
    processSale: processStockSale,
    loading,
  } = useStock();
  const { repairShop } = useRepairShopContext();
  const [cart, setCart] = useState<CartItem[]>([]);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [customerName, setCustomerName] = useState("");
  const [customerPhone, setCustomerPhone] = useState("");
  const [paymentMethod, setPaymentMethod] = useState<
    "cash" | "card" | "mobile"
  >("cash");
  const [amountPaid, setAmountPaid] = useState<number>(0);
  const [barcodeBuffer, setBarcodeBuffer] = useState("");
  const [lastKeyTime, setLastKeyTime] = useState(0);

  // Advanced POS Features
  const [heldOrders, setHeldOrders] = useState<HeldOrder[]>([]);
  const [orderDiscount, setOrderDiscount] = useState<OrderDiscount>({
    type: "percentage",
    value: 0,
  });
  const [showDiscountDialog, setShowDiscountDialog] = useState(false);
  const [showHeldOrders, setShowHeldOrders] = useState(false);
  const [showItemDiscountDialog, setShowItemDiscountDialog] = useState(false);
  const [selectedItemForDiscount, setSelectedItemForDiscount] = useState<
    string | null
  >(null);
  const [orderNotes, setOrderNotes] = useState("");
  const [showNotesDialog, setShowNotesDialog] = useState(false);

  const filteredProducts = products.filter((product) => {
    const matchesSearch =
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (product.barcode &&
        product.barcode.toLowerCase().includes(searchTerm.toLowerCase()));

    const matchesCategory =
      selectedCategory === "all" || product.categoryId === selectedCategory;

    return matchesSearch && matchesCategory;
  });

  const subtotal = cart.reduce((sum, item) => sum + item.totalPrice, 0);
  const orderDiscountAmount =
    orderDiscount.type === "percentage"
      ? (subtotal * orderDiscount.value) / 100
      : orderDiscount.value;
  const total = Math.max(0, subtotal - orderDiscountAmount);
  const change = Math.max(0, amountPaid - total);

  const addToCart = (product: Product) => {
    // Check if product is active
    if (!product.isActive) {
      toast.error(t("stock.productInactive"));
      return;
    }

    // Check if product is out of stock
    if (product.stockQuantity <= 0) {
      toast.error(t("stock.productOutOfStock"));
      return;
    }

    const existingItem = cart.find((item) => item.product.id === product.id);

    if (existingItem) {
      if (existingItem.quantity >= product.stockQuantity) {
        toast.error(t("stock.insufficientStock"));
        return;
      }
      updateCartItemQuantity(product.id, existingItem.quantity + 1);
    } else {
      const newItem: CartItem = {
        product,
        quantity: 1,
        unitPrice: product.price,
        discount: 0,
        discountType: "fixed",
        totalPrice: product.price,
      };
      setCart([...cart, newItem]);
      toast.success(t("stock.productAdded"), { duration: 1500 });
    }
  };

  const handleBarcodeScanned = (barcode: string) => {
    // Find product by barcode
    const product = products.find(
      (p) => p.barcode === barcode || p.sku === barcode
    );

    if (product) {
      if (!product.isActive) {
        toast.error(t("stock.productInactive"));
        return;
      }

      if (product.stockQuantity <= 0) {
        toast.error(t("stock.productOutOfStock"));
        return;
      }

      addToCart(product);
      toast.success(t("stock.barcodeAdded"), { duration: 1500 });
    } else {
      toast.error(t("stock.productNotFound"));
    }
  };

  // Handle keyboard input for barcode scanning
  useEffect(() => {
    const handleKeyPress = (event: KeyboardEvent) => {
      const currentTime = Date.now();
      const timeDiff = currentTime - lastKeyTime;

      // If more than 100ms between keystrokes, start new barcode
      if (timeDiff > 100) {
        setBarcodeBuffer("");
      }

      setLastKeyTime(currentTime);

      // Handle Enter key (end of barcode scan)
      if (event.key === "Enter") {
        event.preventDefault();
        if (barcodeBuffer.length > 0) {
          handleBarcodeScanned(barcodeBuffer);
          setBarcodeBuffer("");
        }
        return;
      }

      // Only process alphanumeric characters and some symbols
      if (/^[a-zA-Z0-9\-_]$/.test(event.key)) {
        // Prevent default only if we're not in an input field
        const target = event.target as HTMLElement;
        const isInputField =
          target.tagName === "INPUT" ||
          target.tagName === "TEXTAREA" ||
          target.contentEditable === "true";

        if (!isInputField) {
          event.preventDefault();
          setBarcodeBuffer((prev) => prev + event.key);
        }
      }
    };

    // Add event listener
    document.addEventListener("keydown", handleKeyPress);

    // Cleanup
    return () => {
      document.removeEventListener("keydown", handleKeyPress);
    };
  }, [barcodeBuffer, lastKeyTime, products]);

  // Auto-clear barcode buffer after timeout
  useEffect(() => {
    if (barcodeBuffer.length > 0) {
      const timeout = setTimeout(() => {
        setBarcodeBuffer("");
      }, 1000); // Clear after 1 second of inactivity

      return () => clearTimeout(timeout);
    }
  }, [barcodeBuffer]);

  const updateCartItemQuantity = (productId: string, newQuantity: number) => {
    if (newQuantity <= 0) {
      removeFromCart(productId);
      return;
    }

    // Find the product to check stock availability
    const product = products.find((p) => p.id === productId);
    if (product && newQuantity > product.stockQuantity) {
      toast.error(t("stock.insufficientStock"));
      return;
    }

    setCart(
      cart.map((item) => {
        if (item.product.id === productId) {
          const totalPrice = (item.unitPrice - item.discount) * newQuantity;
          return { ...item, quantity: newQuantity, totalPrice };
        }
        return item;
      })
    );
  };

  const removeFromCart = (productId: string) => {
    setCart(cart.filter((item) => item.product.id !== productId));
  };

  const clearCart = () => {
    setCart([]);
    setCustomerName("");
    setCustomerPhone("");
    setAmountPaid(0);
    setOrderDiscount({ type: "percentage", value: 0 });
    setOrderNotes("");
  };

  // Advanced POS Functions
  const holdOrder = () => {
    if (cart.length === 0) {
      toast.error(t("stock.cartIsEmpty"));
      return;
    }

    const orderName = `Order ${heldOrders.length + 1}`;
    const newHeldOrder: HeldOrder = {
      id: Date.now().toString(),
      name: orderName,
      items: [...cart],
      customerName: customerName || undefined,
      customerPhone: customerPhone || undefined,
      subtotal,
      totalDiscount: orderDiscountAmount,
      totalAmount: total,
      createdAt: new Date(),
      notes: orderNotes || undefined,
    };

    setHeldOrders([...heldOrders, newHeldOrder]);
    clearCart();
    toast.success(t("stock.orderHeld"));
  };

  const recallOrder = (order: HeldOrder) => {
    setCart(order.items);
    setCustomerName(order.customerName || "");
    setCustomerPhone(order.customerPhone || "");
    setOrderNotes(order.notes || "");

    // Calculate order discount from held order
    if (order.totalDiscount > 0) {
      const discountPercentage = (order.totalDiscount / order.subtotal) * 100;
      setOrderDiscount({
        type: "percentage",
        value: Math.round(discountPercentage * 100) / 100,
      });
    }

    // Remove from held orders
    setHeldOrders(heldOrders.filter((h) => h.id !== order.id));
    setShowHeldOrders(false);
    toast.success(t("stock.orderRecalled"));
  };

  const deleteHeldOrder = (orderId: string) => {
    setHeldOrders(heldOrders.filter((h) => h.id !== orderId));
    toast.success(t("stock.orderDeleted"));
  };

  const applyItemDiscount = (
    productId: string,
    discount: number,
    discountType: "percentage" | "fixed"
  ) => {
    setCart(
      cart.map((item) => {
        if (item.product.id === productId) {
          const discountAmount =
            discountType === "percentage"
              ? (item.unitPrice * discount) / 100
              : discount;
          const finalDiscount = Math.min(discountAmount, item.unitPrice);
          const totalPrice = (item.unitPrice - finalDiscount) * item.quantity;

          return {
            ...item,
            discount: finalDiscount,
            discountType,
            totalPrice,
          };
        }
        return item;
      })
    );

    setShowItemDiscountDialog(false);
    setSelectedItemForDiscount(null);
    toast.success(t("stock.discountApplied"));
  };

  const handleProcessSale = async () => {
    if (cart.length === 0) {
      toast.error(t("stock.cartIsEmpty"));
      return;
    }

    if (amountPaid < total) {
      toast.error(t("stock.insufficientPayment"));
      return;
    }

    // Check if all cart items have sufficient stock
    for (const cartItem of cart) {
      const currentProduct = products.find((p) => p.id === cartItem.product.id);
      if (!currentProduct) {
        toast.error(
          `${cartItem.product.name}: ${t("stock.productNoLongerExists")}`
        );
        return;
      }
      if (!currentProduct.isActive) {
        toast.error(
          `${cartItem.product.name}: ${t("stock.productNoLongerActive")}`
        );
        return;
      }
      if (currentProduct.stockQuantity < cartItem.quantity) {
        toast.error(
          t("stock.insufficientStockDetailed", {
            productName: cartItem.product.name,
            available: currentProduct.stockQuantity,
            required: cartItem.quantity,
          })
        );
        return;
      }
    }

    try {
      const saleData = {
        customerName: customerName || undefined,
        customerPhone: customerPhone || undefined,
        items: cart.map((item) => ({
          productId: item.product.id,
          quantity: item.quantity,
          unitPrice: item.unitPrice,
          discount: item.discount,
        })),
        discountAmount: orderDiscountAmount,
        paymentMethod,
        amountPaid,
        notes: orderNotes || undefined,
      };

      const result = await processStockSale(saleData);

      // Create sale items for printing
      const saleItems = cart.map((item) => ({
        id: `temp-${item.product.id}`,
        saleId: result.id,
        productId: item.product.id,
        product: item.product,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        discount: item.discount,
        totalPrice: item.totalPrice,
        createdAt: new Date(),
        updatedAt: new Date(),
      }));

      // Print the ticket immediately
      printSalesTicket(result, saleItems, repairShop);

      clearCart();
      toast.success(t("stock.saleProcessedSuccessfully"));
    } catch (error) {
      console.error("Error processing sale:", error);
      toast.error(
        `${t("stock.saleFailed")}: ${error.message || t("stock.unknownError")}`
      );
    }
  };

  const buttonSize = touchMode ? "lg" : "default";
  const cardPadding = touchMode ? "p-6" : "p-4";

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Products Section */}
      <div className="lg:col-span-2 space-y-4">
        {/* Search and Filters Section */}
        <div className="bg-card border rounded-lg p-4 space-y-4">
          <div className="flex items-center justify-between">
            <h2 className="text-lg font-semibold text-foreground flex items-center gap-2">
              <Search className="h-5 w-5 text-primary" />
              Product Search
            </h2>
            <div className="text-sm text-muted-foreground">
              {filteredProducts.length} products
            </div>
          </div>
          <div className="flex gap-3">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder={
                  loading ? t("common.loading") : t("stock.searchProducts")
                }
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={`pl-10 border-2 focus:border-primary ${
                  touchMode ? "h-12 text-lg" : "h-10"
                }`}
                disabled={loading}
              />
            </div>
            <Select
              value={selectedCategory}
              onValueChange={setSelectedCategory}
              disabled={loading}
            >
              <SelectTrigger
                className={`w-48 border-2 focus:border-primary ${
                  touchMode ? "h-12" : "h-10"
                }`}
              >
                <SelectValue
                  placeholder={
                    loading ? t("common.loading") : t("stock.allCategories")
                  }
                />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">{t("stock.allCategories")}</SelectItem>
                {(categories || []).map((category) => (
                  <SelectItem key={category.id} value={category.id}>
                    {category.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Scanner Status */}
          <div className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 border-l-4 border-green-500 p-3 rounded-r-lg">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse shadow-lg"></div>
                <div>
                  <span className="text-sm font-semibold text-green-700 dark:text-green-300">
                    {t("stock.scannerActive")}
                  </span>
                  <p className="text-xs text-green-600 dark:text-green-400 mt-0.5">
                    Ready to scan barcodes
                  </p>
                </div>
              </div>
              {barcodeBuffer && (
                <div className="flex items-center gap-2">
                  <span className="text-xs font-medium text-green-600 dark:text-green-400">
                    {t("stock.scanning")}:
                  </span>
                  <code className="px-2 py-1 bg-white dark:bg-gray-800 border rounded text-sm font-mono text-green-700 dark:text-green-300">
                    {barcodeBuffer}
                  </code>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Products Grid */}
        {loading ? (
          <div
            className={`grid gap-4 ${
              viewMode === "grid"
                ? touchMode
                  ? "grid-cols-2 sm:grid-cols-3"
                  : "grid-cols-3 sm:grid-cols-4"
                : "grid-cols-1"
            }`}
          >
            {Array.from({ length: 8 }).map((_, i) => (
              <ProductSkeleton
                key={i}
                touchMode={touchMode}
                cardPadding={cardPadding}
              />
            ))}
          </div>
        ) : filteredProducts.length === 0 ? (
          <EmptyState
            icon={<Package className="h-16 w-16" />}
            title={t("stock.emptyProducts.title")}
            subtitle={t("stock.emptyProducts.subtitle")}
            action={{
              label: t("stock.emptyProducts.action"),
              onClick: () => {
                setSearchTerm("");
                setSelectedCategory("all");
              },
            }}
            tip={t("stock.emptyProducts.tip")}
            className="col-span-full py-12"
          />
        ) : (
          <div
            className={`grid gap-4 ${
              viewMode === "grid"
                ? touchMode
                  ? "grid-cols-2 sm:grid-cols-3"
                  : "grid-cols-3 sm:grid-cols-4"
                : "grid-cols-1"
            }`}
          >
            {filteredProducts.map((product) => {
              const isOutOfStock = product.stockQuantity <= 0;
              const isInactive = !product.isActive;
              const isDisabled = isOutOfStock || isInactive;

              return (
                <Card
                  key={product.id}
                  className={`transition-shadow ${
                    touchMode ? "min-h-[120px]" : "min-h-[100px]"
                  } ${
                    isDisabled
                      ? "opacity-50 cursor-not-allowed"
                      : "cursor-pointer hover:shadow-md"
                  }`}
                  onClick={() => !isDisabled && addToCart(product)}
                >
                  <CardContent className={cardPadding}>
                    <div className="flex flex-col h-full">
                      <div className="flex-1">
                        <h3
                          className={`font-bold text-foreground ${
                            touchMode ? "text-base" : "text-sm"
                          } line-clamp-2 mb-1`}
                        >
                          {product.name}
                          {isInactive && (
                            <span className="ml-2 text-xs font-medium text-red-500 bg-red-50 dark:bg-red-900/20 px-1.5 py-0.5 rounded">
                              {t("stock.inactive")}
                            </span>
                          )}
                        </h3>
                        <p
                          className={`text-muted-foreground font-medium ${
                            touchMode ? "text-sm" : "text-xs"
                          } mb-2`}
                        >
                          SKU: {product.sku}
                        </p>
                      </div>
                      <div className="flex justify-between items-center mt-auto pt-2 border-t border-gray-100 dark:border-gray-800">
                        <span
                          className={`font-bold text-green-600 dark:text-green-400 ${
                            touchMode ? "text-lg" : "text-base"
                          }`}
                        >
                          {product.price.toFixed(3)} TND
                        </span>
                        <Badge
                          variant={
                            isOutOfStock
                              ? "destructive"
                              : product.stockQuantity > product.minStockLevel
                              ? "default"
                              : "secondary"
                          }
                          className="font-semibold"
                        >
                          {isOutOfStock
                            ? t("stock.outOfStock")
                            : `${product.stockQuantity} in stock`}
                        </Badge>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}
      </div>

      {/* Cart & Checkout Section */}
      <div className="space-y-4">
        {/* Customer Info */}
        <Card className="border-2 border-blue-100 dark:border-blue-900/30">
          <CardHeader className="pb-3 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/10 dark:to-indigo-900/10">
            <CardTitle className="flex items-center gap-2 text-lg font-bold text-blue-900 dark:text-blue-100">
              <User className="h-5 w-5 text-blue-600 dark:text-blue-400" />
              {t("stock.customer")}
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            <Input
              placeholder={t("stock.customerName")}
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              className={touchMode ? "h-12" : "h-10"}
            />
            <Input
              placeholder={t("stock.customerPhone")}
              value={customerPhone}
              onChange={(e) => setCustomerPhone(e.target.value)}
              className={touchMode ? "h-12" : "h-10"}
            />
          </CardContent>
        </Card>

        {/* Cart */}
        <Card className="border-2 border-orange-100 dark:border-orange-900/30">
          <CardHeader className="pb-3 bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/10 dark:to-amber-900/10">
            <div className="flex justify-between items-center">
              <CardTitle className="flex items-center gap-2 text-lg font-bold text-orange-900 dark:text-orange-100">
                <ShoppingCart className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                {t("stock.cart")}
                <span className="bg-orange-200 dark:bg-orange-800 text-orange-800 dark:text-orange-200 px-2 py-1 rounded-full text-sm font-bold">
                  {cart.length}
                </span>
              </CardTitle>
              <div className="flex gap-2">
                {cart.length > 0 && (
                  <>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={holdOrder}
                      className="text-blue-600 hover:text-blue-700"
                    >
                      <Pause className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowNotesDialog(true)}
                      className="text-green-600 hover:text-green-700"
                    >
                      <FileText className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearCart}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </>
                )}
                {heldOrders.length > 0 && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowHeldOrders(true)}
                    className="text-purple-600 hover:text-purple-700"
                  >
                    <Clock className="h-4 w-4" />
                    <span className="ml-1">{heldOrders.length}</span>
                  </Button>
                )}
              </div>
            </div>
          </CardHeader>
          <CardContent className="space-y-3">
            {cart.length === 0 ? (
              <EmptyState
                icon={<ShoppingCart className="h-12 w-12" />}
                title={t("stock.emptyCart.title")}
                subtitle={t("stock.emptyCart.subtitle")}
                tip={t("stock.emptyCart.tip")}
                className="py-6"
              />
            ) : (
              <>
                {cart.map((item) => (
                  <div
                    key={item.product.id}
                    className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg space-y-2"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium text-sm">
                          {item.product.name}
                        </h4>
                        <div className="flex items-center gap-2 text-xs text-gray-600 dark:text-gray-400">
                          <span>{item.unitPrice.toFixed(3)} TND</span>
                          {item.discount > 0 && (
                            <span className="text-green-600">
                              -{item.discount.toFixed(3)} TND
                            </span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedItemForDiscount(item.product.id);
                            setShowItemDiscountDialog(true);
                          }}
                          className="h-8 w-8 p-0 text-green-600 hover:text-green-700"
                        >
                          <Percent className="h-3 w-3" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            updateCartItemQuantity(
                              item.product.id,
                              item.quantity - 1
                            )
                          }
                          className="h-8 w-8 p-0"
                        >
                          <Minus className="h-3 w-3" />
                        </Button>
                        <span className="w-8 text-center text-sm font-medium">
                          {item.quantity}
                        </span>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() =>
                            updateCartItemQuantity(
                              item.product.id,
                              item.quantity + 1
                            )
                          }
                          className="h-8 w-8 p-0"
                        >
                          <Plus className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm font-medium">
                        Total: {item.totalPrice.toFixed(3)} TND
                      </span>
                    </div>
                  </div>
                ))}
              </>
            )}
          </CardContent>
        </Card>

        {/* Payment */}
        {cart.length > 0 && (
          <Card className="border-2 border-green-100 dark:border-green-900/30">
            <CardHeader className="pb-3 bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/10 dark:to-emerald-900/10">
              <CardTitle className="flex items-center gap-2 text-lg font-bold text-green-900 dark:text-green-100">
                <Calculator className="h-5 w-5 text-green-600 dark:text-green-400" />
                {t("stock.payment")}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Totals */}
              <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4 space-y-3">
                <div className="flex justify-between text-base">
                  <span className="text-muted-foreground">
                    {t("stock.subtotal")}
                  </span>
                  <span className="font-semibold">
                    {subtotal.toFixed(3)} TND
                  </span>
                </div>
                {orderDiscountAmount > 0 && (
                  <div className="flex justify-between text-green-600 dark:text-green-400">
                    <span className="font-medium">
                      {t("stock.discountAmount")}
                    </span>
                    <span className="font-semibold">
                      -{orderDiscountAmount.toFixed(3)} TND
                    </span>
                  </div>
                )}
                <div className="border-t-2 border-gray-200 dark:border-gray-700 pt-3">
                  <div className="flex justify-between items-center">
                    <span className="text-xl font-bold text-foreground">
                      {t("stock.total")}
                    </span>
                    <span className="text-2xl font-bold text-green-600 dark:text-green-400">
                      {total.toFixed(3)} TND
                    </span>
                  </div>
                </div>
              </div>

              {/* Order Actions */}
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowDiscountDialog(true)}
                  className="flex-1 text-green-600 hover:text-green-700"
                >
                  <Percent className="h-4 w-4 mr-1" />
                  {t("stock.applyDiscount")}
                </Button>
              </div>

              {/* Payment Method */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("stock.paymentMethod")}
                </label>
                <div className="grid grid-cols-3 gap-2">
                  <Button
                    variant={paymentMethod === "cash" ? "default" : "outline"}
                    size={buttonSize}
                    onClick={() => setPaymentMethod("cash")}
                    className="flex flex-col gap-1"
                  >
                    <Banknote className="h-4 w-4" />
                    <span className="text-xs">{t("stock.cash")}</span>
                  </Button>
                  <Button
                    variant={paymentMethod === "card" ? "default" : "outline"}
                    size={buttonSize}
                    onClick={() => setPaymentMethod("card")}
                    className="flex flex-col gap-1"
                  >
                    <CreditCard className="h-4 w-4" />
                    <span className="text-xs">{t("stock.card")}</span>
                  </Button>
                  <Button
                    variant={paymentMethod === "mobile" ? "default" : "outline"}
                    size={buttonSize}
                    onClick={() => setPaymentMethod("mobile")}
                    className="flex flex-col gap-1"
                  >
                    <Smartphone className="h-4 w-4" />
                    <span className="text-xs">{t("stock.mobile")}</span>
                  </Button>
                </div>
              </div>

              {/* Amount Paid */}
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("stock.amountPaid")}
                </label>
                <Input
                  type="number"
                  value={amountPaid}
                  onChange={(e) => setAmountPaid(Number(e.target.value))}
                  className={touchMode ? "h-12 text-lg" : "h-10"}
                  placeholder="0.00"
                />
                {amountPaid > 0 && (
                  <div className="flex justify-between text-sm">
                    <span>{t("stock.change")}</span>
                    <span className="font-medium">{change.toFixed(3)} TND</span>
                  </div>
                )}
              </div>

              {/* Process Sale Button */}
              <Button
                onClick={handleProcessSale}
                size={buttonSize}
                className="w-full"
                disabled={cart.length === 0 || amountPaid < total || loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {t("common.processing")}
                  </>
                ) : (
                  `${t("stock.processSale")} - ${total.toFixed(3)} TND`
                )}
              </Button>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Order Discount Dialog */}
      {showDiscountDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {t("stock.orderDiscount")}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowDiscountDialog(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("stock.discountType")}
                </label>
                <div className="flex gap-2">
                  <Button
                    variant={
                      orderDiscount.type === "percentage"
                        ? "default"
                        : "outline"
                    }
                    size="sm"
                    onClick={() =>
                      setOrderDiscount({ ...orderDiscount, type: "percentage" })
                    }
                    className="flex-1"
                  >
                    {t("stock.percentage")}
                  </Button>
                  <Button
                    variant={
                      orderDiscount.type === "fixed" ? "default" : "outline"
                    }
                    size="sm"
                    onClick={() =>
                      setOrderDiscount({ ...orderDiscount, type: "fixed" })
                    }
                    className="flex-1"
                  >
                    {t("stock.fixedAmount")}
                  </Button>
                </div>
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  {t("stock.discountValue")}
                </label>
                <Input
                  type="number"
                  value={orderDiscount.value}
                  onChange={(e) =>
                    setOrderDiscount({
                      ...orderDiscount,
                      value: Number(e.target.value),
                    })
                  }
                  placeholder={
                    orderDiscount.type === "percentage" ? "0-100" : "0.000"
                  }
                />
              </div>
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowDiscountDialog(false)}
                  className="flex-1"
                >
                  {t("common.cancel")}
                </Button>
                <Button
                  onClick={() => setShowDiscountDialog(false)}
                  className="flex-1"
                >
                  {t("stock.applyDiscount")}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Item Discount Dialog */}
      {showItemDiscountDialog && selectedItemForDiscount && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {t("stock.itemDiscount")}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowItemDiscountDialog(false);
                    setSelectedItemForDiscount(null);
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <ItemDiscountForm
                onApply={(discount, discountType) =>
                  applyItemDiscount(
                    selectedItemForDiscount,
                    discount,
                    discountType
                  )
                }
                onCancel={() => {
                  setShowItemDiscountDialog(false);
                  setSelectedItemForDiscount(null);
                }}
              />
            </CardContent>
          </Card>
        </div>
      )}

      {/* Held Orders Dialog */}
      {showHeldOrders && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-2xl mx-4 max-h-[80vh] overflow-hidden">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {t("stock.heldOrders")}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowHeldOrders(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="overflow-y-auto">
              {heldOrders.length === 0 ? (
                <p className="text-center text-gray-500 py-8">
                  {t("stock.noHeldOrders")}
                </p>
              ) : (
                <div className="space-y-3">
                  {heldOrders.map((order) => (
                    <div key={order.id} className="p-4 border rounded-lg">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="font-medium">{order.name}</h4>
                          <p className="text-sm text-gray-600">
                            {t("stock.heldOn")}:{" "}
                            {order.createdAt.toLocaleString()}
                          </p>
                          {order.customerName && (
                            <p className="text-sm text-gray-600">
                              {order.customerName}
                            </p>
                          )}
                        </div>
                        <div className="text-right">
                          <p className="font-medium">
                            {order.totalAmount.toFixed(3)} TND
                          </p>
                          <p className="text-sm text-gray-600">
                            {order.items.length} items
                          </p>
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => recallOrder(order)}
                          className="flex-1"
                        >
                          <Play className="h-4 w-4 mr-1" />
                          {t("stock.recallOrder")}
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => deleteHeldOrder(order.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </div>
      )}

      {/* Notes Dialog */}
      {showNotesDialog && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <Card className="w-full max-w-md mx-4">
            <CardHeader>
              <CardTitle className="flex items-center justify-between">
                {t("stock.orderNotes")}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowNotesDialog(false)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <textarea
                value={orderNotes}
                onChange={(e) => setOrderNotes(e.target.value)}
                placeholder={t("stock.addNotes")}
                className="w-full h-32 p-3 border rounded-lg resize-none"
              />
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowNotesDialog(false)}
                  className="flex-1"
                >
                  {t("common.cancel")}
                </Button>
                <Button
                  onClick={() => setShowNotesDialog(false)}
                  className="flex-1"
                >
                  {t("common.save")}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Quick Actions Bar */}
      <QuickActionsBar
        cartLength={cart.length}
        heldOrdersLength={heldOrders.length}
        onClearCart={clearCart}
        onHoldOrder={holdOrder}
        onShowHeldOrders={() => setShowHeldOrders(true)}
        onShowDiscount={() => setShowDiscountDialog(true)}
        onShowNotes={() => setShowNotesDialog(true)}
        onProcessSale={handleProcessSale}
        total={total}
        canProcessSale={cart.length > 0 && amountPaid >= total && !loading}
        touchMode={touchMode}
      />
    </div>
  );
};

export default POSInterface;
