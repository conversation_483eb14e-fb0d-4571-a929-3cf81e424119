import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import { useTranslation } from "react-i18next";
import { Calendar, ChevronDown } from "lucide-react";

interface DateRange {
  from: Date;
  to: Date;
}

interface DateRangeSelectorProps {
  onDateRangeChange: (range: DateRange) => void;
  currentRange: DateRange;
}

export const DateRangeSelector: React.FC<DateRangeSelectorProps> = ({
  onDateRangeChange,
  currentRange,
}) => {
  const { t } = useTranslation();
  const [showCustom, setShowCustom] = useState(false);
  const [customFrom, setCustomFrom] = useState(
    currentRange.from.toISOString().split('T')[0]
  );
  const [customTo, setCustomTo] = useState(
    currentRange.to.toISOString().split('T')[0]
  );

  const getDateRange = (type: string): DateRange => {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);

    switch (type) {
      case 'today':
        return { from: startOfDay, to: endOfDay };
      case 'yesterday':
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        return {
          from: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate()),
          to: new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59)
        };
      case 'thisWeek':
        const startOfWeek = new Date(today);
        startOfWeek.setDate(today.getDate() - today.getDay());
        return {
          from: new Date(startOfWeek.getFullYear(), startOfWeek.getMonth(), startOfWeek.getDate()),
          to: endOfDay
        };
      case 'thisMonth':
        return {
          from: new Date(today.getFullYear(), today.getMonth(), 1),
          to: endOfDay
        };
      default:
        return { from: startOfDay, to: endOfDay };
    }
  };

  const handlePresetClick = (type: string) => {
    const range = getDateRange(type);
    onDateRangeChange(range);
    setShowCustom(false);
  };

  const handleCustomApply = () => {
    const fromDate = new Date(customFrom);
    const toDate = new Date(customTo);
    toDate.setHours(23, 59, 59);
    
    onDateRangeChange({ from: fromDate, to: toDate });
    setShowCustom(false);
  };

  return (
    <Card className="border-2 border-indigo-100 dark:border-indigo-900/30">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <h3 className="font-semibold flex items-center gap-2">
            <Calendar className="h-4 w-4 text-indigo-600" />
            {t("stock.selectDateRange")}
          </h3>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowCustom(!showCustom)}
          >
            <ChevronDown className={`h-4 w-4 transition-transform ${showCustom ? 'rotate-180' : ''}`} />
          </Button>
        </div>

        <div className="flex flex-wrap gap-2 mb-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePresetClick('today')}
            className="text-xs"
          >
            {t("stock.today")}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePresetClick('yesterday')}
            className="text-xs"
          >
            {t("stock.yesterday")}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePresetClick('thisWeek')}
            className="text-xs"
          >
            {t("stock.thisWeek")}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={() => handlePresetClick('thisMonth')}
            className="text-xs"
          >
            {t("stock.thisMonth")}
          </Button>
        </div>

        {showCustom && (
          <div className="space-y-3 pt-3 border-t">
            <div className="grid grid-cols-2 gap-3">
              <div>
                <label className="text-xs font-medium">{t("stock.from")}</label>
                <Input
                  type="date"
                  value={customFrom}
                  onChange={(e) => setCustomFrom(e.target.value)}
                  className="h-8"
                />
              </div>
              <div>
                <label className="text-xs font-medium">{t("stock.to")}</label>
                <Input
                  type="date"
                  value={customTo}
                  onChange={(e) => setCustomTo(e.target.value)}
                  className="h-8"
                />
              </div>
            </div>
            <Button
              onClick={handleCustomApply}
              size="sm"
              className="w-full"
            >
              {t("stock.apply")}
            </Button>
          </div>
        )}

        <div className="text-xs text-muted-foreground mt-2">
          {currentRange.from.toLocaleDateString()} - {currentRange.to.toLocaleDateString()}
        </div>
      </CardContent>
    </Card>
  );
};