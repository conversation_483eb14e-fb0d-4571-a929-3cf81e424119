import React from "react";
import { useTranslation } from "react-i18next";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Plus, Grid3X3 } from "lucide-react";
import { useStock } from "@/context/StockContext";
import { useAuth } from "@/context/AuthContext";
import CategoryFormDialog from "./CategoryFormDialog";

interface CategoryManagementProps {
  viewMode: "grid" | "list";
  touchMode: boolean;
  onViewModeChange: (mode: "grid" | "list") => void;
}

const CategoryManagement: React.FC<CategoryManagementProps> = ({
  touchMode,
}) => {
  const { t } = useTranslation();
  const { categories, products, deleteCategory } = useStock();
  const { hasPermission } = useAuth();

  // Calculate product count for each category
  const categoriesWithCount = categories.map((category) => ({
    ...category,
    productCount: products.filter(
      (product) => product.categoryId === category.id
    ).length,
  }));

  const handleDeleteCategory = async (categoryId: string) => {
    const productsInCategory = products.filter(
      (product) => product.categoryId === categoryId
    );

    if (productsInCategory.length > 0) {
      alert(
        `Cannot delete category. It contains ${productsInCategory.length} products. Please move or delete the products first.`
      );
      return;
    }

    if (window.confirm("Are you sure you want to delete this category?")) {
      try {
        await deleteCategory(categoryId);
      } catch (error) {
        console.error("Error deleting category:", error);
      }
    }
  };

  const buttonSize = touchMode ? "lg" : "default";

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">{t("stock.categories")}</h2>
          <p className="text-gray-600 dark:text-gray-400">
            Organize your products into categories
          </p>
        </div>
        <CategoryFormDialog />
      </div>

      {/* Categories Grid */}
      <div
        className={`grid gap-4 ${
          touchMode
            ? "grid-cols-1 sm:grid-cols-2"
            : "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
        }`}
      >
        {categoriesWithCount.map((category) => (
          <Card key={category.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-2">
                <Grid3X3 className="h-5 w-5 text-blue-600" />
                {category.name}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 dark:text-gray-400 mb-4">
                {category.description}
              </p>
              <div className="flex justify-between items-center">
                <span className="text-sm text-gray-500">
                  {category.productCount} products
                </span>
                <div className="flex gap-2">
                  {hasPermission("manage_users") && (
                    <>
                      <CategoryFormDialog
                        category={category}
                        trigger={
                          <Button variant="outline" size="sm">
                            {t("common.edit")}
                          </Button>
                        }
                      />
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                        onClick={() => handleDeleteCategory(category.id)}
                      >
                        Delete
                      </Button>
                    </>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};

export default CategoryManagement;
