import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Eye, Calendar, DollarSign, ShoppingCart, Printer } from "lucide-react";
import { format } from "date-fns";
import { useStock } from "@/context/StockContext";
import { Sale } from "@/types/stock";
import { printSalesTicket } from "./PrintSalesTicket";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { DailySummary } from "./DailySummary";
import { DateRangeSelector } from "./DateRangeSelector";

interface SalesHistoryProps {
  viewMode: "grid" | "list";
  touchMode: boolean;
  onViewModeChange: (mode: "grid" | "list") => void;
}

const SalesHistory: React.FC<SalesHistoryProps> = ({ touchMode }) => {
  const { t } = useTranslation();
  const { sales, loading } = useStock();
  const { repairShop } = useRepairShopContext();

  // Date range state
  const [dateRange, setDateRange] = useState(() => {
    const today = new Date();
    return {
      from: new Date(today.getFullYear(), today.getMonth(), today.getDate()),
      to: new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate(),
        23,
        59,
        59
      ),
    };
  });

  // Sort sales by date (newest first)
  const sortedSales = [...sales].sort(
    (a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
  );

  const filteredSales = sortedSales.filter((sale) => {
    const saleDate = new Date(sale.createdAt);
    return saleDate >= dateRange.from && saleDate <= dateRange.to;
  });

  const periodRevenue = filteredSales.reduce(
    (sum, sale) => sum + sale.totalAmount,
    0
  );

  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(3)} TND`;
  };

  const handlePrintSale = (sale: Sale) => {
    // Create sale items from the sale data
    const saleItems =
      sale.items?.map((item) => ({
        id: item.id || `temp-${item.productId}`,
        saleId: sale.id,
        productId: item.productId,
        product: item.product,
        quantity: item.quantity,
        unitPrice: item.unitPrice,
        discount: item.discount || 0,
        totalPrice: item.totalPrice,
        createdAt: new Date(),
        updatedAt: new Date(),
      })) || [];

    printSalesTicket(sale, saleItems, repairShop);
  };

  const getPaymentMethodBadge = (method: string) => {
    const variants = {
      cash: "default",
      card: "secondary",
      mobile: "outline",
    } as const;

    return variants[method as keyof typeof variants] || "default";
  };

  const handlePrintSummary = () => {
    window.print();
  };

  const handleExportSummary = () => {
    const today = new Date();
    const summaryData = {
      dateRange: `${dateRange.from.toDateString()} - ${dateRange.to.toDateString()}`,
      totalRevenue: periodRevenue,
      totalTransactions: filteredSales.length,
      sales: filteredSales,
    };
    const blob = new Blob([JSON.stringify(summaryData, null, 2)], {
      type: "application/json",
    });
    const url = URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `sales-summary-${
      dateRange.from.toISOString().split("T")[0]
    }-to-${dateRange.to.toISOString().split("T")[0]}.json`;
    a.click();
  };

  return (
    <div className="space-y-6">
      {/* Date Range Selector */}
      <DateRangeSelector
        currentRange={dateRange}
        onDateRangeChange={setDateRange}
      />

      {/* Period Summary */}
      <DailySummary
        todaysSales={filteredSales}
        onPrintSummary={handlePrintSummary}
        onExportSummary={handleExportSummary}
        dateRange={dateRange}
      />

      {/* Today's Summary */}
      <div
        className={`grid gap-4 ${touchMode ? "grid-cols-2" : "grid-cols-3"}`}
      >
        <Card>
          <CardContent className={`${touchMode ? "p-6" : "p-4"} text-center`}>
            <ShoppingCart className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <p className="text-2xl font-bold">{filteredSales.length}</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t("stock.todaysSales")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className={`${touchMode ? "p-6" : "p-4"} text-center`}>
            <DollarSign className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-green-600">
              {formatCurrency(periodRevenue)}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t("stock.todaysRevenue")}
            </p>
          </CardContent>
        </Card>

        {!touchMode && (
          <Card>
            <CardContent className="p-4 text-center">
              <Calendar className="h-8 w-8 text-purple-600 mx-auto mb-2" />
              <p className="text-2xl font-bold">{sortedSales.length}</p>
              <p className="text-sm text-gray-600 dark:text-gray-400">
                {t("stock.totalSales")}
              </p>
            </CardContent>
          </Card>
        )}
      </div>

      {/* Sales List */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Recent Sales
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="border-b">
                <tr className="text-left">
                  <th className={`${touchMode ? "p-4" : "p-3"} font-medium`}>
                    Sale ID
                  </th>
                  <th className={`${touchMode ? "p-4" : "p-3"} font-medium`}>
                    Customer
                  </th>
                  <th className={`${touchMode ? "p-4" : "p-3"} font-medium`}>
                    Items
                  </th>
                  <th className={`${touchMode ? "p-4" : "p-3"} font-medium`}>
                    Total
                  </th>
                  <th className={`${touchMode ? "p-4" : "p-3"} font-medium`}>
                    Payment
                  </th>
                  <th className={`${touchMode ? "p-4" : "p-3"} font-medium`}>
                    Date
                  </th>
                  <th className={`${touchMode ? "p-4" : "p-3"} font-medium`}>
                    Actions
                  </th>
                </tr>
              </thead>
              <tbody>
                {loading ? (
                  <tr>
                    <td colSpan={7} className="text-center py-8">
                      {t("common.loading")}...
                    </td>
                  </tr>
                ) : sortedSales.length === 0 ? (
                  <tr>
                    <td colSpan={7} className="text-center py-8 text-gray-500">
                      {t("stock.noSalesYet")}
                    </td>
                  </tr>
                ) : (
                  sortedSales.map((sale) => (
                    <tr
                      key={sale.id}
                      className="border-b hover:bg-gray-50 dark:hover:bg-gray-800/50"
                    >
                      <td
                        className={`${
                          touchMode ? "p-4" : "p-3"
                        } font-mono text-sm`}
                      >
                        {sale.saleNumber}
                      </td>
                      <td className={`${touchMode ? "p-4" : "p-3"}`}>
                        <div>
                          <p className="font-medium">
                            {sale.customerName || t("stock.walkInCustomer")}
                          </p>
                          {sale.customerPhone && (
                            <p className="text-sm text-gray-600 dark:text-gray-400">
                              {sale.customerPhone}
                            </p>
                          )}
                        </div>
                      </td>
                      <td className={`${touchMode ? "p-4" : "p-3"}`}>
                        <Badge variant="outline">
                          {sale.items?.length || 0} {t("stock.items")}
                        </Badge>
                      </td>
                      <td
                        className={`${
                          touchMode ? "p-4" : "p-3"
                        } font-semibold text-green-600`}
                      >
                        {formatCurrency(sale.totalAmount)}
                      </td>
                      <td className={`${touchMode ? "p-4" : "p-3"}`}>
                        <Badge
                          variant={getPaymentMethodBadge(sale.paymentMethod)}
                        >
                          {t(`stock.${sale.paymentMethod}`)}
                        </Badge>
                      </td>
                      <td className={`${touchMode ? "p-4" : "p-3"} text-sm`}>
                        <div>
                          <p>
                            {format(new Date(sale.createdAt), "MMM dd, yyyy")}
                          </p>
                          <p className="text-gray-500">
                            {format(new Date(sale.createdAt), "HH:mm")}
                          </p>
                        </div>
                      </td>
                      <td className={`${touchMode ? "p-4" : "p-3"}`}>
                        <div className="flex gap-1">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handlePrintSale(sale)}
                            title={t("stock.printTicket")}
                          >
                            <Printer className="h-3 w-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))
                )}
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default SalesHistory;
