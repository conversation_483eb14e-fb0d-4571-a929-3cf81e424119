import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Scan, Keyboard, Zap, Info } from 'lucide-react';

interface ScannerInstructionsProps {
  touchMode?: boolean;
}

const ScannerInstructions: React.FC<ScannerInstructionsProps> = ({ touchMode = false }) => {
  const { t } = useTranslation();

  return (
    <Alert className="border-blue-200 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-800">
      <Info className="h-4 w-4 text-blue-600 dark:text-blue-400" />
      <AlertDescription className="text-blue-800 dark:text-blue-200">
        <div className="space-y-2">
          <p className="font-medium">{t('stock.scannerInstructions')}</p>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <Scan className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <span>{t('stock.useBarcodeScannerDevice')}</span>
            </div>
            <div className="flex items-center gap-2">
              <Keyboard className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <span>{t('stock.typeBarcodeDirect')}</span>
            </div>
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              <span>{t('stock.automaticCartAdd')}</span>
            </div>
          </div>
        </div>
      </AlertDescription>
    </Alert>
  );
};

export default ScannerInstructions;
