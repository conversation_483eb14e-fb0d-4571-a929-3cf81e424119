import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Plus,
  Grid3X3,
  Smartphone,
  Cable,
  Shield,
  Wrench,
  Headphones,
  Palette,
} from "lucide-react";
import { useStock } from "@/context/StockContext";
import { CategoryFormData, ProductCategory } from "@/types/stock";
import { toast } from "sonner";
import StockPasswordDialog from "./StockPasswordDialog";

interface CategoryFormDialogProps {
  category?: ProductCategory;
  trigger?: React.ReactNode;
  onSuccess?: () => void;
}

const CategoryFormDialog: React.FC<CategoryFormDialogProps> = ({
  category,
  trigger,
  onSuccess,
}) => {
  const { t } = useTranslation();
  const { addCategory, updateCategory, loading } = useStock();
  const [open, setOpen] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [passwordDialogOpen, setPasswordDialogOpen] = useState(false);
  const [pendingFormData, setPendingFormData] =
    useState<CategoryFormData | null>(null);

  const [formData, setFormData] = useState<CategoryFormData>({
    name: "",
    description: "",
    icon: "package",
    color: "blue",
  });

  // Available icons for categories
  const iconOptions = [
    { value: "smartphone", label: "Smartphone", icon: Smartphone },
    { value: "cable", label: "Cable", icon: Cable },
    { value: "shield", label: "Shield", icon: Shield },
    { value: "wrench", label: "Wrench", icon: Wrench },
    { value: "headphones", label: "Headphones", icon: Headphones },
    { value: "grid3x3", label: "Grid", icon: Grid3X3 },
  ];

  // Available colors for categories
  const colorOptions = [
    { value: "blue", label: "Blue", class: "bg-blue-500" },
    { value: "green", label: "Green", class: "bg-green-500" },
    { value: "purple", label: "Purple", class: "bg-purple-500" },
    { value: "orange", label: "Orange", class: "bg-orange-500" },
    { value: "red", label: "Red", class: "bg-red-500" },
    { value: "yellow", label: "Yellow", class: "bg-yellow-500" },
    { value: "pink", label: "Pink", class: "bg-pink-500" },
    { value: "indigo", label: "Indigo", class: "bg-indigo-500" },
  ];

  // Reset form when dialog opens/closes or category changes
  useEffect(() => {
    if (category) {
      setFormData({
        name: category.name,
        description: category.description || "",
        icon: category.icon || "grid3x3",
        color: category.color || "blue",
      });
    } else {
      setFormData({
        name: "",
        description: "",
        icon: "grid3x3",
        color: "blue",
      });
    }
  }, [category, open]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.name.trim()) {
      toast.error("Category name is required");
      return;
    }

    // Store form data and show password dialog
    setPendingFormData(formData);
    setPasswordDialogOpen(true);
  };

  const handlePasswordConfirm = async () => {
    if (!pendingFormData) return;

    setSubmitting(true);

    try {
      if (category) {
        await updateCategory(category.id, pendingFormData);
        toast.success(t("stock.categoryUpdated"));
      } else {
        await addCategory(pendingFormData);
        toast.success(t("stock.categoryAdded"));
      }

      setOpen(false);
      onSuccess?.();
    } catch (error) {
      console.error("Error saving category:", error);
      toast.error(t("common.error"));
    } finally {
      setSubmitting(false);
      setPendingFormData(null);
    }
  };

  const handleInputChange = (field: keyof CategoryFormData, value: any) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const getIconComponent = (iconName: string) => {
    const iconOption = iconOptions.find((opt) => opt.value === iconName);
    return iconOption ? iconOption.icon : Grid3X3;
  };

  const defaultTrigger = (
    <Button className="flex items-center gap-2">
      <Plus className="h-4 w-4" />
      {t("stock.addCategory")}
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Grid3X3 className="h-5 w-5" />
            {category ? t("stock.editCategory") : t("stock.addCategory")}
          </DialogTitle>
        </DialogHeader>

        <form onSubmit={handleSubmit} className="space-y-4">
          {/* Category Name */}
          <div className="space-y-2">
            <Label htmlFor="name">{t("stock.categoryName")} *</Label>
            <Input
              id="name"
              value={formData.name}
              onChange={(e) => handleInputChange("name", e.target.value)}
              placeholder="Enter category name"
              required
            />
          </div>

          {/* Category Description */}
          <div className="space-y-2">
            <Label htmlFor="description">
              {t("stock.categoryDescription")}
            </Label>
            <Textarea
              id="description"
              value={formData.description}
              onChange={(e) => handleInputChange("description", e.target.value)}
              placeholder="Enter category description"
              rows={3}
            />
          </div>

          {/* Icon Selection */}
          <div className="space-y-2">
            <Label htmlFor="icon">Icon</Label>
            <Select
              value={formData.icon}
              onValueChange={(value) => handleInputChange("icon", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select icon" />
              </SelectTrigger>
              <SelectContent>
                {iconOptions.map((option) => {
                  const IconComponent = option.icon;
                  return (
                    <SelectItem key={option.value} value={option.value}>
                      <div className="flex items-center gap-2">
                        <IconComponent className="h-4 w-4" />
                        {option.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
          </div>

          {/* Color Selection */}
          <div className="space-y-2">
            <Label htmlFor="color">Color</Label>
            <Select
              value={formData.color}
              onValueChange={(value) => handleInputChange("color", value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select color" />
              </SelectTrigger>
              <SelectContent>
                {colorOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center gap-2">
                      <div className={`w-4 h-4 rounded-full ${option.class}`} />
                      {option.label}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Preview */}
          <div className="space-y-2">
            <Label>Preview</Label>
            <div className="flex items-center gap-3 p-3 border rounded-lg bg-gray-50 dark:bg-gray-800">
              <div
                className={`p-2 rounded-lg bg-${formData.color}-100 dark:bg-${formData.color}-900/30`}
              >
                {React.createElement(
                  getIconComponent(formData.icon || "grid3x3"),
                  {
                    className: `h-5 w-5 text-${formData.color}-600 dark:text-${formData.color}-400`,
                  }
                )}
              </div>
              <div>
                <h4 className="font-medium">
                  {formData.name || "Category Name"}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  {formData.description || "Category description"}
                </p>
              </div>
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-2 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => setOpen(false)}
              disabled={submitting}
            >
              Cancel
            </Button>
            <Button type="submit" disabled={submitting || loading}>
              {submitting
                ? "Saving..."
                : category
                ? "Update Category"
                : "Add Category"}
            </Button>
          </div>
        </form>
      </DialogContent>

      <StockPasswordDialog
        open={passwordDialogOpen}
        onOpenChange={setPasswordDialogOpen}
        onConfirm={handlePasswordConfirm}
        title={
          category
            ? t("stock.confirmCategoryUpdate")
            : t("stock.confirmCategoryCreation")
        }
        description={
          category
            ? t("stock.categoryUpdateWarning")
            : t("stock.categoryCreationWarning")
        }
        confirmButtonText={
          category ? t("stock.updateCategory") : t("stock.addCategory")
        }
      />
    </Dialog>
  );
};

export default CategoryFormDialog;
