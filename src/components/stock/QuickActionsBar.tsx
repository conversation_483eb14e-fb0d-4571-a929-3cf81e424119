import React from "react";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import {
  ShoppingCart,
  Calculator,
  Trash2,
  Pause,
  Clock,
  Percent,
  FileText,
  CreditCard,
} from "lucide-react";

interface QuickActionsBarProps {
  cartLength: number;
  heldOrdersLength: number;
  onClearCart: () => void;
  onHoldOrder: () => void;
  onShowHeldOrders: () => void;
  onShowDiscount: () => void;
  onShowNotes: () => void;
  onProcessSale: () => void;
  total: number;
  canProcessSale: boolean;
  touchMode: boolean;
}

export const QuickActionsBar: React.FC<QuickActionsBarProps> = ({
  cartLength,
  heldOrdersLength,
  onClearCart,
  onHoldOrder,
  onShowHeldOrders,
  onShowDiscount,
  onShowNotes,
  onProcessSale,
  total,
  canProcessSale,
  touchMode,
}) => {
  const { t } = useTranslation();

  if (cartLength === 0 && heldOrdersLength === 0) return null;

  return (
    <div className="fixed bottom-6 right-6 z-[100]">
      <div className="flex flex-col gap-2">
        {/* Primary Actions */}
        {cartLength > 0 && (
          <>
            <Button
              onClick={onProcessSale}
              disabled={!canProcessSale}
              size={touchMode ? "lg" : "default"}
              className="bg-green-600 hover:bg-green-700 text-white shadow-lg min-w-[140px]"
            >
              <CreditCard className="h-4 w-4 mr-2" />
              {total.toFixed(3)} TND
            </Button>
            
            <div className="flex gap-2">
              <Button
                onClick={onShowDiscount}
                variant="outline"
                size={touchMode ? "lg" : "default"}
                className="bg-white shadow-lg"
              >
                <Percent className="h-4 w-4" />
              </Button>
              
              <Button
                onClick={onShowNotes}
                variant="outline"
                size={touchMode ? "lg" : "default"}
                className="bg-white shadow-lg"
              >
                <FileText className="h-4 w-4" />
              </Button>
            </div>

            <div className="flex gap-2">
              <Button
                onClick={onHoldOrder}
                variant="outline"
                size={touchMode ? "lg" : "default"}
                className="bg-blue-50 hover:bg-blue-100 text-blue-600 shadow-lg"
              >
                <Pause className="h-4 w-4" />
              </Button>
              
              <Button
                onClick={onClearCart}
                variant="outline"
                size={touchMode ? "lg" : "default"}
                className="bg-red-50 hover:bg-red-100 text-red-600 shadow-lg"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </>
        )}

        {/* Held Orders */}
        {heldOrdersLength > 0 && (
          <Button
            onClick={onShowHeldOrders}
            variant="outline"
            size={touchMode ? "lg" : "default"}
            className="bg-purple-50 hover:bg-purple-100 text-purple-600 shadow-lg relative"
          >
            <Clock className="h-4 w-4 mr-2" />
            {heldOrdersLength}
            <span className="absolute -top-2 -right-2 bg-purple-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {heldOrdersLength}
            </span>
          </Button>
        )}
      </div>
    </div>
  );
};