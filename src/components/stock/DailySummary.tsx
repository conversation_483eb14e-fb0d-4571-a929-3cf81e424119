import React from "react";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useTranslation } from "react-i18next";
import {
  TrendingUp,
  DollarSign,
  ShoppingCart,
  Users,
  Clock,
  Printer,
  Download,
} from "lucide-react";
import { printDailySummaryReport } from "./DailySummaryReport";

interface DailySummaryProps {
  todaysSales: any[];
  onPrintSummary: () => void;
  onExportSummary: () => void;
  dateRange: { from: Date; to: Date };
}

export const DailySummary: React.FC<DailySummaryProps> = ({
  todaysSales,
  onPrintSummary,
  onExportSummary,
  dateRange,
}) => {
  const { t } = useTranslation();

  const totalRevenue = todaysSales.reduce((sum, sale) => sum + sale.totalAmount, 0);
  const totalTransactions = todaysSales.length;
  const averageTransaction = totalTransactions > 0 ? totalRevenue / totalTransactions : 0;
  const totalItems = todaysSales.reduce((sum, sale) => sum + sale.items?.length || 0, 0);

  // Payment method breakdown
  const paymentMethods = todaysSales.reduce((acc, sale) => {
    acc[sale.paymentMethod] = (acc[sale.paymentMethod] || 0) + sale.totalAmount;
    return acc;
  }, {});

  // Hourly breakdown
  const hourlyData = todaysSales.reduce((acc, sale) => {
    const hour = new Date(sale.createdAt).getHours();
    acc[hour] = (acc[hour] || 0) + sale.totalAmount;
    return acc;
  }, {});

  const peakHour = Object.entries(hourlyData).reduce((peak, [hour, amount]) => 
    amount > (peak.amount || 0) ? { hour: parseInt(hour), amount } : peak, {}
  );

  return (
    <Card className="border-2 border-purple-100 dark:border-purple-900/30">
      <CardHeader className="pb-3 bg-gradient-to-r from-purple-50 to-violet-50 dark:from-purple-900/10 dark:to-violet-900/10">
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2 text-lg font-bold text-purple-900 dark:text-purple-100">
            <TrendingUp className="h-5 w-5 text-purple-600 dark:text-purple-400" />
            {t("stock.dailySummary")}
          </CardTitle>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => printDailySummaryReport({ sales: todaysSales, dateRange })}
              className="text-purple-600 hover:text-purple-700"
            >
              <Printer className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={onExportSummary}
              className="text-purple-600 hover:text-purple-700"
            >
              <Download className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-6 space-y-4">
        {/* Key Metrics */}
        <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
          <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <DollarSign className="h-4 w-4 text-green-600" />
              <span className="text-xs font-medium text-green-700 dark:text-green-300">
                {t("stock.totalRevenue")}
              </span>
            </div>
            <p className="text-lg font-bold text-green-800 dark:text-green-200">
              {totalRevenue.toFixed(3)} TND
            </p>
          </div>

          <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <ShoppingCart className="h-4 w-4 text-blue-600" />
              <span className="text-xs font-medium text-blue-700 dark:text-blue-300">
                {t("stock.transactions")}
              </span>
            </div>
            <p className="text-lg font-bold text-blue-800 dark:text-blue-200">
              {totalTransactions}
            </p>
          </div>

          <div className="bg-orange-50 dark:bg-orange-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <TrendingUp className="h-4 w-4 text-orange-600" />
              <span className="text-xs font-medium text-orange-700 dark:text-orange-300">
                {t("stock.avgTransaction")}
              </span>
            </div>
            <p className="text-lg font-bold text-orange-800 dark:text-orange-200">
              {averageTransaction.toFixed(3)} TND
            </p>
          </div>

          <div className="bg-purple-50 dark:bg-purple-900/20 p-4 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <Users className="h-4 w-4 text-purple-600" />
              <span className="text-xs font-medium text-purple-700 dark:text-purple-300">
                {t("stock.itemsSold")}
              </span>
            </div>
            <p className="text-lg font-bold text-purple-800 dark:text-purple-200">
              {totalItems}
            </p>
          </div>
        </div>

        {/* Payment Methods */}
        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
          <h4 className="font-semibold text-sm mb-3 flex items-center gap-2">
            <DollarSign className="h-4 w-4" />
            {t("stock.paymentBreakdown")}
          </h4>
          <div className="space-y-2">
            {Object.entries(paymentMethods).map(([method, amount]) => (
              <div key={method} className="flex justify-between items-center">
                <span className="text-sm capitalize">{method}</span>
                <span className="font-semibold">{(amount as number).toFixed(3)} TND</span>
              </div>
            ))}
          </div>
        </div>

        {/* Peak Hour */}
        {peakHour.hour !== undefined && (
          <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
            <h4 className="font-semibold text-sm mb-2 flex items-center gap-2">
              <Clock className="h-4 w-4 text-yellow-600" />
              {t("stock.peakHour")}
            </h4>
            <p className="text-sm">
              <span className="font-bold">{peakHour.hour}:00 - {peakHour.hour + 1}:00</span>
              <span className="text-muted-foreground ml-2">
                ({(peakHour.amount as number).toFixed(3)} TND)
              </span>
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
};