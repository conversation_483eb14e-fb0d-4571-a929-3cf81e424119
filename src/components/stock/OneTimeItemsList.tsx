import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  Smartphone,
  Laptop,
  Monitor,
  HardDrive,
  Battery,
  Hash,
  Palette,
  FileText,
  Star,
  Search,
  Plus,
  Edit,
  Trash2,
  Filter,
  Eye,
  ShoppingCart,
} from "lucide-react";
import { Product, OneTimeItemData } from "@/types/stock";
import { toast } from "sonner";

interface OneTimeItemsListProps {
  products: Product[];
  onAddItem: () => void;
  onEditItem: (product: Product) => void;
  onDeleteItem: (productId: string) => void;
  onAddToCart?: (product: Product) => void;
  loading?: boolean;
}

const deviceTypeIcons = {
  iphone: Smartphone,
  android: Smartphone,
  mac: Monitor,
  pc: Monitor,
  laptop: Laptop,
  other: HardDrive,
};

const conditionColors = {
  excellent: "bg-green-100 text-green-800 border-green-200",
  good: "bg-blue-100 text-blue-800 border-blue-200",
  fair: "bg-yellow-100 text-yellow-800 border-yellow-200",
  poor: "bg-red-100 text-red-800 border-red-200",
};

const OneTimeItemsList: React.FC<OneTimeItemsListProps> = ({
  products,
  onAddItem,
  onEditItem,
  onDeleteItem,
  onAddToCart,
  loading = false,
}) => {
  const { t } = useTranslation();
  const [searchTerm, setSearchTerm] = useState("");
  const [deviceTypeFilter, setDeviceTypeFilter] = useState<string>("all");
  const [conditionFilter, setConditionFilter] = useState<string>("all");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [itemToDelete, setItemToDelete] = useState<Product | null>(null);

  // Filter one-time items
  const oneTimeItems = products.filter((product) => product.isOneTimeItem);

  // Apply filters
  const filteredItems = oneTimeItems.filter((product) => {
    const matchesSearch =
      product.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      product.sku.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (product.oneTimeItemData?.serialNumber &&
        product.oneTimeItemData.serialNumber
          .toLowerCase()
          .includes(searchTerm.toLowerCase())) ||
      (product.oneTimeItemData?.model &&
        product.oneTimeItemData.model
          .toLowerCase()
          .includes(searchTerm.toLowerCase()));

    const matchesDeviceType =
      deviceTypeFilter === "all" ||
      product.oneTimeItemData?.deviceType === deviceTypeFilter;

    const matchesCondition =
      conditionFilter === "all" ||
      product.oneTimeItemData?.condition === conditionFilter;

    const matchesStatus =
      statusFilter === "all" ||
      (statusFilter === "available" &&
        product.isActive &&
        !product.oneTimeItemData?.isSold) ||
      (statusFilter === "sold" && product.oneTimeItemData?.isSold) ||
      (statusFilter === "inactive" && !product.isActive);

    return (
      matchesSearch && matchesDeviceType && matchesCondition && matchesStatus
    );
  });

  const handleDeleteClick = (product: Product) => {
    setItemToDelete(product);
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (itemToDelete) {
      onDeleteItem(itemToDelete.id);
      setDeleteDialogOpen(false);
      setItemToDelete(null);
    }
  };

  const getStatusBadge = (product: Product) => {
    if (product.oneTimeItemData?.isSold) {
      return (
        <Badge variant="secondary" className="bg-gray-100 text-gray-800">
          {t("stock.sold")}
        </Badge>
      );
    }
    if (!product.isActive) {
      return (
        <Badge variant="destructive" className="bg-red-100 text-red-800">
          {t("stock.inactive")}
        </Badge>
      );
    }
    return (
      <Badge variant="default" className="bg-green-100 text-green-800">
        {t("stock.available")}
      </Badge>
    );
  };

  const renderItemCard = (product: Product) => {
    const DeviceIcon =
      deviceTypeIcons[product.oneTimeItemData?.deviceType || "other"];
    const isAvailable = product.isActive && !product.oneTimeItemData?.isSold;

    return (
      <Card
        key={product.id}
        className={`transition-all hover:shadow-md ${
          !isAvailable ? "opacity-75" : ""
        }`}
      >
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="flex items-center gap-2">
              <DeviceIcon className="h-5 w-5 text-gray-600" />
              <div>
                <CardTitle className="text-lg">{product.name}</CardTitle>
                <p className="text-sm text-gray-600 mt-1">SKU: {product.sku}</p>
              </div>
            </div>
            <div className="flex items-center gap-2">
              {getStatusBadge(product)}
              <Badge className="text-xs">{t("stock.oneTimeItem")}</Badge>
            </div>
          </div>
        </CardHeader>

        <CardContent className="space-y-4">
          {/* Device Information */}
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div className="space-y-2">
              {product.oneTimeItemData?.model && (
                <div className="flex items-center gap-2">
                  <span className="text-gray-600">{t("stock.model")}:</span>
                  <span className="font-medium">
                    {product.oneTimeItemData.model}
                  </span>
                </div>
              )}
              {product.oneTimeItemData?.serialNumber && (
                <div className="flex items-center gap-2">
                  <Hash className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-600">
                    {product.oneTimeItemData.deviceType === "iphone" ||
                    product.oneTimeItemData.deviceType === "android"
                      ? t("stock.imei")
                      : t("stock.serial")}
                    :
                  </span>
                  <span className="font-mono text-xs">
                    {product.oneTimeItemData.serialNumber}
                  </span>
                </div>
              )}
              {product.oneTimeItemData?.storage && (
                <div className="flex items-center gap-2">
                  <HardDrive className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-600">{t("stock.storage")}:</span>
                  <span className="font-medium">
                    {product.oneTimeItemData.storage}
                  </span>
                </div>
              )}
            </div>

            <div className="space-y-2">
              {product.oneTimeItemData?.color && (
                <div className="flex items-center gap-2">
                  <Palette className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-600">{t("stock.color")}:</span>
                  <span className="font-medium">
                    {product.oneTimeItemData.color}
                  </span>
                </div>
              )}
              {product.oneTimeItemData?.batteryPercentage && (
                <div className="flex items-center gap-2">
                  <Battery className="h-3 w-3 text-gray-400" />
                  <span className="text-gray-600">{t("stock.battery")}:</span>
                  <span className="font-medium">
                    {product.oneTimeItemData.batteryPercentage}%
                  </span>
                </div>
              )}
              <div className="flex items-center gap-2">
                <Star className="h-3 w-3 text-gray-400" />
                <span className="text-gray-600">{t("stock.condition")}:</span>
                <Badge
                  className={`text-xs ${
                    conditionColors[
                      product.oneTimeItemData?.condition || "good"
                    ]
                  }`}
                >
                  {t(`stock.${product.oneTimeItemData?.condition || "good"}`)}
                </Badge>
              </div>
            </div>
          </div>

          {/* Notes */}
          {product.oneTimeItemData?.notes && (
            <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
              <div className="flex items-start gap-2">
                <FileText className="h-4 w-4 text-gray-400 mt-0.5" />
                <div>
                  <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {t("stock.notes")}:
                  </p>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                    {product.oneTimeItemData.notes}
                  </p>
                </div>
              </div>
            </div>
          )}

          {/* Price and Actions */}
          <div className="flex items-center justify-between pt-2 border-t">
            <div className="text-right">
              <div className="text-2xl font-bold text-green-600">
                {product.price.toFixed(3)} TND
              </div>
              {product.cost > 0 && (
                <div className="text-sm text-gray-600">
                  {t("stock.profit")}:{" "}
                  {(product.price - product.cost).toFixed(3)} TND
                </div>
              )}
            </div>

            <div className="flex items-center gap-2">
              {onAddToCart && isAvailable && (
                <Button
                  size="sm"
                  onClick={() => onAddToCart(product)}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <ShoppingCart className="h-4 w-4 mr-1" />
                  {t("stock.addToCart")}
                </Button>
              )}
              <Button
                size="sm"
                variant="outline"
                onClick={() => onEditItem(product)}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <Button
                size="sm"
                variant="outline"
                onClick={() => handleDeleteClick(product)}
                className="text-red-600 hover:text-red-700"
              >
                <Trash2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">{t("stock.oneTimeItems")}</h2>
          <p className="text-gray-600 mt-1">
            {t("stock.oneTimeItemsDescription")}
          </p>
        </div>
        <Button onClick={onAddItem} className="flex items-center gap-2">
          <Plus className="h-4 w-4" />
          {t("stock.addOneTimeItem")}
        </Button>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            {t("stock.filters")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <div className="space-y-2">
              <label className="text-sm font-medium">{t("stock.search")}</label>
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                <Input
                  placeholder={t("stock.searchOneTimeItems")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">
                {t("stock.deviceType")}
              </label>
              <Select
                value={deviceTypeFilter}
                onValueChange={setDeviceTypeFilter}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("stock.allDevices")}</SelectItem>
                  <SelectItem value="iphone">iPhone</SelectItem>
                  <SelectItem value="android">Android</SelectItem>
                  <SelectItem value="mac">Mac</SelectItem>
                  <SelectItem value="pc">PC</SelectItem>
                  <SelectItem value="laptop">Laptop</SelectItem>
                  <SelectItem value="other">{t("stock.other")}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">
                {t("stock.condition")}
              </label>
              <Select
                value={conditionFilter}
                onValueChange={setConditionFilter}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">
                    {t("stock.allConditions")}
                  </SelectItem>
                  <SelectItem value="excellent">
                    {t("stock.excellent")}
                  </SelectItem>
                  <SelectItem value="good">{t("stock.good")}</SelectItem>
                  <SelectItem value="fair">{t("stock.fair")}</SelectItem>
                  <SelectItem value="poor">{t("stock.poor")}</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <label className="text-sm font-medium">{t("stock.status")}</label>
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">{t("stock.allStatuses")}</SelectItem>
                  <SelectItem value="available">
                    {t("stock.available")}
                  </SelectItem>
                  <SelectItem value="sold">{t("stock.sold")}</SelectItem>
                  <SelectItem value="inactive">
                    {t("stock.inactive")}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Items Grid */}
      {loading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-6 bg-gray-200 rounded w-3/4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-4 bg-gray-200 rounded"></div>
                  <div className="h-4 bg-gray-200 rounded w-2/3"></div>
                  <div className="h-8 bg-gray-200 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredItems.length === 0 ? (
        <Card>
          <CardContent className="text-center py-12">
            <div className="flex flex-col items-center gap-4">
              <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                <Smartphone className="h-8 w-8 text-gray-400" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">
                  {oneTimeItems.length === 0
                    ? t("stock.noOneTimeItems")
                    : t("stock.noMatchingItems")}
                </h3>
                <p className="text-gray-600 mt-1">
                  {oneTimeItems.length === 0
                    ? t("stock.addFirstOneTimeItem")
                    : t("stock.tryDifferentFilters")}
                </p>
              </div>
              {oneTimeItems.length === 0 && (
                <Button onClick={onAddItem} className="mt-4">
                  <Plus className="h-4 w-4 mr-2" />
                  {t("stock.addOneTimeItem")}
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredItems.map(renderItemCard)}
        </div>
      )}

      {/* Summary */}
      {filteredItems.length > 0 && (
        <Card>
          <CardContent className="pt-6">
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
              <div>
                <div className="text-2xl font-bold text-blue-600">
                  {filteredItems.length}
                </div>
                <div className="text-sm text-gray-600">
                  {t("stock.totalItems")}
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {
                    filteredItems.filter(
                      (p) => p.isActive && !p.oneTimeItemData?.isSold
                    ).length
                  }
                </div>
                <div className="text-sm text-gray-600">
                  {t("stock.available")}
                </div>
              </div>
              <div>
                <div className="text-2xl font-bold text-gray-600">
                  {
                    filteredItems.filter((p) => p.oneTimeItemData?.isSold)
                      .length
                  }
                </div>
                <div className="text-sm text-gray-600">{t("stock.sold")}</div>
              </div>
              <div>
                <div className="text-2xl font-bold text-green-600">
                  {filteredItems
                    .filter((p) => p.isActive && !p.oneTimeItemData?.isSold)
                    .reduce((sum, p) => sum + p.price, 0)
                    .toFixed(0)}{" "}
                  TND
                </div>
                <div className="text-sm text-gray-600">
                  {t("stock.totalValue")}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>{t("stock.deleteOneTimeItem")}</AlertDialogTitle>
            <AlertDialogDescription>
              {t("stock.deleteOneTimeItemConfirmation", {
                name: itemToDelete?.name,
              })}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>{t("common.cancel")}</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDeleteConfirm}
              className="bg-red-600 hover:bg-red-700"
            >
              {t("common.delete")}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
};

export default OneTimeItemsList;
