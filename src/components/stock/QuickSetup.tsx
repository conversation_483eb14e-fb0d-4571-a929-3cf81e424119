import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { CheckCircle, AlertTriangle, Plus } from "lucide-react";
import { toast } from "sonner";

const QuickSetup: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  const { repairShop } = useRepairShopContext();
  const [loading, setLoading] = useState(false);
  const [setupStatus, setSetupStatus] = useState<{
    categories: boolean;
    products: boolean;
    canCreateSale: boolean;
  } | null>(null);

  const checkSetup = async () => {
    if (!user || !repairShop) {
      toast.error(t("stock.userOrShopNotFound"));
      return;
    }

    setLoading(true);
    try {
      // Check categories
      const { data: categories, error: catError } = await supabase
        .from("product_categories")
        .select("*")
        .eq("repair_shop_id", repairShop.id);

      // Check products
      const { data: products, error: prodError } = await supabase
        .from("products")
        .select("*")
        .eq("repair_shop_id", repairShop.id)
        .eq("is_active", true);

      if (catError || prodError) {
        toast.error("Database tables not found. Please run the setup script.");
        return;
      }

      setSetupStatus({
        categories: categories.length > 0,
        products: products.length > 0,
        canCreateSale: categories.length > 0 && products.length > 0,
      });
    } catch (error) {
      console.error("Setup check error:", error);
      toast.error(t("stock.errorCheckingSetup"));
    } finally {
      setLoading(false);
    }
  };

  const createSampleData = async () => {
    if (!user || !repairShop) return;

    setLoading(true);
    try {
      // Create sample categories
      const sampleCategories = [
        {
          name: "Mobile Phones",
          description: "Smartphones and feature phones",
          icon: "smartphone",
          color: "blue",
        },
        {
          name: "Accessories",
          description: "Cases, chargers, cables, etc.",
          icon: "cable",
          color: "green",
        },
        {
          name: "Screen Protectors",
          description: "Tempered glass and film protectors",
          icon: "shield",
          color: "purple",
        },
      ];

      const { data: createdCategories, error: catError } = await supabase
        .from("product_categories")
        .insert(
          sampleCategories.map((cat) => ({
            ...cat,
            repair_shop_id: repairShop.id,
          }))
        )
        .select();

      if (catError) throw catError;

      // Create sample products
      const sampleProducts = [
        {
          name: "iPhone 15 Pro",
          description: "Latest iPhone model",
          sku: "IP15P-128",
          category_id: createdCategories[0].id,
          price: 999.99,
          cost: 800.0,
          stock_quantity: 5,
          min_stock_level: 2,
          is_active: true,
          repair_shop_id: repairShop.id,
        },
        {
          name: "Phone Case - Clear",
          description: "Transparent protective case",
          sku: "CASE-CLR",
          category_id: createdCategories[1].id,
          price: 24.99,
          cost: 10.0,
          stock_quantity: 50,
          min_stock_level: 10,
          is_active: true,
          repair_shop_id: repairShop.id,
        },
        {
          name: "Screen Protector",
          description: "Tempered glass screen protector",
          sku: "SCREEN-GLASS",
          category_id: createdCategories[2].id,
          price: 12.99,
          cost: 4.0,
          stock_quantity: 30,
          min_stock_level: 10,
          is_active: true,
          repair_shop_id: repairShop.id,
        },
      ];

      const { error: prodError } = await supabase
        .from("products")
        .insert(sampleProducts);

      if (prodError) throw prodError;

      toast.success(t("stock.sampleDataCreatedSuccessfully"));
      await checkSetup();
    } catch (error) {
      console.error("Error creating sample data:", error);
      toast.error(t("stock.errorCreatingSampleData"));
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>🚀 Quick Setup</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={checkSetup} disabled={loading}>
              {loading ? t("stock.checking") : t("stock.checkSetup")}
            </Button>
            <Button
              onClick={createSampleData}
              disabled={loading}
              variant="outline"
            >
              <Plus className="h-4 w-4 mr-2" />
              {t("stock.createSampleData")}
            </Button>
          </div>

          {setupStatus && (
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                {setupStatus.categories ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                )}
                <span>
                  Categories:{" "}
                  {setupStatus.categories ? "✅ Ready" : "❌ Missing"}
                </span>
              </div>

              <div className="flex items-center gap-2">
                {setupStatus.products ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                )}
                <span>
                  Products: {setupStatus.products ? "✅ Ready" : "❌ Missing"}
                </span>
              </div>

              <div className="flex items-center gap-2">
                {setupStatus.canCreateSale ? (
                  <CheckCircle className="h-5 w-5 text-green-500" />
                ) : (
                  <AlertTriangle className="h-5 w-5 text-yellow-500" />
                )}
                <span>
                  Sales Ready:{" "}
                  {setupStatus.canCreateSale
                    ? "✅ Ready"
                    : "❌ Need categories and products"}
                </span>
              </div>

              {!setupStatus.canCreateSale && (
                <Alert>
                  <AlertTriangle className="h-4 w-4" />
                  <AlertDescription>
                    To process sales, you need at least one category and one
                    product. Click "Create Sample Data" to get started quickly.
                  </AlertDescription>
                </Alert>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>📋 Setup Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-3">
          <div className="text-sm space-y-2">
            <p>
              <strong>If database tables are missing:</strong>
            </p>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              <li>Go to your Supabase Dashboard</li>
              <li>Open SQL Editor</li>
              <li>Run the setup_stock_database.sql script</li>
              <li>Come back and click "Check Setup"</li>
            </ol>
          </div>

          <div className="text-sm space-y-2">
            <p>
              <strong>If you get "ambiguous column reference" error:</strong>
            </p>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              <li>Go to your Supabase Dashboard</li>
              <li>Open SQL Editor</li>
              <li>Run the fix_sale_number_function.sql script</li>
              <li>Try processing a sale again</li>
            </ol>
          </div>

          <div className="text-sm space-y-2">
            <p>
              <strong>If you need sample data:</strong>
            </p>
            <ol className="list-decimal list-inside space-y-1 ml-4">
              <li>Click "Create Sample Data" above</li>
              <li>This will create 3 categories and 3 products</li>
              <li>You can then test the POS system</li>
            </ol>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default QuickSetup;
