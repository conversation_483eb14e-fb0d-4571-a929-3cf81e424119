import React, { useEffect } from "react";
import { Sale, SaleItem } from "@/types/stock";
import SalesTicket from "./SalesTicket";
import i18n from "@/i18n";

// Simple function to trigger browser print dialog with translations and shop info
export const printSalesTicket = (
  sale: Sale,
  saleItems: SaleItem[],
  repairShop?: any
) => {
  const t = (key: string) => i18n.t(key);
  const currentLanguage = i18n.language;
  // Create a new window for printing
  const printWindow = window.open("", "_blank");
  if (!printWindow) return;

  // Generate the ticket HTML with translations and shop info
  const ticketHTML = `
    <!DOCTYPE html>
    <html dir="${currentLanguage === "ar" ? "rtl" : "ltr"}">
      <head>
        <title>${t("stock.salesTicket")} ${sale.saleNumber}</title>
        <meta charset="UTF-8">
        <style>
          @page {
            size: 80mm auto;
            margin: 0;
          }

          body {
            margin: 0;
            padding: 8px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
            color: black;
            background: white;
            direction: ${currentLanguage === "ar" ? "rtl" : "ltr"};
          }

          .ticket {
            max-width: 80mm;
            margin: 0 auto;
          }

          .header {
            text-align: center;
            border-bottom: 2px solid black;
            padding-bottom: 8px;
            margin-bottom: 8px;
          }

          .logo {
            height: 32px;
            margin-bottom: 4px;
            max-width: 100%;
          }

          .store-name {
            font-size: 16px;
            font-weight: bold;
            margin-bottom: 2px;
          }

          .store-info {
            font-size: 10px;
            margin-bottom: 1px;
          }

          .sale-info {
            margin-bottom: 8px;
            font-size: 10px;
          }

          .sale-info-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1px;
          }

          .items-section {
            border-top: 1px solid black;
            padding-top: 4px;
            margin-bottom: 8px;
          }

          .items-title {
            font-weight: bold;
            text-align: center;
            margin-bottom: 4px;
          }

          .item {
            margin-bottom: 4px;
            font-size: 10px;
          }

          .item-name {
            display: flex;
            justify-content: space-between;
          }

          .item-details {
            display: flex;
            justify-content: space-between;
            font-size: 9px;
          }

          .totals {
            border-top: 2px solid black;
            padding-top: 4px;
            margin-bottom: 8px;
          }

          .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 1px;
            font-size: 10px;
          }

          .total-final {
            font-weight: bold;
            font-size: 12px;
            border-top: 1px solid black;
            padding-top: 2px;
            margin-top: 2px;
          }

          .payment-info {
            border-top: 1px solid black;
            padding-top: 4px;
            margin-bottom: 8px;
            font-size: 10px;
          }

          .footer {
            border-top: 2px solid black;
            padding-top: 8px;
            text-align: center;
            font-size: 10px;
          }

          .footer-message {
            font-weight: bold;
            margin-bottom: 2px;
          }
        </style>
      </head>
      <body>
        <div class="ticket">
          <!-- Header -->
          <div class="header">
            <!-- Store Logo -->
            <img src="${window.location.origin}/logo-print.png" alt="${t(
    "common.logo"
  )}" class="logo"
                 onerror="this.style.display='none'" />

            <!-- Store Name -->
            <div class="store-name">${
              repairShop?.name || t("common.repairShop")
            }</div>

            <!-- Store Address -->
            ${
              repairShop?.address
                ? `<div class="store-info">${repairShop.address}</div>`
                : ""
            }

            <!-- Store Phone -->
            ${
              repairShop?.phone
                ? `<div class="store-info">${t("common.tel")}: ${
                    repairShop.phone
                  }</div>`
                : ""
            }

          

          <!-- Sale Info -->
          <div class="sale-info">
            <div class="sale-info-row">
              <span>${t("stock.saleNumber")}:</span>
              <span>${sale.saleNumber}</span>
            </div>
            <div class="sale-info-row">
              <span>${t("stock.date")}:</span>
              <span>${new Date(sale.createdAt).toLocaleString(
                currentLanguage === "ar" ? "ar-TN" : "fr-FR",
                {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                  hour: "2-digit",
                  minute: "2-digit",
                  hour12: false,
                }
              )}</span>
            </div>
            ${
              sale.customerName
                ? `
            <div class="sale-info-row">
              <span>${t("stock.customer")}:</span>
              <span>${sale.customerName}</span>
            </div>
            `
                : ""
            }
            ${
              sale.customerPhone
                ? `
            <div class="sale-info-row">
              <span>${t("common.phone")}:</span>
              <span>${sale.customerPhone}</span>
            </div>
            `
                : ""
            }
          </div>

          <!-- Items -->
          <div class="items-section">
            <div class="items-title">${t("stock.items")}</div>
            ${saleItems
              .map(
                (item) => `
              <div class="item">
                <div class="item-name">
                  <span>${item.product?.name || "Product"}</span>
                  <span>${item.quantity}x</span>
                </div>
                <div class="item-details">
                  <span>${item.unitPrice.toFixed(3)} TND each</span>
                  <span>${item.totalPrice.toFixed(3)} TND</span>
                </div>
              </div>
            `
              )
              .join("")}
          </div>

          <!-- Totals -->
          <div class="totals">
            <div class="total-row">
              <span>${t("stock.subtotal")}:</span>
              <span>${sale.subtotal.toFixed(3)} TND</span>
            </div>
            ${
              sale.discountAmount > 0
                ? `
            <div class="total-row">
              <span>${t("stock.discount")}:</span>
              <span>-${sale.discountAmount.toFixed(3)} TND</span>
            </div>
            `
                : ""
            }
            <div class="total-row total-final">
              <span>${t("stock.total")}:</span>
              <span>${sale.totalAmount.toFixed(3)} TND</span>
            </div>
          </div>

          <!-- Payment Info -->
          <div class="payment-info">
            <div class="total-row">
              <span>${t("stock.paymentMethod")}:</span>
              <span>${t(`stock.${sale.paymentMethod}`)}</span>
            </div>
            <div class="total-row">
              <span>${t("stock.amountPaid")}:</span>
              <span>${sale.amountPaid.toFixed(3)} TND</span>
            </div>
            ${
              sale.changeAmount > 0
                ? `
            <div class="total-row">
              <span>${t("stock.change")}:</span>
              <span>${sale.changeAmount.toFixed(3)} TND</span>
            </div>
            `
                : ""
            }
          </div>

          <!-- Footer -->
          <div class="footer">
            <div class="footer-message">${t("stock.thankYouVisit")}</div>
            <div>${t("stock.keepTicket")}</div>
            <div style="margin-top: 4px;">${t("stock.saleId")}: ${sale.id
    .slice(-8)
    .toUpperCase()}</div>
          </div>
        </div>
      </body>
    </html>
  `;

  // Write the HTML to the new window
  printWindow.document.write(ticketHTML);
  printWindow.document.close();

  // Wait for content to load, then print
  printWindow.onload = () => {
    printWindow.print();
    printWindow.close();
  };
};

// Legacy component - kept for compatibility but not used
interface PrintSalesTicketProps {
  sale: Sale;
  saleItems: SaleItem[];
  autoPrint?: boolean;
}

const PrintSalesTicket: React.FC<PrintSalesTicketProps> = ({
  sale,
  saleItems,
  autoPrint = false,
}) => {
  useEffect(() => {
    if (autoPrint) {
      printSalesTicket(sale, saleItems);
    }
  }, [autoPrint, sale, saleItems]);

  return null; // This component doesn't render anything
};

export default PrintSalesTicket;
