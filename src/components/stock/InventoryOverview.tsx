import React from "react";
import { useTranslation } from "react-i18next";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertTriangle, Package, TrendingDown, TrendingUp } from "lucide-react";
import { useStock } from "@/context/StockContext";

interface InventoryOverviewProps {
  viewMode: "grid" | "list";
  touchMode: boolean;
  onViewModeChange: (mode: "grid" | "list") => void;
}

const InventoryOverview: React.FC<InventoryOverviewProps> = ({ touchMode }) => {
  const { t } = useTranslation();

  const { products, loading } = useStock();

  // Calculate real inventory statistics
  const inventoryStats = React.useMemo(() => {
    const activeProducts = products.filter((p) => p.isActive);
    const lowStockItems = activeProducts.filter(
      (p) => p.stockQuantity <= p.minStockLevel && p.stockQuantity > 0
    );
    const outOfStockItems = activeProducts.filter((p) => p.stockQuantity === 0);
    const totalValue = activeProducts.reduce(
      (sum, p) => sum + p.price * p.stockQuantity,
      0
    );

    return {
      totalProducts: activeProducts.length,
      lowStockItems: lowStockItems.length,
      outOfStockItems: outOfStockItems.length,
      totalValue,
    };
  }, [products]);

  // Get actual low stock products
  const lowStockProducts = React.useMemo(() => {
    return products
      .filter(
        (p) =>
          p.isActive &&
          p.stockQuantity <= p.minStockLevel &&
          p.stockQuantity > 0
      )
      .slice(0, 5) // Show top 5
      .map((p) => ({
        id: p.id,
        name: p.name,
        currentStock: p.stockQuantity,
        minStock: p.minStockLevel,
        value: p.price * p.stockQuantity,
      }));
  }, [products]);

  // Get actual out of stock products
  const outOfStockProducts = React.useMemo(() => {
    return products
      .filter((p) => p.isActive && p.stockQuantity === 0)
      .slice(0, 5) // Show top 5
      .map((p) => ({
        id: p.id,
        name: p.name,
        lastStock: new Date(p.updatedAt).toLocaleDateString(),
        value: 0,
      }));
  }, [products]);

  const formatCurrency = (amount: number) => {
    return `${amount.toFixed(3)} TND`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div
          className={`grid gap-4 ${
            touchMode ? "grid-cols-2" : "grid-cols-2 lg:grid-cols-4"
          }`}
        >
          {[1, 2, 3, 4].map((i) => (
            <Card key={i}>
              <CardContent
                className={`${touchMode ? "p-6" : "p-4"} text-center`}
              >
                <div className="animate-pulse">
                  <div className="h-8 w-8 bg-gray-300 rounded mx-auto mb-2"></div>
                  <div className="h-6 bg-gray-300 rounded mb-2"></div>
                  <div className="h-4 bg-gray-300 rounded"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Stats Cards */}
      <div
        className={`grid gap-4 ${
          touchMode ? "grid-cols-2" : "grid-cols-2 lg:grid-cols-4"
        }`}
      >
        <Card>
          <CardContent className={`${touchMode ? "p-6" : "p-4"} text-center`}>
            <Package className="h-8 w-8 text-blue-600 mx-auto mb-2" />
            <p className="text-2xl font-bold">{inventoryStats.totalProducts}</p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Total Products
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className={`${touchMode ? "p-6" : "p-4"} text-center`}>
            <TrendingDown className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-yellow-600">
              {inventoryStats.lowStockItems}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t("stock.lowStock")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className={`${touchMode ? "p-6" : "p-4"} text-center`}>
            <AlertTriangle className="h-8 w-8 text-red-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-red-600">
              {inventoryStats.outOfStockItems}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t("stock.outOfStock")}
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardContent className={`${touchMode ? "p-6" : "p-4"} text-center`}>
            <TrendingUp className="h-8 w-8 text-green-600 mx-auto mb-2" />
            <p className="text-2xl font-bold text-green-600">
              {formatCurrency(inventoryStats.totalValue)}
            </p>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              {t("stock.totalValue")}
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Alerts Section */}
      <div className="grid gap-6 lg:grid-cols-2">
        {/* Low Stock Alert */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-yellow-600">
              <TrendingDown className="h-5 w-5" />
              {t("stock.lowStock")} ({lowStockProducts.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {lowStockProducts.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <Package className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>{t("stock.noLowStockItems")}</p>
              </div>
            ) : (
              lowStockProducts.map((product) => (
                <div
                  key={product.id}
                  className="flex justify-between items-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg"
                >
                  <div>
                    <h4 className="font-medium">{product.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Current: {product.currentStock} | Min: {product.minStock}
                    </p>
                  </div>
                  <div className="text-right">
                    <Badge variant="secondary">{product.currentStock}</Badge>
                    <p className="text-xs text-gray-500 mt-1">
                      {formatCurrency(product.value)}
                    </p>
                  </div>
                </div>
              ))
            )}
          </CardContent>
        </Card>

        {/* Out of Stock Alert */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-red-600">
              <AlertTriangle className="h-5 w-5" />
              {t("stock.outOfStock")} ({outOfStockProducts.length})
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {outOfStockProducts.length === 0 ? (
              <div className="text-center py-8 text-gray-500">
                <AlertTriangle className="h-12 w-12 mx-auto mb-2 opacity-50" />
                <p>{t("stock.noOutOfStockItems")}</p>
              </div>
            ) : (
              outOfStockProducts.map((product) => (
                <div
                  key={product.id}
                  className="flex justify-between items-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg"
                >
                  <div>
                    <h4 className="font-medium">{product.name}</h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Last in stock: {product.lastStock}
                    </p>
                  </div>
                  <Badge variant="destructive">0</Badge>
                </div>
              ))
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default InventoryOverview;
