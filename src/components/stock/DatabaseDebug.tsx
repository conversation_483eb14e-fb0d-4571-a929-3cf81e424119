import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { supabase } from '@/integrations/supabase/client';
import { useAuth } from '@/context/AuthContext';
import { useRepairShopContext } from '@/context/RepairShopContext';

const DatabaseDebug: React.FC = () => {
  const { user } = useAuth();
  const { repairShop } = useRepairShopContext();
  const [results, setResults] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const testDatabaseConnection = async () => {
    setLoading(true);
    setResults([]);
    
    try {
      // Test 1: Check if we can connect to Supabase
      console.log("🔍 Testing database connection...");
      setResults(prev => [...prev, { test: "Database Connection", status: "✅ Connected", data: "Supabase client initialized" }]);

      // Test 2: Check user and repair shop
      console.log("👤 User:", user);
      console.log("🏪 Repair Shop:", repairShop);
      setResults(prev => [...prev, { 
        test: "User & Repair Shop", 
        status: user && repairShop ? "✅ Available" : "❌ Missing", 
        data: { userId: user?.id, repairShopId: repairShop?.id }
      }]);

      if (!user || !repairShop) {
        setResults(prev => [...prev, { test: "Error", status: "❌ Cannot proceed", data: "User or repair shop missing" }]);
        return;
      }

      // Test 3: Check if product_categories table exists and we can query it
      try {
        const { data: categories, error: categoriesError } = await supabase
          .from('product_categories')
          .select('*')
          .eq('repair_shop_id', repairShop.id)
          .limit(5);

        if (categoriesError) {
          setResults(prev => [...prev, { 
            test: "Categories Table", 
            status: "❌ Error", 
            data: categoriesError 
          }]);
        } else {
          setResults(prev => [...prev, { 
            test: "Categories Table", 
            status: "✅ Accessible", 
            data: `Found ${categories.length} categories`
          }]);
        }
      } catch (error) {
        setResults(prev => [...prev, { 
          test: "Categories Table", 
          status: "❌ Exception", 
          data: error 
        }]);
      }

      // Test 4: Check if products table exists and we can query it
      try {
        const { data: products, error: productsError } = await supabase
          .from('products')
          .select('*')
          .eq('repair_shop_id', repairShop.id)
          .limit(5);

        if (productsError) {
          setResults(prev => [...prev, { 
            test: "Products Table", 
            status: "❌ Error", 
            data: productsError 
          }]);
        } else {
          setResults(prev => [...prev, { 
            test: "Products Table", 
            status: "✅ Accessible", 
            data: `Found ${products.length} products`
          }]);
        }
      } catch (error) {
        setResults(prev => [...prev, { 
          test: "Products Table", 
          status: "❌ Exception", 
          data: error 
        }]);
      }

      // Test 5: Check if sales table exists and we can query it
      try {
        const { data: sales, error: salesError } = await supabase
          .from('sales')
          .select('*')
          .eq('repair_shop_id', repairShop.id)
          .limit(5);

        if (salesError) {
          setResults(prev => [...prev, { 
            test: "Sales Table", 
            status: "❌ Error", 
            data: salesError 
          }]);
        } else {
          setResults(prev => [...prev, { 
            test: "Sales Table", 
            status: "✅ Accessible", 
            data: `Found ${sales.length} sales`
          }]);
        }
      } catch (error) {
        setResults(prev => [...prev, { 
          test: "Sales Table", 
          status: "❌ Exception", 
          data: error 
        }]);
      }

      // Test 6: Check if sale_items table exists
      try {
        const { data: saleItems, error: saleItemsError } = await supabase
          .from('sale_items')
          .select('*')
          .limit(5);

        if (saleItemsError) {
          setResults(prev => [...prev, { 
            test: "Sale Items Table", 
            status: "❌ Error", 
            data: saleItemsError 
          }]);
        } else {
          setResults(prev => [...prev, { 
            test: "Sale Items Table", 
            status: "✅ Accessible", 
            data: `Found ${saleItems.length} sale items`
          }]);
        }
      } catch (error) {
        setResults(prev => [...prev, { 
          test: "Sale Items Table", 
          status: "❌ Exception", 
          data: error 
        }]);
      }

      // Test 7: Test a simple insert to see if we have write permissions
      try {
        const testCategory = {
          name: 'Test Category',
          description: 'Test category for debugging',
          repair_shop_id: repairShop.id
        };

        const { data: insertResult, error: insertError } = await supabase
          .from('product_categories')
          .insert(testCategory)
          .select()
          .single();

        if (insertError) {
          setResults(prev => [...prev, { 
            test: "Write Permissions", 
            status: "❌ Error", 
            data: insertError 
          }]);
        } else {
          setResults(prev => [...prev, { 
            test: "Write Permissions", 
            status: "✅ Can Write", 
            data: "Successfully inserted test category"
          }]);

          // Clean up - delete the test category
          await supabase
            .from('product_categories')
            .delete()
            .eq('id', insertResult.id);
        }
      } catch (error) {
        setResults(prev => [...prev, { 
          test: "Write Permissions", 
          status: "❌ Exception", 
          data: error 
        }]);
      }

    } catch (error) {
      console.error("Database test error:", error);
      setResults(prev => [...prev, { test: "General Error", status: "❌ Failed", data: error }]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle>🔍 Database Debug Tool</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={testDatabaseConnection} disabled={loading}>
          {loading ? "Testing..." : "Test Database Connection"}
        </Button>

        {results.length > 0 && (
          <div className="space-y-2">
            <h3 className="font-semibold">Test Results:</h3>
            {results.map((result, index) => (
              <div key={index} className="p-3 border rounded-lg">
                <div className="flex justify-between items-start">
                  <span className="font-medium">{result.test}</span>
                  <span className="text-sm">{result.status}</span>
                </div>
                <pre className="text-xs mt-2 bg-gray-100 dark:bg-gray-800 p-2 rounded overflow-auto">
                  {typeof result.data === 'object' ? JSON.stringify(result.data, null, 2) : result.data}
                </pre>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DatabaseDebug;
