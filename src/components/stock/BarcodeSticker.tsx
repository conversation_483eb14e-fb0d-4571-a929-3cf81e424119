import { useEffect, useState, forwardRef } from "react";
import { Product } from "@/types/stock";
import { generateBarcodeDataURL } from "@/utils/barcodeUtils";

interface BarcodeStickerProps {
  product: Product;
  barcode?: string;
  className?: string;
  onPrintReady?: (ready: boolean) => void;
}

/**
 * BarcodeSticker component for generating product barcode stickers
 * Optimized for 40x20mm paper in landscape orientation
 */
const BarcodeSticker = forwardRef<HTMLDivElement, BarcodeStickerProps>(
  ({ product, barcode, className = "", onPrintReady }, ref) => {
    const [barcodeDataURL, setBarcodeDataURL] = useState<string>("");
    const [currentBarcode, setCurrentBarcode] = useState<string>("");
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string>("");

    useEffect(() => {
      const generateBarcode = async () => {
        try {
          setLoading(true);
          setError("");

          // Determine which barcode to use
          const barcodeToUse = barcode || product.barcode;

          if (!barcodeToUse) {
            setError("No barcode available");
            setLoading(false);
            return;
          }

          // Set the current barcode
          setCurrentBarcode(barcodeToUse);

          // Generate barcode image optimized for portrait 40x20mm sticker
          const dataURL = await generateBarcodeDataURL(barcodeToUse, {
            format: "EAN13", // Try EAN13 first
            width: 1.0, // Standard width
            height: 20, // Taller height for portrait
            displayValue: false, // Don't show barcode number to save space
            fontSize: 0, // No font for barcode itself
            textMargin: 0, // No text margin
            margin: 1, // Minimal margin
            background: "#ffffff",
            lineColor: "#000000",
          });

          setBarcodeDataURL(dataURL);
        } catch (err) {
          console.error("Error generating barcode:", err);

          // If EAN13 fails, try CODE128 which is more flexible
          try {
            const barcodeToUse = barcode || product.barcode;
            if (barcodeToUse) {
              const dataURL = await generateBarcodeDataURL(barcodeToUse, {
                format: "CODE128",
                width: 1.0, // Standard width
                height: 20, // Taller height for portrait
                displayValue: false, // Don't show barcode number
                fontSize: 0, // No font for barcode itself
                textMargin: 0, // No text margin
                margin: 1, // Minimal margin
                background: "#ffffff",
                lineColor: "#000000",
              });
              setBarcodeDataURL(dataURL);
            }
          } catch (fallbackErr) {
            console.error("Error generating fallback barcode:", fallbackErr);
            setError("Failed to generate barcode");
          }
        } finally {
          setLoading(false);
          onPrintReady?.(!error && !!barcodeDataURL);
        }
      };

      // Only regenerate if barcode value actually changes
      if (barcode !== currentBarcode || product.barcode !== currentBarcode) {
        generateBarcode();
      }
    }, [product.barcode, barcode, currentBarcode]);

    if (loading) {
      return (
        <div
          ref={ref}
          className={`bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 p-4 rounded-lg ${className}`}
        >
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
            <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div
          ref={ref}
          className={`bg-white dark:bg-gray-800 border border-red-300 dark:border-red-600 p-4 rounded-lg ${className}`}
        >
          <div className="text-red-600 dark:text-red-400 text-center">
            <p className="text-sm">{error}</p>
          </div>
        </div>
      );
    }

    // Truncate product name to fit nicely in portrait
    const truncatedName =
      product.name.length > 15
        ? product.name.substring(0, 15) + "..."
        : product.name;

    return (
      <div
        ref={ref}
        className={`barcode-sticker bg-white border border-gray-300 print:border-0 print:rounded-none print:shadow-none print:bg-white ${className}`}
        style={{
          // Fixed dimensions for 40x20mm in landscape mode (40mm wide, 20mm tall)
          width: "40mm",
          height: "20mm",
          padding: "0.5mm",
          display: "flex",
          flexDirection: "column", // Column layout within landscape orientation
          justifyContent: "space-between",
          alignItems: "center", // Center content horizontally
          fontFamily: "Arial, sans-serif",
          boxSizing: "border-box",
          overflow: "hidden",
          // Ensure proper print sizing and page breaks
          pageBreakInside: "avoid",
          breakInside: "avoid",
          margin: "0 auto", // Center the sticker itself
          transform: "scale(1)", // Ensure no scaling issues
        }}
      >
        {/* Top section: Product name */}
        <div style={{ marginBottom: "1mm" }}>
          <h3
            className="font-bold text-black leading-tight"
            style={{
              fontSize: "10px",
              fontWeight: "bold",
              margin: "0",
              padding: "0",
              lineHeight: "1.1",
              wordBreak: "break-word",
              textAlign: "center",
            }}
          >
            {truncatedName}
          </h3>
        </div>

        {/* Middle section: Barcode */}
        <div
          style={{
            flex: "1 1 auto", // Take remaining space
            display: "flex",
            flexDirection: "column",
            justifyContent: "center",
            alignItems: "center",
          }}
        >
          {/* Barcode */}
          <div
            style={{
              display: "flex",
              justifyContent: "center",
              alignItems: "center",
              marginBottom: "0.5mm",
            }}
          >
            {barcodeDataURL ? (
              <img
                src={barcodeDataURL}
                alt={`Barcode: ${currentBarcode}`}
                style={{
                  maxHeight: "12mm", // Increased height for better visibility
                  width: "auto",
                  maxWidth: "38mm", // Use more of the available width
                  display: "block",
                }}
              />
            ) : (
              <div
                className="bg-gray-200 rounded flex items-center justify-center"
                style={{
                  height: "15mm",
                  width: "40mm",
                }}
              >
                <span className="text-gray-500" style={{ fontSize: "6px" }}>
                  No barcode
                </span>
              </div>
            )}
          </div>

          {/* Bottom section: Price */}
          <div>
            <div
              className="font-bold text-green-700"
              style={{
                fontSize: "12px",
                fontWeight: "bold",
                margin: "0",
                padding: "0",
                lineHeight: "1.1",
                textAlign: "center",
              }}
            >
              {product.price.toFixed(3)} TND
            </div>
          </div>

          {/* Barcode number (very small) */}
          {currentBarcode && (
            <div
              className="text-gray-600"
              style={{
                fontSize: "6px",
                margin: "0",
                padding: "0",
                lineHeight: "1",
                textAlign: "center",
              }}
            >
              {currentBarcode}
            </div>
          )}
        </div>
      </div>
    );
  }
);

BarcodeSticker.displayName = "BarcodeSticker";

export default BarcodeSticker;
