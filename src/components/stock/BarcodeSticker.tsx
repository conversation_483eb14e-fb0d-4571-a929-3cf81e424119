import { useEffect, useState, forwardRef } from "react";
import { Product } from "@/types/stock";
import { generateBarcodeDataURL } from "@/utils/barcodeUtils";

interface BarcodeStickerProps {
  product: Product;
  barcode?: string;
  className?: string;
}

/**
 * BarcodeSticker component for generating printable product stickers
 * Contains product name, EAN13 barcode, and price in a standard sticker format
 */
const BarcodeSticker = forwardRef<HTMLDivElement, BarcodeStickerProps>(
  ({ product, barcode, className = "" }, ref) => {
    const [barcodeDataURL, setBarcodeDataURL] = useState<string>("");
    const [currentBarcode, setCurrentBarcode] = useState<string>("");
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string>("");

    useEffect(() => {
      const generateBarcode = async () => {
        try {
          setLoading(true);
          setError("");

          // Determine which barcode to use
          const barcodeToUse = barcode || product.barcode;

          if (!barcodeToUse) {
            setError("No barcode available");
            setLoading(false);
            return;
          }

          // Set the current barcode
          setCurrentBarcode(barcodeToUse);

          // Generate barcode image optimized for 40x20mm sticker
          const dataURL = await generateBarcodeDataURL(barcodeToUse, {
            format: "EAN13", // Try EAN13 first
            width: 1.5, // Thinner bars for small sticker
            height: 35, // Shorter height for 20mm paper
            displayValue: true,
            fontSize: 8, // Smaller font for compact size
            textMargin: 2, // Minimal text margin
            margin: 2, // Minimal margin for small sticker
            background: "#ffffff",
            lineColor: "#000000",
          });

          setBarcodeDataURL(dataURL);
        } catch (err) {
          console.error("Error generating barcode:", err);

          // If EAN13 fails, try CODE128 which is more flexible
          try {
            const barcodeToUse = barcode || product.barcode;
            if (barcodeToUse) {
              const dataURL = await generateBarcodeDataURL(barcodeToUse, {
                format: "CODE128",
                width: 1.5, // Thinner bars for small sticker
                height: 35, // Shorter height for 20mm paper
                displayValue: true,
                fontSize: 8, // Smaller font for compact size
                textMargin: 2, // Minimal text margin
                margin: 2, // Minimal margin for small sticker
                background: "#ffffff",
                lineColor: "#000000",
              });
              setBarcodeDataURL(dataURL);
            }
          } catch (fallbackErr) {
            console.error("Error generating fallback barcode:", fallbackErr);
            setError("Failed to generate barcode");
          }
        } finally {
          setLoading(false);
        }
      };

      generateBarcode();
    }, [product.id, product.barcode, barcode]);

    if (loading) {
      return (
        <div
          ref={ref}
          className={`bg-white border border-gray-300 p-4 rounded-lg ${className}`}
        >
          <div className="animate-pulse">
            <div className="h-4 bg-gray-200 rounded mb-2"></div>
            <div className="h-16 bg-gray-200 rounded mb-2"></div>
            <div className="h-4 bg-gray-200 rounded"></div>
          </div>
        </div>
      );
    }

    if (error) {
      return (
        <div
          ref={ref}
          className={`bg-white border border-red-300 p-4 rounded-lg ${className}`}
        >
          <div className="text-red-600 text-center">
            <p className="text-sm">{error}</p>
          </div>
        </div>
      );
    }

    return (
      <div
        ref={ref}
        className={`bg-white border border-gray-300 p-1 rounded-lg print:border-0 print:rounded-none print:shadow-none ${className}`}
        style={{
          width: "40mm", // 40mm width for small sticker
          height: "20mm", // 20mm height for small sticker
          fontSize: "6px", // Very small font for compact sticker
          lineHeight: "1.1",
          padding: "1mm", // Minimal padding
        }}
      >
        {/* Product Name - Compact for 40x20mm */}
        <div className="text-center mb-1">
          <h3
            className="font-bold text-black leading-tight"
            style={{
              fontSize: "7px", // Very small font for 40x20mm
              maxHeight: "8px", // Limited height for small sticker
              overflow: "hidden",
              display: "-webkit-box",
              WebkitLineClamp: 1, // Only 1 line for compact sticker
              WebkitBoxOrient: "vertical",
            }}
          >
            {product.name.length > 25
              ? product.name.substring(0, 25) + "..."
              : product.name}
          </h3>
        </div>

        {/* Barcode - Compact for 40x20mm */}
        <div className="flex justify-center mb-1">
          {barcodeDataURL ? (
            <img
              src={barcodeDataURL}
              alt={`Barcode: ${currentBarcode}`}
              className="max-w-full h-auto"
              style={{
                maxHeight: "8mm", // Compact height for 20mm sticker
                width: "auto",
                maxWidth: "36mm", // Leave 2mm margin on each side
              }}
            />
          ) : (
            <div className="h-6 bg-gray-200 rounded flex items-center justify-center text-xs">
              <span className="text-gray-500" style={{ fontSize: "5px" }}>
                No barcode
              </span>
            </div>
          )}
        </div>

        {/* Price - Compact layout */}
        <div className="text-center">
          <div className="font-bold text-green-600" style={{ fontSize: "6px" }}>
            {product.price.toFixed(3)} TND
          </div>
        </div>
      </div>
    );
  }
);

BarcodeSticker.displayName = "BarcodeSticker";

export default BarcodeSticker;
