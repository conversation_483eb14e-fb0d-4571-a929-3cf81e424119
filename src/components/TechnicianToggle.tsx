import React from "react";
import { useRepairContext } from "@/context/RepairContext";
import { useAuth } from "@/context/AuthContext";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { useTranslation } from "react-i18next";

const TechnicianToggle: React.FC = () => {
  const { showAllTickets, setShowAllTickets } = useRepairContext();
  const { hasPermission } = useAuth();
  const { t } = useTranslation();

  // Show toggle for all users (will be filtered by role in context)
  // Remove permission check for now

  return (
    <div className="flex items-center space-x-2">
      <Label htmlFor="show-all-tickets" className="text-sm">
        {t("common.all")}
      </Label>
      <Switch
        id="show-all-tickets"
        checked={showAllTickets}
        onCheckedChange={setShowAllTickets}
      />
    </div>
  );
};

export default TechnicianToggle;