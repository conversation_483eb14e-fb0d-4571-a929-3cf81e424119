import React, { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { useRepairShopContext } from "@/context/RepairShopContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import {
  Clock,
  BarChart3,
  Download,
  TrendingUp,
  Target,
  Lightbulb,
  Users,
  AlertTriangle,
} from "lucide-react";

export default function WorkflowIntelligence() {
  const { user, hasPermission } = useAuth();
  const { repairShop } = useRepairShopContext();
  const { t } = useTranslation();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("overview");
  const [timeEntries, setTimeEntries] = useState<any[]>([]);
  const [technicians, setTechnicians] = useState<any[]>([]);
  const [pendingRepairs, setPendingRepairs] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [dateFilter, setDateFilter] = useState("today");
  const [exportLoading, setExportLoading] = useState(false);

  const canTrackTime = hasPermission("track_time");
  const canViewReports = hasPermission("view_reports");

  useEffect(() => {
    if (!canTrackTime && !canViewReports) return;
    if (!repairShop) return;

    const loadData = async () => {
      await fetchTechnicians();
      await fetchTimeEntries();
      await fetchPendingRepairs();
    };

    loadData();
  }, [canTrackTime, canViewReports, repairShop]);

  const fetchTimeEntries = async () => {
    if (!repairShop) return;

    try {
      const { data, error } = await supabase.rpc(
        "get_technician_time_tracking",
        {
          shop_id: repairShop.id,
        }
      );

      if (!error && data) {
        setTimeEntries(data);
      }
    } catch (error) {
      console.error("Error fetching time entries:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTechnicians = async () => {
    if (!repairShop) return;

    try {
      const { data, error } = await supabase.rpc("get_shop_users", {
        shop_id: repairShop.id,
      });

      if (!error && data) {
        const techs = data.filter(
          (u) => u.role === "technician" || u.role === "administrator"
        );
        setTechnicians(techs);
      }
    } catch (error) {
      console.error("Error fetching technicians:", error);
    }
  };

  const fetchPendingRepairs = async () => {
    if (!repairShop) return;

    try {
      const { data, error } = await supabase
        .from("repairs")
        .select("id, created_at, customer_name, phone_model, ticket_number")
        .eq("repair_shop_id", repairShop.id)
        .eq("status", "pending");

      if (!error && data) {
        setPendingRepairs(data);
      }
    } catch (error) {
      console.error("Error fetching pending repairs:", error);
    }
  };

  const getFilteredEntries = () => {
    let filtered = timeEntries;

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    switch (dateFilter) {
      case "today":
        filtered = filtered.filter(
          (entry) => new Date(entry.started_at) >= today
        );
        break;
      case "yesterday":
        filtered = filtered.filter((entry) => {
          const entryDate = new Date(entry.started_at);
          return entryDate >= yesterday && entryDate < today;
        });
        break;
      case "week":
        filtered = filtered.filter(
          (entry) => new Date(entry.started_at) >= weekAgo
        );
        break;
      case "month":
        filtered = filtered.filter(
          (entry) => new Date(entry.started_at) >= monthAgo
        );
        break;
    }

    return filtered;
  };

  const exportToPDF = async () => {
    setExportLoading(true);
    try {
      const filteredData = getFilteredEntries();
      const totalMinutes = filteredData.reduce(
        (sum, entry) => sum + (entry.duration_minutes || 0),
        0
      );
      const avgMinutes = filteredData.length > 0 ? totalMinutes / filteredData.length : 0;
      const completedEntries = filteredData.filter(
        (entry) => entry.status_to === "completed"
      );
      const inProgressEntries = filteredData.filter(
        (entry) => entry.status_to === "in_progress"
      );
      const otherEntries = filteredData.length - completedEntries.length - inProgressEntries.length;
      
      const currentDate = new Date();
      const reportData = {
        totalHours: Math.round((totalMinutes / 60) * 100) / 100,
        totalEntries: filteredData.length,
        avgHours: Math.round((avgMinutes / 60) * 100) / 100,
        completedRepairs: completedEntries.length,
        successRate: Math.round((completedEntries.length / filteredData.length) * 100) || 0,
      };
      
      const pdfContent = `<!DOCTYPE html>
        <html>
          <head>
            <meta charset="UTF-8">
            <title>Workflow Intelligence Report</title>
            <style>
              body { font-family: Arial, sans-serif; margin: 20px; }
              h1 { color: #333; text-align: center; margin-bottom: 10px; }
              .header { text-align: center; margin-bottom: 30px; }
              .metrics { display: flex; justify-content: space-around; margin: 20px 0; }
              .metric { text-align: center; padding: 10px; }
              .metric-value { font-size: 24px; font-weight: bold; color: #2563eb; }
              .metric-label { font-size: 12px; color: #666; margin-top: 5px; }
              table { width: 100%; border-collapse: collapse; margin: 20px 0; }
              th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
              th { background-color: #f2f2f2; font-weight: bold; }
              tr:nth-child(even) { background-color: #f9f9f9; }
              h2 { color: #333; margin-top: 30px; }
            </style>
          </head>
          <body>
            <div class="header">
              <h1>Workflow Intelligence Report</h1>
              <p>Period: ${dateFilter.toUpperCase()} | Shop: ${repairShop?.name || 'N/A'}</p>
              <p>Generated: ${currentDate.toLocaleDateString()} ${currentDate.toLocaleTimeString()}</p>
            </div>
            
            <div class="metrics">
              <div class="metric">
                <div class="metric-value">${reportData.totalHours}h</div>
                <div class="metric-label">Total Hours</div>
              </div>
              <div class="metric">
                <div class="metric-value">${reportData.totalEntries}</div>
                <div class="metric-label">Total Processes</div>
              </div>
              <div class="metric">
                <div class="metric-value">${reportData.avgHours}h</div>
                <div class="metric-label">Avg Hours/Process</div>
              </div>
              <div class="metric">
                <div class="metric-value">${reportData.completedRepairs}</div>
                <div class="metric-label">Completed</div>
              </div>
            </div>
            
            <h2>Activity Details</h2>
            <table>
              <thead>
                <tr>
                  <th>Ticket Number</th>
                  <th>Status</th>
                  <th>Duration</th>
                  <th>Started</th>
                  <th>Completed</th>
                </tr>
              </thead>
              <tbody>
                ${filteredData.slice(0, 15).map(entry => `
                  <tr>
                    <td>#${entry.ticket_number || 'N/A'}</td>
                    <td>${entry.status_to || 'Unknown'}</td>
                    <td>${Math.floor((entry.duration_minutes || 0) / 60)}h ${Math.round((entry.duration_minutes || 0) % 60)}m</td>
                    <td>${entry.started_at ? new Date(entry.started_at).toLocaleDateString() : 'N/A'}</td>
                    <td>${entry.ended_at ? new Date(entry.ended_at).toLocaleDateString() : 'In Progress'}</td>
                  </tr>
                `).join('')}
              </tbody>
            </table>
            
            <h2>Summary Statistics</h2>
            <table>
              <thead>
                <tr>
                  <th>Metric</th>
                  <th>Value</th>
                  <th>Percentage</th>
                </tr>
              </thead>
              <tbody>
                <tr>
                  <td>Completed Repairs</td>
                  <td>${completedEntries.length}</td>
                  <td>${Math.round((completedEntries.length / filteredData.length) * 100) || 0}%</td>
                </tr>
                <tr>
                  <td>In Progress</td>
                  <td>${inProgressEntries.length}</td>
                  <td>${Math.round((inProgressEntries.length / filteredData.length) * 100) || 0}%</td>
                </tr>
                <tr>
                  <td>Other Status</td>
                  <td>${otherEntries}</td>
                  <td>${Math.round((otherEntries / filteredData.length) * 100) || 0}%</td>
                </tr>
              </tbody>
            </table>
          </body>
        </html>
      `;
      
      const printWindow = window.open('', '_blank');
      if (printWindow) {
        printWindow.document.open();
        printWindow.document.write(pdfContent);
        printWindow.document.close();
        
        setTimeout(() => {
          printWindow.print();
        }, 500);
      }
      
      toast.success('Report generated successfully');
    } catch (error) {
      console.error('Error generating report:', error);
      toast.error('Failed to generate report');
    } finally {
      setExportLoading(false);
    }
  };

  if (!canTrackTime && !canViewReports) {
    return (
      <div className="flex items-center justify-center h-screen">
        {t("workflowIntelligence.accessDenied")}
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const filteredEntries = getFilteredEntries();
  const totalMinutes = filteredEntries.reduce(
    (sum, entry) => sum + (entry.duration_minutes || 0),
    0
  );
  const avgMinutes =
    filteredEntries.length > 0 ? totalMinutes / filteredEntries.length : 0;
  const completedEntries = filteredEntries.filter(
    (entry) => entry.status_to === "completed"
  );

  // Calculate overdue pending repairs (more than 3 days)
  const now = new Date();
  const overduePendingRepairs = pendingRepairs.filter((repair) => {
    const daysSinceCreated = Math.floor(
      (now.getTime() - new Date(repair.created_at).getTime()) /
        (1000 * 60 * 60 * 24)
    );
    return daysSinceCreated > 3;
  });

  const handleOverduePendingClick = () => {
    navigate("/search?status=pending");
  };

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold flex items-center">
            <TrendingUp className="h-8 w-8 mr-3 text-blue-500" />
            {t("performanceMonitor.title")}
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            {t("performanceMonitor.subtitle")}
          </p>
        </div>
        <Button
          onClick={exportToPDF}
          disabled={exportLoading}
          className="flex items-center gap-2"
        >
          {exportLoading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          ) : (
            <Download className="h-4 w-4" />
          )}
          {t("performanceMonitor.exportReport")}
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">
            {t("performanceMonitor.processOverview")}
          </TabsTrigger>
          <TabsTrigger value="analytics">
            {t("performanceMonitor.businessInsights")}
          </TabsTrigger>
          <TabsTrigger value="improvements">
            {t("performanceMonitor.improvementOpportunities")}
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="flex flex-wrap gap-4 mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2">
              <Label className="text-blue-700 dark:text-blue-300">
                {t("performanceMonitor.analysisPeriod")}:
              </Label>
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">
                    {t("performanceMonitor.today")}
                  </SelectItem>
                  <SelectItem value="yesterday">
                    {t("performanceMonitor.yesterday")}
                  </SelectItem>
                  <SelectItem value="week">
                    {t("performanceMonitor.thisWeek")}
                  </SelectItem>
                  <SelectItem value="month">
                    {t("performanceMonitor.thisMonth")}
                  </SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400">
              <Lightbulb className="h-4 w-4" />
              <span>{t("performanceMonitor.analyzingWorkflow")}</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-5 gap-6 mb-6">
            <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <Clock className="h-4 w-4 mr-2" />
                  {t("workflowIntelligence.workshopCapacity")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.floor(totalMinutes / 60)}h{" "}
                  {Math.round(totalMinutes % 60)}m
                </div>
                <p className="text-xs opacity-80">
                  {t("workflowIntelligence.totalProductiveTime")}
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <Target className="h-4 w-4 mr-2" />
                  {t("workflowIntelligence.processCompletion")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {completedEntries.length}
                </div>
                <p className="text-xs opacity-80">
                  {t("workflowIntelligence.successfulOutcomes")}
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <Users className="h-4 w-4 mr-2" />
                  {t("workflowIntelligence.activeWorkflows")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {filteredEntries.filter((entry) => !entry.ended_at).length}
                </div>
                <p className="text-xs opacity-80">
                  {t("workflowIntelligence.currentlyInProgress")}
                </p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  {t("workflowIntelligence.processEfficiency")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.floor(avgMinutes / 60)}h {Math.round(avgMinutes % 60)}m
                </div>
                <p className="text-xs opacity-80">
                  {t("workflowIntelligence.averageCycleTime")}
                </p>
              </CardContent>
            </Card>

            <Card
              className={`bg-gradient-to-r ${
                overduePendingRepairs.length > 0
                  ? "from-red-500 to-red-600"
                  : "from-gray-500 to-gray-600"
              } text-white cursor-pointer hover:opacity-90 transition-opacity`}
              onClick={handleOverduePendingClick}
            >
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  {t("workflowIntelligence.overduePending")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {overduePendingRepairs.length}
                </div>
                <p className="text-xs opacity-80">
                  {t("workflowIntelligence.pendingOverThreeDays")}
                </p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                {t("workflowIntelligence.businessPerformanceMetrics")}
              </CardTitle>
              <CardDescription>
                {t("workflowIntelligence.operationalExcellence")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded bg-blue-50 dark:bg-blue-900/20">
                  <div className="text-2xl font-bold text-blue-600">
                    {Math.round((totalMinutes / filteredEntries.length) * 10) /
                      10 || 0}
                    min
                  </div>
                  <div className="text-sm text-gray-600">
                    {t("workflowIntelligence.averageServiceTime")}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {t("workflowIntelligence.industryBenchmark")}
                  </div>
                </div>
                <div className="text-center p-4 border rounded bg-green-50 dark:bg-green-900/20">
                  <div className="text-2xl font-bold text-green-600">
                    {Math.round(
                      (completedEntries.length / filteredEntries.length) * 100
                    ) || 0}
                    %
                  </div>
                  <div className="text-sm text-gray-600">
                    {t("workflowIntelligence.processSuccessRate")}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {t("workflowIntelligence.targetRate")}
                  </div>
                </div>
                <div className="text-center p-4 border rounded bg-purple-50 dark:bg-purple-900/20">
                  <div className="text-2xl font-bold text-purple-600">
                    {Math.round(
                      (completedEntries.length / (totalMinutes / 60)) * 10
                    ) / 10 || 0}
                  </div>
                  <div className="text-sm text-gray-600">
                    {t("workflowIntelligence.throughput")}
                  </div>
                  <div className="text-xs text-gray-500 mt-1">
                    {t("workflowIntelligence.workshopCapacityIndicator")}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="improvements">
          <div className="space-y-6">
            {/* Key Metrics Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <BarChart3 className="h-5 w-5 mr-2 text-blue-500" />
                  {t("workflowIntelligence.performanceSummary")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {Math.round((completedEntries.length / filteredEntries.length) * 100) || 0}%
                    </div>
                    <div className="text-sm text-gray-600">{t("workflowIntelligence.successRate")}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">
                      {Math.round((totalMinutes / filteredEntries.length) * 10) / 10 || 0}min
                    </div>
                    <div className="text-sm text-gray-600">{t("workflowIntelligence.avgServiceTime")}</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-purple-600">
                      {Math.round((completedEntries.length / (totalMinutes / 60)) * 10) / 10 || 0}
                    </div>
                    <div className="text-sm text-gray-600">{t("workflowIntelligence.repairsPerHour")}</div>
                  </div>
                  <div className="text-2xl font-bold text-red-600 text-center">
                    {overduePendingRepairs.length}
                    <div className="text-sm text-gray-600">{t("workflowIntelligence.overdue")}</div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Dynamic Insights */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-green-700">
                    <Target className="h-5 w-5 mr-2" />
                    {t("workflowIntelligence.strengthsIdentified")}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Dynamic success rate insight */}
                    {((completedEntries.length / filteredEntries.length) * 100) > 85 && (
                      <div className="flex items-start gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                        <div>
                          <div className="font-medium text-sm">{t("workflowIntelligence.excellentSuccessRate")}</div>
                          <div className="text-xs text-gray-600">{t("workflowIntelligence.successRateExceeds", { rate: Math.round((completedEntries.length / filteredEntries.length) * 100) })}</div>
                        </div>
                      </div>
                    )}
                    
                    {/* Dynamic service time insight */}
                    {(totalMinutes / filteredEntries.length) < 90 && filteredEntries.length > 0 && (
                      <div className="flex items-start gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                        <div>
                          <div className="font-medium text-sm">{t("workflowIntelligence.efficientServiceTimes")}</div>
                          <div className="text-xs text-gray-600">{t("workflowIntelligence.avgTimeOptimal", { time: Math.round((totalMinutes / filteredEntries.length) * 10) / 10 })}</div>
                        </div>
                      </div>
                    )}
                    
                    {/* Dynamic throughput insight */}
                    {(completedEntries.length / (totalMinutes / 60)) > 1 && (
                      <div className="flex items-start gap-3 p-3 bg-green-50 dark:bg-green-900/20 rounded">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-2"></div>
                        <div>
                          <div className="font-medium text-sm">{t("workflowIntelligence.highProductivity")}</div>
                          <div className="text-xs text-gray-600">{t("workflowIntelligence.completingRepairsPerHour", { rate: Math.round((completedEntries.length / (totalMinutes / 60)) * 10) / 10 })}</div>
                        </div>
                      </div>
                    )}
                    
                    {/* Fallback if no strengths */}
                    {((completedEntries.length / filteredEntries.length) * 100) <= 85 && 
                     (totalMinutes / filteredEntries.length) >= 90 && 
                     (completedEntries.length / (totalMinutes / 60)) <= 1 && (
                      <div className="flex items-start gap-3 p-3 bg-gray-50 dark:bg-gray-800 rounded">
                        <div className="w-2 h-2 bg-gray-400 rounded-full mt-2"></div>
                        <div>
                          <div className="font-medium text-sm">{t("workflowIntelligence.buildingFoundation")}</div>
                          <div className="text-xs text-gray-600">{t("workflowIntelligence.focusOnOptimization")}</div>
                        </div>
                      </div>
                    )}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center text-blue-700">
                    <Lightbulb className="h-5 w-5 mr-2" />
                    {t("workflowIntelligence.optimizationOpportunities")}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {/* Dynamic overdue insight */}
                    {overduePendingRepairs.length > 0 && (
                      <div className="flex items-start gap-3 p-3 bg-red-50 dark:bg-red-900/20 rounded">
                        <div className="w-2 h-2 bg-red-500 rounded-full mt-2"></div>
                        <div>
                          <div className="font-medium text-sm">{t("workflowIntelligence.addressOverdueRepairs")}</div>
                          <div className="text-xs text-gray-600">{t("workflowIntelligence.repairsPendingAttention", { count: overduePendingRepairs.length })}</div>
                        </div>
                      </div>
                    )}
                    
                    {/* Dynamic success rate improvement */}
                    {((completedEntries.length / filteredEntries.length) * 100) < 85 && filteredEntries.length > 0 && (
                      <div className="flex items-start gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        <div>
                          <div className="font-medium text-sm">{t("workflowIntelligence.improveCompletionRate")}</div>
                          <div className="text-xs text-gray-600">{t("workflowIntelligence.currentRateCanImprove", { rate: Math.round((completedEntries.length / filteredEntries.length) * 100) })}</div>
                        </div>
                      </div>
                    )}
                    
                    {/* Dynamic service time improvement */}
                    {(totalMinutes / filteredEntries.length) > 90 && filteredEntries.length > 0 && (
                      <div className="flex items-start gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        <div>
                          <div className="font-medium text-sm">{t("workflowIntelligence.optimizeServiceTime")}</div>
                          <div className="text-xs text-gray-600">{t("workflowIntelligence.avgTimeExceedsBenchmark", { time: Math.round((totalMinutes / filteredEntries.length) * 10) / 10 })}</div>
                        </div>
                      </div>
                    )}
                    
                    {/* Dynamic throughput improvement */}
                    {(completedEntries.length / (totalMinutes / 60)) < 1 && totalMinutes > 0 && (
                      <div className="flex items-start gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                        <div>
                          <div className="font-medium text-sm">{t("workflowIntelligence.increaseThroughput")}</div>
                          <div className="text-xs text-gray-600">{t("workflowIntelligence.currentThroughputImprove", { rate: Math.round((completedEntries.length / (totalMinutes / 60)) * 10) / 10 })}</div>
                        </div>
                      </div>
                    )}
                    
                    {/* General workflow optimization */}
                    <div className="flex items-start gap-3 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                      <div className="w-2 h-2 bg-blue-500 rounded-full mt-2"></div>
                      <div>
                        <div className="font-medium text-sm">{t("workflowIntelligence.workflowStandardization")}</div>
                        <div className="text-xs text-gray-600">{t("workflowIntelligence.createChecklistsReduce")}</div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Action Items */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Target className="h-5 w-5 mr-2 text-purple-500" />
                  {t("workflowIntelligence.recommendedActions")}
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {overduePendingRepairs.length > 0 && (
                    <div className="p-4 border border-red-200 rounded-lg bg-red-50 dark:bg-red-900/10">
                      <div className="flex items-center gap-2 mb-2">
                        <AlertTriangle className="h-4 w-4 text-red-500" />
                        <span className="font-medium text-red-700">{t("workflowIntelligence.urgentProcessOverdue")}</span>
                      </div>
                      <p className="text-sm text-red-600">{t("workflowIntelligence.reviewPrioritizeRepairs", { count: overduePendingRepairs.length })}</p>
                    </div>
                  )}
                  
                  <div className="p-4 border border-blue-200 rounded-lg bg-blue-50 dark:bg-blue-900/10">
                    <div className="flex items-center gap-2 mb-2">
                      <Users className="h-4 w-4 text-blue-500" />
                      <span className="font-medium text-blue-700">{t("workflowIntelligence.teamTraining")}</span>
                    </div>
                    <p className="text-sm text-blue-600">{t("workflowIntelligence.identifySkillGaps")}</p>
                  </div>
                  
                  <div className="p-4 border border-green-200 rounded-lg bg-green-50 dark:bg-green-900/10">
                    <div className="flex items-center gap-2 mb-2">
                      <BarChart3 className="h-4 w-4 text-green-500" />
                      <span className="font-medium text-green-700">{t("workflowIntelligence.processDocumentation")}</span>
                    </div>
                    <p className="text-sm text-green-600">{t("workflowIntelligence.documentBestPractices")}</p>
                  </div>
                  
                  <div className="p-4 border border-purple-200 rounded-lg bg-purple-50 dark:bg-purple-900/10">
                    <div className="flex items-center gap-2 mb-2">
                      <TrendingUp className="h-4 w-4 text-purple-500" />
                      <span className="font-medium text-purple-700">{t("workflowIntelligence.capacityPlanning")}</span>
                    </div>
                    <p className="text-sm text-purple-600">{t("workflowIntelligence.useDataOptimizeStaffing")}</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
