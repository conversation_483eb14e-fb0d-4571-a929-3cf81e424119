import React from "react";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface EmptyStateProps {
  icon: React.ReactNode;
  title: string;
  subtitle?: string;
  action?: {
    label: string;
    onClick: () => void;
  };
  tip?: string;
  className?: string;
}

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon,
  title,
  subtitle,
  action,
  tip,
  className,
}) => {
  return (
    <div className={cn("flex flex-col items-center justify-center py-12 px-4 text-center", className)}>
      <div className="mb-4 text-muted-foreground/50">
        {icon}
      </div>
      <h3 className="text-lg font-semibold text-foreground mb-2">{title}</h3>
      {subtitle && (
        <p className="text-sm text-muted-foreground mb-4 max-w-sm">{subtitle}</p>
      )}
      {action && (
        <Button onClick={action.onClick} variant="outline" className="mb-3">
          {action.label}
        </Button>
      )}
      {tip && (
        <p className="text-xs text-muted-foreground/70 max-w-xs">{tip}</p>
      )}
    </div>
  );
};