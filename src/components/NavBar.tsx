import React from "react";
import { Link, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { LogOut, Package } from "lucide-react";
import LanguageSwitcher from "./LanguageSwitcher";
import ThemeSwitcher from "./ThemeSwitcher";
import KeyboardShortcutsButton from "./KeyboardShortcutsButton";
import Logo from "./Logo";
import { isFeatureEnabled } from "@/config/features";

import { useAuth } from "@/context/AuthContext";

const NavBar: React.FC = () => {
  const { t } = useTranslation();
  const { signOut } = useAuth();
  const location = useLocation();

  // Check if the current path matches a pattern
  const isActive = (path: string) => {
    if (path === "/") {
      return location.pathname === "/";
    }
    // For repair detail pages
    if (path === "/repair") {
      return location.pathname.startsWith("/repair/");
    }
    return location.pathname === path;
  };

  const handleLogout = async () => {
    await signOut();
  };

  return (
    <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
      <div className="container mx-auto px-4 py-3 flex items-center justify-between flex-wrap gap-4">
        <Link to="/" className="flex items-center gap-2">
          <Logo size={24} className="h-6 w-6" />
          <span className="font-bold text-xl dark:text-white">
            {t("app.title")}
          </span>
        </Link>

        <nav className="flex items-center gap-2 md:gap-4 flex-wrap">
          <Link to="/">
            <Button
              variant={isActive("/") ? "default" : "ghost"}
              className={
                isActive("/") ? "font-bold border-b-2 border-primary" : ""
              }
            >
              {t("common.dashboard")}
            </Button>
          </Link>
          <Link to="/new-repair">
            <Button
              variant={isActive("/new-repair") ? "default" : "ghost"}
              className={
                isActive("/new-repair")
                  ? "font-bold border-b-2 border-primary"
                  : ""
              }
            >
              {t("common.newRepair")}
            </Button>
          </Link>
          <Link to="/search">
            <Button
              variant={isActive("/search") ? "default" : "ghost"}
              className={
                isActive("/search") ? "font-bold border-b-2 border-primary" : ""
              }
            >
              {t("common.search")}
            </Button>
          </Link>
          <Link to="/stock">
            <Button
              variant={isActive("/stock") ? "default" : "ghost"}
              className={
                isActive("/stock") ? "font-bold border-b-2 border-primary" : ""
              }
            >
              <Package className="h-4 w-4 mr-2" />
              {t("common.stock")}
            </Button>
          </Link>
          <Link to="/lottery">
            <Button
              variant={isActive("/lottery") ? "default" : "ghost"}
              className={
                isActive("/lottery")
                  ? "font-bold border-b-2 border-primary"
                  : ""
              }
            >
              {t("common.lottery")}
            </Button>
          </Link>

          {/* Show repair detail indicator when on a repair detail page */}
          {isActive("/repair") && (
            <Button
              variant="default"
              className="font-bold border-b-2 border-primary"
            >
              {t("repair.repairDetails")}
            </Button>
          )}

          {/* Visual separator */}
          <div className="h-6 w-px bg-gray-300 dark:bg-gray-600 mx-1 md:mx-2"></div>

          {/* Theme and Keyboard Shortcuts */}
          <div className="flex items-center gap-1 md:gap-2">
            <ThemeSwitcher />
            <KeyboardShortcutsButton />
            <LanguageSwitcher />
          </div>

          <Button
            variant="ghost"
            onClick={handleLogout}
            className="flex items-center gap-2"
          >
            <LogOut className="h-4 w-4" />
            <span className="hidden md:inline">{t("auth.logout")}</span>
          </Button>
        </nav>
      </div>
    </header>
  );
};

export default NavBar;
