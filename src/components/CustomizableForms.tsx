import React, { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Checkbox } from "@/components/ui/checkbox";
import { Textarea } from "@/components/ui/textarea";
import { useTranslation } from "react-i18next";
import { DndContext, closestCenter, KeyboardSensor, PointerSensor, useSensor, useSensors } from '@dnd-kit/core';
import { arrayMove, SortableContext, sortableKeyboardCoordinates, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';

interface FormTemplate {
  id: string;
  name: string;
  description: string | null;
  device_type: string;
  fields: FormField[];
  is_active: boolean;
  repair_shop_id: string;
  created_at: string;
  updated_at: string;
}

interface FormField {
  id: string;
  name: string;
  type: 'text' | 'textarea' | 'checkbox' | 'select' | 'radio' | 'number';
  required: boolean;
  options?: string[];
  placeholder?: string;
  order: number;
}

interface FormResponse {
  id: string;
  repair_id: string;
  template_id: string;
  responses: Record<string, any>;
  created_at: string;
  updated_at: string;
  template?: {
    name: string;
    device_type: string;
  };
  repair?: {
    customer_name: string;
    phone_model: string;
    ticket_number: string;
  };
}

// Sortable field item component
function SortableFieldItem({ field, onRemove }: { field: FormField, onRemove: (id: string) => void }) {
  const {
    attributes,
    listeners,
    setNodeRef,
    transform,
    transition,
  } = useSortable({ id: field.id });
  
  const style = {
    transform: CSS.Transform.toString(transform),
    transition,
  };
  
  return (
    <div
      ref={setNodeRef}
      style={style}
      {...attributes}
      className="flex items-center justify-between p-3 border rounded-md bg-background"
    >
      <div className="flex items-center gap-2">
        <div 
          {...listeners} 
          className="cursor-grab p-1 hover:bg-muted rounded"
        >
          ≡
        </div>
        <div>
          <div className="font-medium">{field.name}</div>
          <div className="text-sm text-muted-foreground">
            Type: {field.type.charAt(0).toUpperCase() + field.type.slice(1)}
            {field.required && ' • Required'}
            {field.options && ` • Options: ${field.options.join(', ')}`}
          </div>
        </div>
      </div>
      <Button 
        variant="ghost" 
        size="sm"
        onClick={() => onRemove(field.id)}
      >
        Remove
      </Button>
    </div>
  );
}

export default function CustomizableForms() {
  const { user, hasPermission, logUserAction } = useAuth();
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("templates");
  const [templates, setTemplates] = useState<FormTemplate[]>([]);
  const [responses, setResponses] = useState<FormResponse[]>([]);
  const [selectedShop, setSelectedShop] = useState<string | null>(null);
  const [shops, setShops] = useState<any[]>([]);
  const [templateDialogOpen, setTemplateDialogOpen] = useState(false);
  const [responseDialogOpen, setResponseDialogOpen] = useState(false);
  const [selectedTemplate, setSelectedTemplate] = useState<FormTemplate | null>(null);
  const [selectedResponse, setSelectedResponse] = useState<FormResponse | null>(null);
  
  // Form template states
  const [templateName, setTemplateName] = useState("");
  const [templateDescription, setTemplateDescription] = useState("");
  const [templateDeviceType, setTemplateDeviceType] = useState("");
  const [templateFields, setTemplateFields] = useState<FormField[]>([]);
  const [newFieldName, setNewFieldName] = useState("");
  const [newFieldType, setNewFieldType] = useState<FormField["type"]>("text");
  const [newFieldRequired, setNewFieldRequired] = useState(false);
  const [newFieldOptions, setNewFieldOptions] = useState("");
  const [newFieldPlaceholder, setNewFieldPlaceholder] = useState("");

  const canManageForms = hasPermission("manage_settings");
  const canViewForms = hasPermission("manage_repairs");

  useEffect(() => {
    if (!user) return;
    
    // Get all shops the user has access to
    const fetchShops = async () => {
      const { data, error } = await supabase
        .from("repair_shops")
        .select("id, name")
        .order("name");
      
      if (!error && data) {
        setShops(data);
        if (!selectedShop && data.length > 0) {
          setSelectedShop(data[0].id);
        }
      }
    };
    
    fetchShops();
  }, [user, selectedShop]);

  useEffect(() => {
    if (!user || !selectedShop || !canViewForms) return;
    
    // Get all form templates for the selected shop
    const fetchTemplates = async () => {
      const { data, error } = await supabase
        .from("form_templates")
        .select("*")
        .eq("repair_shop_id", selectedShop)
        .order("created_at", { ascending: false });
      
      if (!error && data) {
        setTemplates(data);
      }
    };
    
    fetchTemplates();
  }, [user, selectedShop, canViewForms]);

  useEffect(() => {
    if (!user || !selectedShop || activeTab !== "responses" || !canViewForms) return;
    
    // Get all form responses for the selected shop
    const fetchResponses = async () => {
      const { data, error } = await supabase
        .from("form_responses")
        .select(`
          *,
          template:template_id(name, device_type),
          repair:repair_id(customer_name, phone_model, ticket_number)
        `)
        .eq("repair_shop_id", selectedShop)
        .order("created_at", { ascending: false });
      
      if (!error && data) {
        setResponses(data);
      }
    };
    
    fetchResponses();
  }, [user, selectedShop, activeTab, canViewForms]);

  const handleTemplateSelect = (template: FormTemplate) => {
    setSelectedTemplate(template);
    setTemplateName(template.name);
    setTemplateDescription(template.description || "");
    setTemplateDeviceType(template.device_type);
    setTemplateFields(template.fields || []);
    setTemplateDialogOpen(true);
  };

  const handleResponseSelect = (response: FormResponse) => {
    setSelectedResponse(response);
    setResponseDialogOpen(true);
  };

  const resetTemplateForm = () => {
    setSelectedTemplate(null);
    setTemplateName("");
    setTemplateDescription("");
    setTemplateDeviceType("");
    setTemplateFields([]);
  };

  const addField = () => {
    if (!newFieldName) {
      toast.error("Field name is required");
      return;
    }
    
    const newField: FormField = {
      id: `field-${Date.now()}`,
      name: newFieldName,
      type: newFieldType,
      required: newFieldRequired,
      order: templateFields.length
    };
    
    if ((newFieldType === 'select' || newFieldType === 'radio') && newFieldOptions) {
      newField.options = newFieldOptions.split(',').map(opt => opt.trim());
    }
    
    if (newFieldPlaceholder) {
      newField.placeholder = newFieldPlaceholder;
    }
    
    setTemplateFields([...templateFields, newField]);
    setNewFieldName("");
    setNewFieldType("text");
    setNewFieldRequired(false);
    setNewFieldOptions("");
    setNewFieldPlaceholder("");
  };

  const removeField = (fieldId: string) => {
    setTemplateFields(templateFields.filter(field => field.id !== fieldId));
  };

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    })
  );

  const handleDragEnd = (event: any) => {
    const { active, over } = event;
    
    if (active.id !== over.id) {
      setTemplateFields((items) => {
        const oldIndex = items.findIndex(item => item.id === active.id);
        const newIndex = items.findIndex(item => item.id === over.id);
        
        const updatedItems = arrayMove(items, oldIndex, newIndex).map((item, index) => ({
          ...item,
          order: index
        }));
        
        return updatedItems;
      });
    }
  };

  const handleSaveTemplate = async () => {
    if (!selectedShop || !templateName || !templateDeviceType || templateFields.length === 0) {
      toast.error("Please fill in all required fields and add at least one form field");
      return;
    }
    
    try {
      const templateData = {
        name: templateName,
        description: templateDescription || null,
        device_type: templateDeviceType,
        fields: templateFields,
        is_active: true,
        repair_shop_id: selectedShop
      };
      
      let result;
      
      if (selectedTemplate) {
        // Update existing template
        const { data, error } = await supabase
          .from("form_templates")
          .update(templateData)
          .eq("id", selectedTemplate.id)
          .select()
          .single();
        
        if (error) throw error;
        result = data;
        
        await logUserAction("update_form_template", { 
          template_id: selectedTemplate.id,
          template_name: templateName
        });
        
        toast.success("Form template updated successfully");
      } else {
        // Create new template
        const { data, error } = await supabase
          .from("form_templates")
          .insert(templateData)
          .select()
          .single();
        
        if (error) throw error;
        result = data;
        
        await logUserAction("create_form_template", { 
          template_id: result.id,
          template_name: templateName
        });
        
        toast.success("Form template created successfully");
      }
      
      resetTemplateForm();
      setTemplateDialogOpen(false);
      
      // Refresh templates list
      const { data: updatedTemplates } = await supabase
        .from("form_templates")
        .select("*")
        .eq("repair_shop_id", selectedShop)
        .order("created_at", { ascending: false });
      
      if (updatedTemplates) {
        setTemplates(updatedTemplates);
      }
    } catch (error) {
      toast.error("Failed to save form template");
      console.error(error);
    }
  };

  const handleDeleteTemplate = async (templateId: string) => {
    if (!confirm("Are you sure you want to delete this template? All associated form responses will also be deleted.")) {
      return;
    }
    
    try {
      const { error } = await supabase
        .from("form_templates")
        .delete()
        .eq("id", templateId);
      
      if (error) throw error;
      
      await logUserAction("delete_form_template", { template_id: templateId });
      
      toast.success("Form template deleted successfully");
      
      // Refresh templates list
      const { data: updatedTemplates } = await supabase
        .from("form_templates")
        .select("*")
        .eq("repair_shop_id", selectedShop)
        .order("created_at", { ascending: false });
      
      if (updatedTemplates) {
        setTemplates(updatedTemplates);
      }
    } catch (error) {
      toast.error("Failed to delete form template");
      console.error(error);
    }
  };

  const exportResponsesToCSV = async (templateId?: string) => {
    try {
      let query = supabase
        .from("form_responses")
        .select(`
          *,
          template:template_id(name, device_type),
          repair:repair_id(customer_name, phone_model, ticket_number)
        `)
        .eq("repair_shop_id", selectedShop);
      
      if (templateId) {
        query = query.eq("template_id", templateId);
      }
      
      const { data, error } = await query.order("created_at", { ascending: false });
      
      if (error) throw error;
      
      if (!data || data.length === 0) {
        toast.error("No form responses to export");
        return;
      }
      
      // Process data for CSV
      const csvRows = [];
      
      // Create headers
      const baseHeaders = ["Date", "Customer", "Device", "Ticket", "Form Template"];
      
      // Find all unique field names across all responses
      const allFields = new Set<string>();
      data.forEach(response => {
        if (response.responses) {
          Object.keys(response.responses).forEach(field => allFields.add(field));
        }
      });
      
      const headers = [...baseHeaders, ...Array.from(allFields)];
      csvRows.push(headers.join(','));
      
      // Add data rows
      data.forEach(response => {
        const row = [
          new Date(response.created_at).toLocaleDateString(),
          response.repair?.customer_name || 'Unknown',
          response.repair?.phone_model || 'Unknown',
          response.repair?.ticket_number || 'Unknown',
          response.template?.name || 'Unknown'
        ];
        
        // Add field values
        Array.from(allFields).forEach(field => {
          const value = response.responses && response.responses[field] 
            ? response.responses[field].toString().replace(/,/g, ';') // Replace commas to avoid CSV issues
            : '';
          row.push(value);
        });
        
        csvRows.push(row.join(','));
      });
      
      // Create and download CSV
      const csvContent = csvRows.join('\n');
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.setAttribute('href', url);
      link.setAttribute('download', `form-responses-${new Date().toISOString().split('T')[0]}.csv`);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      await logUserAction("export_form_responses", { 
        template_id: templateId,
        count: data.length
      });
      
      toast.success(`Exported ${data.length} form responses`);
    } catch (error) {
      toast.error("Failed to export form responses");
      console.error(error);
    }
  };

  if (!canViewForms) {
    return (
      <div className="flex items-center justify-center h-screen">
        Access denied. You need form management privileges.
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">{t("customForms.title")}</h1>
      
      <div className="flex items-center space-x-4 mb-6">
        <div className="flex-1">
          <Label htmlFor="shop-select">Select Shop</Label>
          <Select 
            value={selectedShop || ""} 
            onValueChange={(value) => setSelectedShop(value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a shop" />
            </SelectTrigger>
            <SelectContent>
              {shops.map((shop) => (
                <SelectItem key={shop.id} value={shop.id}>
                  {shop.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="templates">{t("customForms.title")}</TabsTrigger>
          <TabsTrigger value="responses">{t("customForms.formBuilder")}</TabsTrigger>
        </TabsList>
        
        <TabsContent value="templates">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>{t("customForms.title")}</CardTitle>
                <CardDescription>
                  {t("customForms.title")}
                </CardDescription>
              </div>
              {canManageForms && (
                <Button onClick={() => {
                  resetTemplateForm();
                  setTemplateDialogOpen(true);
                }}>
                  {t("customForms.createForm")}
                </Button>
              )}
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {templates.length === 0 ? (
                  <div className="col-span-full p-4 text-center text-muted-foreground">
                    {t("customForms.noForms")}
                  </div>
                ) : (
                  templates.map((template) => (
                    <Card key={template.id} className="overflow-hidden">
                      <CardHeader className="pb-2">
                        <CardTitle>{template.name}</CardTitle>
                        <CardDescription>
                          Device Type: {template.device_type}
                        </CardDescription>
                      </CardHeader>
                      <CardContent>
                        <div className="mb-4">
                          <p className="text-sm text-muted-foreground">
                            {template.description || 'No description'}
                          </p>
                          <p className="text-sm mt-2">
                            {template.fields?.length || 0} fields
                          </p>
                        </div>
                        <div className="flex space-x-2">
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleTemplateSelect(template)}
                          >
                            {t("customForms.editForm")}
                          </Button>
                          {canManageForms && (
                            <Button 
                              variant="destructive" 
                              size="sm"
                              onClick={() => handleDeleteTemplate(template.id)}
                            >
                              {t("customForms.deleteForm")}
                            </Button>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="responses">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>{t("customForms.formBuilder")}</CardTitle>
                <CardDescription>
                  {t("customForms.formBuilder")}
                </CardDescription>
              </div>
              <Button onClick={() => exportResponsesToCSV()}>
                Export All Responses
              </Button>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="p-2 text-left font-medium">{t("common.date")}</th>
                      <th className="p-2 text-left font-medium">{t("repair.customerName")}</th>
                      <th className="p-2 text-left font-medium">{t("customForms.deviceType")}</th>
                      <th className="p-2 text-left font-medium">{t("customForms.title")}</th>
                      <th className="p-2 text-left font-medium">{t("common.actions")}</th>
                    </tr>
                  </thead>
                  <tbody>
                    {responses.length === 0 ? (
                      <tr>
                        <td colSpan={5} className="p-4 text-center text-muted-foreground">
                          {t("customForms.noForms")}
                        </td>
                      </tr>
                    ) : (
                      responses.map((response) => (
                        <tr key={response.id} className="border-b">
                          <td className="p-2">
                            {new Date(response.created_at).toLocaleString()}
                          </td>
                          <td className="p-2">
                            {response.repair?.customer_name || 'Unknown'}
                          </td>
                          <td className="p-2">
                            {response.repair?.phone_model || 'Unknown'}
                            {response.repair?.ticket_number && (
                              <span className="block text-xs text-muted-foreground">
                                Ticket: {response.repair.ticket_number}
                              </span>
                            )}
                          </td>
                          <td className="p-2">
                            {response.template?.name || 'Unknown'}
                            {response.template?.device_type && (
                              <span className="block text-xs text-muted-foreground">
                                {response.template.device_type}
                              </span>
                            )}
                          </td>
                          <td className="p-2">
                            <div className="flex space-x-2">
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => handleResponseSelect(response)}
                              >
                                {t("common.viewDetails")}
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* Form Template Dialog */}
      <Dialog open={templateDialogOpen} onOpenChange={setTemplateDialogOpen}>
        <DialogContent className="max-w-3xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>
              {selectedTemplate ? 'Edit Form Template' : 'Create Form Template'}
            </DialogTitle>
            <DialogDescription>
              {selectedTemplate 
                ? 'Update the form template details below' 
                : 'Design a custom form for device intake'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="template-name">Template Name *</Label>
                <Input
                  id="template-name"
                  value={templateName}
                  onChange={(e) => setTemplateName(e.target.value)}
                  placeholder="e.g., Phone Intake Form"
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="template-device-type">Device Type *</Label>
                <Input
                  id="template-device-type"
                  value={templateDeviceType}
                  onChange={(e) => setTemplateDeviceType(e.target.value)}
                  placeholder="e.g., Smartphone, Laptop, Tablet"
                />
              </div>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="template-description">Description</Label>
              <Textarea
                id="template-description"
                value={templateDescription}
                onChange={(e) => setTemplateDescription(e.target.value)}
                placeholder="Describe the purpose of this form"
                rows={2}
              />
            </div>
            
            <div className="border-t pt-4">
              <h3 className="text-lg font-medium mb-2">Form Fields</h3>
              
              <div className="bg-muted/30 p-4 rounded-md mb-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div className="space-y-2">
                    <Label htmlFor="new-field-name">Field Name *</Label>
                    <Input
                      id="new-field-name"
                      value={newFieldName}
                      onChange={(e) => setNewFieldName(e.target.value)}
                      placeholder="e.g., IMEI Number"
                    />
                  </div>
                  
                  <div className="space-y-2">
                    <Label htmlFor="new-field-type">Field Type *</Label>
                    <Select 
                      value={newFieldType} 
                      onValueChange={(value: FormField["type"]) => setNewFieldType(value)}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="text">Text</SelectItem>
                        <SelectItem value="textarea">Text Area</SelectItem>
                        <SelectItem value="number">Number</SelectItem>
                        <SelectItem value="checkbox">Checkbox</SelectItem>
                        <SelectItem value="select">Dropdown</SelectItem>
                        <SelectItem value="radio">Radio Buttons</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  {(newFieldType === 'select' || newFieldType === 'radio') && (
                    <div className="space-y-2">
                      <Label htmlFor="new-field-options">Options (comma separated) *</Label>
                      <Input
                        id="new-field-options"
                        value={newFieldOptions}
                        onChange={(e) => setNewFieldOptions(e.target.value)}
                        placeholder="e.g., Yes, No, Maybe"
                      />
                    </div>
                  )}
                  
                  {(newFieldType === 'text' || newFieldType === 'textarea' || newFieldType === 'number') && (
                    <div className="space-y-2">
                      <Label htmlFor="new-field-placeholder">Placeholder</Label>
                      <Input
                        id="new-field-placeholder"
                        value={newFieldPlaceholder}
                        onChange={(e) => setNewFieldPlaceholder(e.target.value)}
                        placeholder="e.g., Enter the device IMEI"
                      />
                    </div>
                  )}
                  
                  <div className="flex items-center space-x-2 h-full">
                    <div className="flex items-center space-x-2 pt-6">
                      <Checkbox 
                        id="new-field-required"
                        checked={newFieldRequired}
                        onCheckedChange={(checked) => setNewFieldRequired(checked === true)}
                      />
                      <Label htmlFor="new-field-required">Required Field</Label>
                    </div>
                  </div>
                </div>
                
                <Button onClick={addField}>Add Field</Button>
              </div>
              
              {templateFields.length > 0 ? (
                <DndContext 
                  sensors={sensors}
                  collisionDetection={closestCenter}
                  onDragEnd={handleDragEnd}
                >
                  <SortableContext 
                    items={templateFields.map(field => field.id)}
                    strategy={verticalListSortingStrategy}
                  >
                    <div className="space-y-2">
                      {templateFields
                        .sort((a, b) => a.order - b.order)
                        .map((field) => (
                          <SortableFieldItem 
                            key={field.id} 
                            field={field} 
                            onRemove={removeField} 
                          />
                        ))}
                    </div>
                  </SortableContext>
                </DndContext>
              ) : (
                <div className="text-center p-4 text-muted-foreground">
                  No fields added yet. Add fields to create your form.
                </div>
              )}
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setTemplateDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveTemplate}>Save Template</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Form Response Dialog */}
      <Dialog open={responseDialogOpen} onOpenChange={setResponseDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Form Response Details</DialogTitle>
            <DialogDescription>
              {selectedResponse?.template?.name} - {new Date(selectedResponse?.created_at || "").toLocaleString()}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-medium">Customer</h3>
                <p>{selectedResponse?.repair?.customer_name || 'Unknown'}</p>
              </div>
              <div>
                <h3 className="font-medium">Device</h3>
                <p>{selectedResponse?.repair?.phone_model || 'Unknown'}</p>
              </div>
              <div>
                <h3 className="font-medium">Ticket Number</h3>
                <p>{selectedResponse?.repair?.ticket_number || 'Unknown'}</p>
              </div>
              <div>
                <h3 className="font-medium">Form Template</h3>
                <p>{selectedResponse?.template?.name || 'Unknown'}</p>
              </div>
            </div>
            
            <div className="border-t pt-4">
              <h3 className="font-medium mb-2">Form Responses</h3>
              
              {selectedResponse?.responses ? (
                <div className="space-y-2">
                  {Object.entries(selectedResponse.responses).map(([field, value]) => (
                    <div key={field} className="grid grid-cols-2 gap-2 p-2 border-b">
                      <div className="font-medium">{field}</div>
                      <div>{value === true ? 'Yes' : value === false ? 'No' : value || 'N/A'}</div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center p-4 text-muted-foreground">
                  No response data available.
                </div>
              )}
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setResponseDialogOpen(false)}>Close</Button>
            <Button onClick={() => exportResponsesToCSV(selectedResponse?.template_id)}>
              Export Template Responses
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}