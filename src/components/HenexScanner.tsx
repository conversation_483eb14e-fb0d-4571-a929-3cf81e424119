// src/components/HenexScanner.tsx
import React, { useEffect, useRef, useState, useCallback } from "react";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON><PERSON>riangle, Barcode } from "lucide-react";
import { useTranslation } from "react-i18next";
import { Input } from "@/components/ui/input";

// Function to deduplicate characters in a string
// For example, "66997722883311221100886633" becomes "6972831210863"
const deduplicateCharacters = (input: string): string => {
  // Check if the string has an even length and might contain duplicated characters
  if (input.length % 2 !== 0) return input;

  // Check if each character is duplicated (e.g., "aabbcc" -> "abc")
  let result = "";
  for (let i = 0; i < input.length; i += 2) {
    if (i + 1 < input.length && input[i] === input[i + 1]) {
      result += input[i];
    } else {
      // If characters don't match, it's probably not a simple duplication pattern
      return input;
    }
  }

  return result;
};

interface HenexScannerProps {
  onScan: (value: string) => void;
  autoStart?: boolean;
  showStartButton?: boolean; // Whether to show a start button even with autoStart=true
}

const HenexScanner: React.FC<HenexScannerProps> = ({
  onScan,
  autoStart = false,
  showStartButton = true,
}) => {
  const [isListening, setIsListening] = useState(false);
  const [manuallyStarted, setManuallyStarted] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [scanBuffer, setScanBuffer] = useState<string>("");
  const inputRef = useRef<HTMLInputElement>(null);
  const timeoutRef = useRef<number | null>(null);
  const { t } = useTranslation();

  // Function to clean up scanned input
  const cleanScannedInput = (input: string): string => {
    // First, check for character duplication (e.g., "66997722883311221100886633" should be "6972831210863")
    // This is a common issue with barcode scanners
    const deduplicated = deduplicateCharacters(input);
    if (deduplicated !== input) {
      console.log(
        "Detected duplicated characters. Deduplicated:",
        deduplicated
      );
      return deduplicated;
    }

    // Check if the input is a numeric barcode (any number of digits)
    const numericPattern = /^[0-9]+$/;
    if (numericPattern.test(input.trim())) {
      console.log("Detected numeric barcode:", input.trim());

      // For numeric barcodes, we simply use the number as the ticket number
      // Remove leading zeros if any
      const ticketNumber = input.trim().replace(/^0+/, "");

      console.log("Extracted ticket number from barcode:", ticketNumber);
      return ticketNumber;
    }

    // Next, try to extract a UUID pattern
    const uuidPattern =
      /([a-f0-9]{8}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{4}-[a-f0-9]{12})/i;
    const match = input.match(uuidPattern);
    if (match) {
      return match[1];
    }

    // Define a mapping of AZERTY to expected characters for UUID
    // UUIDs only contain: 0-9, a-f, and hyphens
    const keyboardMappings: Record<string, string> = {
      // Letters used in UUIDs (only a-f are valid)
      q: "a", // This is the most important mapping for UUIDs

      // These letters stay the same in both layouts and are used in UUIDs
      b: "b",
      c: "c",
      d: "d",
      e: "e",
      f: "f",

      // Number keys on AZERTY that might be used in UUIDs
      "&": "1",
      é: "2",
      '"': "3",
      "'": "4",
      "(": "5",
      "-": "6",
      è: "7",
      _: "8",
      ç: "9",
      à: "0",
      ")": "0",

      // Special characters that might appear instead of hyphens
      "°": "-", // Degree symbol (most common replacement for hyphen)
      "*": "-",
      "^": "-",
      $: "-",
      "#": "-",
      "|": "-",

      // Numbers that stay the same
      "0": "0",
      "1": "1",
      "2": "2",
      "3": "3",
      "4": "4",
      "5": "5",
      "6": "6",
      "7": "7",
      "8": "8",
      "9": "9",
    };

    // Try to map the input directly to a UUID format
    // First, decode URL encoding but keep special characters for mapping
    let decodedInput: string;
    try {
      decodedInput = decodeURIComponent(input);
    } catch (e) {
      // If decoding fails, use the original input
      decodedInput = input;
    }

    // Check for the specific pattern we're seeing with the degree symbol
    // e5ea5632°4e64°4qfe°bf62-0qd3q3b1244f
    if (decodedInput.includes("°")) {
      console.log("Detected degree symbol in UUID, replacing with hyphens");
      decodedInput = decodedInput.replace(/°/g, "-");
    }

    // Then map each character according to the keyboard layout
    let mappedInput = "";
    for (let i = 0; i < decodedInput.length; i++) {
      const char = decodedInput[i].toLowerCase();
      mappedInput += keyboardMappings[char] || char;
    }

    // For UUIDs, we know that certain patterns will always be the same
    // Convert any 'q' that should be 'a' in a UUID context
    mappedInput = mappedInput.replace(/q([0-9f])/g, "a$1");

    // Log the mapped input for debugging
    console.log("After AZERTY mapping:", mappedInput);

    // For UUIDs, we need to extract exactly 32 hexadecimal characters
    // and format them correctly with hyphens

    // First, extract only valid UUID characters (0-9, a-f)
    const hexChars = mappedInput.replace(/[^0-9a-f]/g, "");

    console.log("Extracted hex characters:", hexChars);

    // If we have at least 32 characters, we can try to format it as a UUID
    if (hexChars.length >= 32) {
      // Take exactly 32 characters (in case there are extra)
      const uuidChars = hexChars.substring(0, 32);

      // Format as UUID with hyphens in the correct positions
      const formattedUUID =
        uuidChars.substring(0, 8) +
        "-" +
        uuidChars.substring(8, 12) +
        "-" +
        uuidChars.substring(12, 16) +
        "-" +
        uuidChars.substring(16, 20) +
        "-" +
        uuidChars.substring(20, 32);

      console.log("Formatted UUID:", formattedUUID);

      // Since we've carefully extracted and formatted the UUID,
      // we can be confident it's in the correct format
      return formattedUUID;
    }

    // If all else fails, just return the original input
    // This allows manual entry to still work
    return input.trim();
  };

  // Function to handle keyboard input from the scanner
  const handleScannerInput = useCallback(
    (e: KeyboardEvent) => {
      // If Enter key is pressed, it typically means the scanner has finished sending data
      if (e.key === "Enter") {
        if (scanBuffer.trim()) {
          // Play beep sound on successful scan
          const audio = new Audio("beep.mp3");
          audio.play();

          // Clean up and process the scanned code
          const cleanedCode = cleanScannedInput(scanBuffer);

          // Log detailed information about the scan for debugging
          console.log({
            originalScan: scanBuffer,
            scanLength: scanBuffer.length,
            urlEncoded: encodeURIComponent(scanBuffer),
            cleanedScan: cleanedCode,
            cleanedLength: cleanedCode.length,
            isDuplicated:
              cleanedCode.length !== scanBuffer.length &&
              scanBuffer.length % 2 === 0,
          });

          onScan(cleanedCode);
          setScanBuffer("");
        }
        return;
      }

      // Ignore special keys like Shift, Control, Alt, etc.
      if (e.key.length > 1) return;

      // Add the character to the buffer
      setScanBuffer((prev) => prev + e.key);

      // Reset the timeout
      if (timeoutRef.current) {
        window.clearTimeout(timeoutRef.current);
      }

      // Set a timeout to clear the buffer if no more input is received
      // This helps distinguish between manual typing and scanner input
      // as scanners typically send data very quickly
      timeoutRef.current = window.setTimeout(() => {
        // If the buffer is still there after the timeout, it might be manual input
        // We'll keep it for now, but it will be cleared on Enter or when the scanner sends new data
      }, 50);
    },
    [onScan, scanBuffer]
  );

  // Start listening for scanner input
  const startListening = useCallback((manual: boolean = false) => {
    setError(null);
    setIsListening(true);
    setManuallyStarted(manual);

    // Focus the input field to capture keyboard events
    if (inputRef.current) {
      inputRef.current.focus();
    }
  }, []);

  // Stop listening for scanner input
  const stopListening = useCallback(() => {
    setIsListening(false);
    setManuallyStarted(false);
    setScanBuffer("");

    if (timeoutRef.current) {
      window.clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);

  // Toggle listening state
  const toggleListening = () => {
    if (isListening) {
      stopListening();
    } else {
      startListening(true); // Pass true to indicate manual start
    }
  };

  // Auto-start effect - only auto-start if showStartButton is false
  useEffect(() => {
    if (autoStart && !showStartButton && !isListening) {
      startListening();
    } else if (
      (!autoStart || showStartButton) &&
      isListening &&
      !manuallyStarted
    ) {
      stopListening();
    }
  }, [
    autoStart,
    showStartButton,
    isListening,
    startListening,
    stopListening,
    manuallyStarted,
  ]);

  // Set up and clean up event listeners
  useEffect(() => {
    if (isListening) {
      // Add event listener for keyboard input
      document.addEventListener("keydown", handleScannerInput);

      // Focus the input field
      if (inputRef.current) {
        inputRef.current.focus();
      }
    }

    // Clean up function
    return () => {
      document.removeEventListener("keydown", handleScannerInput);

      if (timeoutRef.current) {
        window.clearTimeout(timeoutRef.current);
        timeoutRef.current = null;
      }
    };
  }, [isListening, handleScannerInput]);

  return (
    <div className="space-y-4">
      {/* Show the button if autoStart is false or showStartButton is true */}
      {(showStartButton || !autoStart) && (
        <Button onClick={toggleListening}>
          <Barcode className="mr-2 h-4 w-4" />
          {isListening ? t("common.stopScanning") : t("common.startScanning")}
        </Button>
      )}

      {isListening && (
        <div className="mt-2">
          <div className="flex items-center gap-2 mb-2">
            <div className="w-2 h-2 bg-green-500 rounded-full animate-scanner-pulse"></div>
            <div className="text-sm font-medium text-green-600">
              {t("scanner.scanningActive")} - {t("scanner.scannerInstructions")}
            </div>
          </div>

          <div className="relative w-full">
            <Input
              ref={inputRef}
              type="text"
              value={scanBuffer}
              onChange={(e) => setScanBuffer(e.target.value)}
              className="w-full pr-10" /* Add right padding for the indicator */
              placeholder={t("scanner.scanPlaceholder")}
              autoFocus
            />

            {/* Scanning indicator - positioned inside the input */}
            <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
              <div className="w-3 h-3 bg-green-500 rounded-full animate-scanner-pulse"></div>
            </div>
          </div>
        </div>
      )}

      {error && (
        <Alert variant="destructive" className="mt-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default HenexScanner;
