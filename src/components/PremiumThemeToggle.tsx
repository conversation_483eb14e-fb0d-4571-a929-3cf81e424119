import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Crown } from "lucide-react";
import { useTheme } from "@/context/ThemeContext";

const PremiumThemeToggle: React.FC = () => {
  const { isPremium } = useTheme();

  if (!isPremium) return null;

  return (
    <div className="flex items-center gap-2 text-sm">
      <Crown className="h-4 w-4 text-primary" />
      <span className="premium-badge">Premium Active</span>
    </div>
  );
};

export default PremiumThemeToggle;