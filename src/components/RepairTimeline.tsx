import React from "react";
import { useTranslation } from "react-i18next";
import { format } from "date-fns";
import { RepairItem, StatusHistory } from "@/types";
import {
  CheckCircle2,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>gle,
  <PERSON><PERSON>,
  <PERSON>otateCcw,
  CalendarClock,
} from "lucide-react";

interface RepairTimelineProps {
  repair: RepairItem;
}

const RepairTimeline: React.FC<RepairTimelineProps> = ({ repair }) => {
  const { t } = useTranslation();

  // Generate timeline events from repair data
  const generateTimelineEvents = (repair: RepairItem) => {
    const events: {
      id: string;
      date: Date;
      status: string;
      icon: React.ReactNode;
      color: string;
    }[] = [];

    // Only add creation event if there's no status history
    // or if the first status in history isn't "pending"
    const hasStatusHistory =
      repair.statusHistory && repair.statusHistory.length > 0;
    const firstStatusIsPending =
      hasStatusHistory &&
      repair.statusHistory.some((history) => history.status === "pending");

    if (!hasStatusHistory || !firstStatusIsPending) {
      events.push({
        id: "creation",
        date: repair.createdAt,
        status: "pending",
        icon: <Clock className="h-5 w-5" />,
        color: "text-yellow-500 bg-yellow-100 border-yellow-200",
      });
    }

    // Add events from status history if available
    if (repair.statusHistory && repair.statusHistory.length > 0) {
      // Sort by date
      const sortedHistory = [...repair.statusHistory].sort(
        (a, b) => a.createdAt.getTime() - b.createdAt.getTime()
      );

      // Track the last status to avoid duplicates
      let lastStatus = "";

      // Add each unique status change as an event
      sortedHistory.forEach((history) => {
        // Only add if the status is different from the last one
        if (history.status !== lastStatus) {
          events.push({
            id: history.id,
            date: history.createdAt,
            status: history.status,
            icon: getStatusIcon(history.status),
            color: getStatusColor(history.status),
          });

          // Update the last status
          lastStatus = history.status;
        }
      });
    } else {
      // If no history, infer from current status and dates
      if (repair.status === "inProgress") {
        events.push({
          id: "inProgress",
          date: new Date(), // We don't have the exact date, use current
          status: "inProgress",
          icon: <Wrench className="h-5 w-5" />,
          color: "text-blue-500 bg-blue-100 border-blue-200",
        });
      }

      if (repair.status === "completed" && repair.completedAt) {
        events.push({
          id: "completed",
          date: repair.completedAt,
          status: "completed",
          icon: <CheckCircle2 className="h-5 w-5" />,
          color: "text-green-500 bg-green-100 border-green-200",
        });
      }

      if (repair.status === "cancelled") {
        events.push({
          id: "cancelled",
          date: new Date(), // We don't have the exact date, use current
          status: "cancelled",
          icon: <AlertTriangle className="h-5 w-5" />,
          color: "text-red-500 bg-red-100 border-red-200",
        });
      }

      if (repair.status === "returned") {
        events.push({
          id: "returned",
          date: new Date(), // We don't have the exact date, use current
          status: "returned",
          icon: <RotateCcw className="h-5 w-5" />,
          color: "text-purple-500 bg-purple-100 border-purple-200",
        });
      }
    }

    // Sort events by date
    return events.sort((a, b) => a.date.getTime() - b.date.getTime());
  };

  const getStatusIcon = (
    status: "pending" | "inProgress" | "completed" | "cancelled" | "returned"
  ) => {
    switch (status) {
      case "pending":
        return <Clock className="h-5 w-5" />;
      case "inProgress":
        return <Wrench className="h-5 w-5" />;
      case "completed":
        return <CheckCircle2 className="h-5 w-5" />;
      case "cancelled":
        return <AlertTriangle className="h-5 w-5" />;
      case "returned":
        return <RotateCcw className="h-5 w-5" />;
      default:
        return <Clock className="h-5 w-5" />;
    }
  };

  const getStatusColor = (
    status: "pending" | "inProgress" | "completed" | "cancelled" | "returned"
  ) => {
    switch (status) {
      case "pending":
        return "text-yellow-500 bg-yellow-100 border-yellow-200";
      case "inProgress":
        return "text-blue-500 bg-blue-100 border-blue-200";
      case "completed":
        return "text-green-500 bg-green-100 border-green-200";
      case "cancelled":
        return "text-red-500 bg-red-100 border-red-200";
      case "returned":
        return "text-purple-500 bg-purple-100 border-purple-200";
      default:
        return "text-gray-500 bg-gray-100 border-gray-200";
    }
  };

  const timelineEvents = generateTimelineEvents(repair);

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">{t("repair.repairTimeline")}</h3>
      <div className="relative">
        {/* Vertical line */}
        <div className="absolute left-5 top-0 bottom-0 w-0.5 bg-gray-200"></div>

        {/* Timeline events */}
        <div className="space-y-6">
          {timelineEvents.map((event, index) => (
            <div key={event.id} className="relative flex items-start gap-4">
              {/* Timeline dot */}
              <div
                className={`relative z-10 flex h-10 w-10 items-center justify-center rounded-full border-2 ${event.color}`}
              >
                {event.icon}
              </div>

              {/* Event content */}
              <div className="flex-1 pt-1.5">
                <h4 className="font-medium">
                  {t(`repair.status.${event.status}`)}
                </h4>
                <div className="flex items-center text-sm text-gray-500">
                  <CalendarClock className="mr-1 h-3.5 w-3.5" />
                  {format(event.date, "dd/MM/yyyy - HH:mm")}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
};

export default RepairTimeline;
