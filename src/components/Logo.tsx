import React, { useState } from 'react';
import { Wrench } from 'lucide-react';
import { logoConfig } from '@/config/appConfig';

interface LogoProps {
  className?: string;
  size?: number;
}

/**
 * Logo component that displays either an image logo or falls back to the Wrench icon
 */
const Logo: React.FC<LogoProps> = ({ className = '', size = 48 }) => {
  const [imageError, setImageError] = useState(false);
  
  // If there's no logo path configured or if the image fails to load, show the Wrench icon
  if (!logoConfig.path || imageError) {
    return <Wrench className={`text-blue-600 ${className}`} size={size} />;
  }
  
  return (
    <img
      src={logoConfig.path}
      alt={logoConfig.alt}
      width={logoConfig.width || size}
      height={logoConfig.height || size}
      className={`object-contain ${className}`}
      onError={() => setImageError(true)}
    />
  );
};

export default Logo;
