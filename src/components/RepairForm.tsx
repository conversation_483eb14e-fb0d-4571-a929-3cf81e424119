import React, { useState } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Plus,
  X,
  User,
  Phone,
  Mail,
  Smartphone,
  CreditCard,
  Wallet,
  DollarSign,
  FileText,
  ClipboardList,
  MessageSquare,
  Loader2,
} from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RepairFormData } from "@/types";

const RepairForm: React.FC<RepairFormProps> = ({
  initialData,
  onSubmit,
  isLoading = false,
  isRTL = false,
}) => {
  const { t } = useTranslation();

  const formSchema = z.object({
    customerName: z
      .string()
      .min(2, t("repair.customerName") + " must be at least 2 characters"),
    customerPhone: z.string().optional().or(z.literal("")),
    customerEmail: z
      .string()
      .email("Invalid email format")
      .optional()
      .or(z.literal("")),
    phoneModel: z.string().optional().or(z.literal("")),
    problemDescription: z.string().optional().or(z.literal("")),
    repairPrice: z.coerce
      .number()
      .min(0, t("repair.repairPrice") + " must be a positive number")
      .optional()
      .or(z.literal(0)),
    paymentStatus: z.enum(["paid", "partial", "unpaid"]),
    downPayment: z.coerce
      .number()
      .min(0, t("repair.downPayment") + " must be a positive number")
      .optional()
      .or(z.literal(0)),
    observations: z.array(z.string()).optional(),
  });

  const form = useForm<RepairFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      customerName: "",
      customerPhone: "",
      customerEmail: "",
      phoneModel: "",
      problemDescription: "",
      repairPrice: 0,
      paymentStatus: "unpaid",
      downPayment: 0,
      observations: [],
    },
  });

  // State for managing observations
  const [newObservation, setNewObservation] = useState("");

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-8"
        style={{ textAlign: isRTL ? "right" : "left" }}
      >
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center">
            <User className="h-5 w-5 mr-2 text-blue-500" />
            {t("repair.customerInfo")}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 bg-gray-50 dark:bg-gray-800/50 p-4 rounded-md">
            <FormField
              control={form.control}
              name="customerName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t("repair.customerName")}{" "}
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <User className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Input
                        placeholder="John Doe"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="customerPhone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("repair.customerPhone")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Phone className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Input
                        placeholder="******-456-7890"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* <FormField
              control={form.control}
              name="customerEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t("repair.customerEmail")}{" "}
                    <span className="text-gray-400 text-sm">
                      ({t("common.optional")})
                    </span>
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Mail className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            /> */}

            <FormField
              control={form.control}
              name="phoneModel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("repair.phoneModel")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Smartphone className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Input
                        placeholder="iPhone 13 Pro"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="repairPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("repair.repairPrice")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <DollarSign className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Input
                        type="number"
                        placeholder="0.00"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="paymentStatus"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("repair.paymentStatus")}</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="pl-10">
                        <div className="absolute left-3 top-2.5 text-gray-400">
                          <CreditCard className="h-4 w-4" />
                        </div>
                        <SelectValue placeholder={t("repair.paymentStatus")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="paid">
                        {t("repair.payment.paid")}
                      </SelectItem>
                      <SelectItem value="partial">
                        {t("repair.payment.partial")}
                      </SelectItem>
                      <SelectItem value="unpaid">
                        {t("repair.payment.unpaid")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="downPayment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("repair.downPayment")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Wallet className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Input
                        type="number"
                        placeholder="0.00"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-500" />
            {t("repair.problemDescription")}
          </h2>
          <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-md">
            <FormField
              control={form.control}
              name="problemDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("repair.problemDescription")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <MessageSquare className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Textarea
                        placeholder={t("repair.problemDescription") + "..."}
                        className="min-h-[120px] pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Observations Section */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center">
            <ClipboardList className="h-5 w-5 mr-2 text-blue-500" />
            {t("repair.observations")}{" "}
            <span className="text-sm font-normal text-gray-500 ml-2">
              {t("repair.observationsOptional")}
            </span>
          </h2>
          <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-md space-y-4">
            <div
              className="flex gap-2"
              style={{ flexDirection: isRTL ? "row-reverse" : "row" }}
            >
              <div className="relative flex-1">
                <ClipboardList className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                <Input
                  placeholder={t("repair.addObservation") + "..."}
                  value={newObservation}
                  onChange={(e) => setNewObservation(e.target.value)}
                  className="flex-1 pl-10"
                />
              </div>
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => {
                  if (newObservation.trim()) {
                    const currentObservations =
                      form.getValues("observations") || [];
                    form.setValue("observations", [
                      ...currentObservations,
                      newObservation.trim(),
                    ]);
                    setNewObservation("");
                  }
                }}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-2">
              {form.watch("observations")?.map((observation, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between bg-gray-50 p-2 rounded-md"
                  style={{ flexDirection: isRTL ? "row-reverse" : "row" }}
                >
                  <span className="text-sm">{observation}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      const currentObservations = [
                        ...(form.getValues("observations") || []),
                      ];
                      currentObservations.splice(index, 1);
                      form.setValue("observations", currentObservations);
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>

        <Button
          type="submit"
          className="w-full bg-blue-600 hover:bg-blue-700 text-white py-2 rounded-md transition-colors flex items-center justify-center gap-2"
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              {t("common.processing")}
            </>
          ) : (
            <>
              <FileText className="h-4 w-4" />
              {t("repair.submitRepair")}
            </>
          )}
        </Button>
      </form>
    </Form>
  );
};

interface RepairFormProps {
  initialData?: RepairFormData;
  onSubmit: (data: RepairFormData) => void;
  isLoading?: boolean;
  isRTL?: boolean;
}

export default RepairForm;
