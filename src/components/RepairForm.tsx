import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { useTranslation } from "react-i18next";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { But<PERSON> } from "@/components/ui/button";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { Checkbox } from "@/components/ui/checkbox";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Plus,
  X,
  User,
  Phone,
  Mail,
  Smartphone,
  CreditCard,
  Wallet,
  DollarSign,
  FileText,
  ClipboardList,
  MessageSquare,
  Loader2,
  FileCheck,
  Package,
} from "lucide-react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { RepairFormData } from "@/types";
import { useTheme } from "@/context/ThemeContext";

const RepairForm: React.FC<RepairFormProps> = ({
  initialData,
  onSubmit,
  isLoading = false,
  isRTL = false,
}) => {
  const { t } = useTranslation();
  const { isPremium } = useTheme();

  const formSchema = z.object({
    customerName: z
      .string()
      .min(2, t("repair.customerName") + " must be at least 2 characters"),
    customerPhone: z.string().optional().or(z.literal("")),
    customerEmail: z
      .string()
      .email("Invalid email format")
      .optional()
      .or(z.literal("")),
    phoneModel: z.string().optional().or(z.literal("")),
    problemDescription: z.string().optional().or(z.literal("")),
    repairPrice: z.coerce
      .number()
      .min(0, t("repair.repairPrice") + " must be a positive number")
      .optional()
      .or(z.literal(0)),
    paymentStatus: z.enum(["paid", "partial", "unpaid"]),
    downPayment: z.coerce
      .number()
      .min(0, t("repair.downPayment") + " must be a positive number")
      .optional()
      .or(z.literal(0)),
    observations: z.array(z.string()).optional(),
    customFormResponses: z
      .object({
        templateId: z.string().optional(),
        responses: z.record(z.any()).optional(),
      })
      .optional(),
    stockItems: z
      .array(
        z.object({
          id: z.string(),
          quantity: z.number().min(1),
        })
      )
      .optional(),
    assignedTechnician: z.string().optional(),
  });

  const form = useForm<RepairFormData>({
    resolver: zodResolver(formSchema),
    defaultValues: initialData || {
      customerName: "",
      customerPhone: "",
      customerEmail: "",
      phoneModel: "",
      problemDescription: "",
      repairPrice: 0,
      paymentStatus: "unpaid",
      downPayment: 0,
      observations: [],
      customFormResponses: { templateId: undefined, responses: {} },
      stockItems: [],
      assignedTechnician: "",
    },
  });

  // State for managing observations
  const [newObservation, setNewObservation] = useState("");

  // State for custom forms
  const [availableForms, setAvailableForms] = useState<any[]>([]);
  const [selectedFormId, setSelectedFormId] = useState<string | null>(null);
  const [selectedForm, setSelectedForm] = useState<any>(null);
  const [formResponses, setFormResponses] = useState<Record<string, any>>({});

  // State for stock items
  const [availableProducts, setAvailableProducts] = useState<any[]>([]);
  const [availableCategories, setAvailableCategories] = useState<any[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<
    { id: string; quantity: number }[]
  >([]);
  const [productSearch, setProductSearch] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string>("all");

  // State for technician assignment
  const [availableTechnicians, setAvailableTechnicians] = useState<any[]>([]);
  const [assignedTechnician, setAssignedTechnician] = useState<string>("");

  const { user } = useAuth();
  const { repairShop } = useRepairShopContext();

  // Load available custom forms
  useEffect(() => {
    const loadCustomForms = async () => {
      if (!user) return;

      const { data, error } = await supabase
        .from("form_templates")
        .select("*")
        .eq("is_active", true)
        .order("name");

      if (!error && data) {
        setAvailableForms(data);
      }
    };

    loadCustomForms();
    loadAvailableProducts();
    loadAvailableTechnicians();
  }, [user]);

  // Load available technicians
  const loadAvailableTechnicians = async () => {
    if (!user) return;

    const { data, error } = await supabase.rpc("get_shop_users", {
      shop_id: repairShop?.id,
    });

    if (!error && data) {
      // Filter for technicians and administrators
      const technicians = data.filter(
        (u) => u.role === "technician" || u.role === "administrator"
      );
      setAvailableTechnicians(technicians);
    }
  };

  // Load available stock products
  const loadAvailableProducts = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("products")
        .select("id, name, sku, price, stock_quantity, category_id, is_active")
        .gt("stock_quantity", 0)
        .order("name");

      console.log("Products query result:", { data, error });

      if (!error && data) {
        setAvailableProducts(data);
      } else if (error) {
        console.error("Products query error:", error);
      }

      // Load categories
      const { data: categoriesData, error: categoriesError } = await supabase
        .from("product_categories")
        .select("id, name")
        .order("name");

      if (!categoriesError && categoriesData) {
        setAvailableCategories(categoriesData);
      }
    } catch (err) {
      console.error("Error loading products:", err);
    }
  };

  // Load selected form details
  useEffect(() => {
    if (selectedFormId) {
      const form = availableForms.find((f) => f.id === selectedFormId);
      setSelectedForm(form);

      // Initialize form responses
      if (form?.fields) {
        const initialResponses: Record<string, any> = {};
        form.fields.forEach((field: any) => {
          if (field.type === "checkbox") {
            initialResponses[field.name] = false;
          } else {
            initialResponses[field.name] = "";
          }
        });
        setFormResponses(initialResponses);
      }
    } else {
      setSelectedForm(null);
      setFormResponses({});
    }
  }, [selectedFormId, availableForms]);

  // Update form data when custom form responses change
  useEffect(() => {
    const formData = {
      templateId: selectedFormId || undefined,
      responses: formResponses,
    };
    form.setValue("customFormResponses", formData);
  }, [formResponses, selectedFormId, form]);

  // Update form data when stock items change
  useEffect(() => {
    form.setValue("stockItems", selectedProducts);
  }, [selectedProducts, form]);

  // Update form data when technician changes
  useEffect(() => {
    form.setValue("assignedTechnician", assignedTechnician);
  }, [assignedTechnician, form]);

  const addStockItem = (productId: string) => {
    const existing = selectedProducts.find((p) => p.id === productId);
    if (existing) {
      setSelectedProducts((prev) =>
        prev.map((p) =>
          p.id === productId ? { ...p, quantity: p.quantity + 1 } : p
        )
      );
    } else {
      setSelectedProducts((prev) => [...prev, { id: productId, quantity: 1 }]);
    }
  };

  const removeStockItem = (productId: string) => {
    setSelectedProducts((prev) => prev.filter((p) => p.id !== productId));
  };

  const updateStockQuantity = (productId: string, quantity: number) => {
    if (quantity <= 0) {
      removeStockItem(productId);
    } else {
      setSelectedProducts((prev) =>
        prev.map((p) => (p.id === productId ? { ...p, quantity } : p))
      );
    }
  };

  const renderCustomFormField = (field: any) => {
    const value = formResponses[field.name] || "";

    switch (field.type) {
      case "text":
      case "number":
        return (
          <Input
            type={field.type}
            placeholder={field.placeholder || field.name}
            value={value}
            onChange={(e) =>
              setFormResponses((prev) => ({
                ...prev,
                [field.name]:
                  field.type === "number"
                    ? Number(e.target.value)
                    : e.target.value,
              }))
            }
            required={field.required}
          />
        );

      case "textarea":
        return (
          <Textarea
            placeholder={field.placeholder || field.name}
            value={value}
            onChange={(e) =>
              setFormResponses((prev) => ({
                ...prev,
                [field.name]: e.target.value,
              }))
            }
            required={field.required}
            rows={3}
          />
        );

      case "checkbox":
        return (
          <div className="flex items-center space-x-2">
            <Checkbox
              checked={value === true}
              onCheckedChange={(checked) =>
                setFormResponses((prev) => ({
                  ...prev,
                  [field.name]: checked === true,
                }))
              }
            />
            <span className="text-sm">{field.name}</span>
          </div>
        );

      case "select":
        return (
          <Select
            value={value}
            onValueChange={(newValue) =>
              setFormResponses((prev) => ({
                ...prev,
                [field.name]: newValue,
              }))
            }
          >
            <SelectTrigger>
              <SelectValue placeholder={`Select ${field.name}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option: string) => (
                <SelectItem key={option} value={option}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        );

      case "radio":
        return (
          <RadioGroup
            value={value}
            onValueChange={(newValue) =>
              setFormResponses((prev) => ({
                ...prev,
                [field.name]: newValue,
              }))
            }
            className="flex flex-col space-y-2"
          >
            {field.options?.map((option: string) => (
              <div key={option} className="flex items-center space-x-2">
                <RadioGroupItem value={option} id={`${field.name}-${option}`} />
                <label
                  htmlFor={`${field.name}-${option}`}
                  className="text-sm cursor-pointer"
                >
                  {option}
                </label>
              </div>
            ))}
          </RadioGroup>
        );

      default:
        return null;
    }
  };

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        className="space-y-8"
        style={{ textAlign: isRTL ? "right" : "left" }}
      >
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center">
            <User className="h-5 w-5 mr-2 text-blue-500" />
            {t("repair.customerInfo")}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 bg-gray-50 dark:bg-gray-800/50 p-4 rounded-md">
            <FormField
              control={form.control}
              name="customerName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t("repair.customerName")}{" "}
                    <span className="text-red-500">*</span>
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <User className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Input
                        placeholder="John Doe"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="customerPhone"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("repair.customerPhone")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Phone className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Input
                        placeholder="******-456-7890"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* <FormField
              control={form.control}
              name="customerEmail"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>
                    {t("repair.customerEmail")}{" "}
                    <span className="text-gray-400 text-sm">
                      ({t("common.optional")})
                    </span>
                  </FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Mail className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            /> */}

            <FormField
              control={form.control}
              name="phoneModel"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("repair.phoneModel")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Smartphone className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Input
                        placeholder="iPhone 13 Pro"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="repairPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("repair.repairPrice")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <DollarSign className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Input
                        type="number"
                        placeholder="0.00"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="paymentStatus"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("repair.paymentStatus")}</FormLabel>
                  <Select
                    onValueChange={field.onChange}
                    defaultValue={field.value}
                  >
                    <FormControl>
                      <SelectTrigger className="pl-10">
                        <div className="absolute left-3 top-2.5 text-gray-400">
                          <CreditCard className="h-4 w-4" />
                        </div>
                        <SelectValue placeholder={t("repair.paymentStatus")} />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="paid">
                        {t("repair.payment.paid")}
                      </SelectItem>
                      <SelectItem value="partial">
                        {t("repair.payment.partial")}
                      </SelectItem>
                      <SelectItem value="unpaid">
                        {t("repair.payment.unpaid")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="downPayment"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("repair.downPayment")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Wallet className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Input
                        type="number"
                        placeholder="0.00"
                        className="pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center">
            <FileText className="h-5 w-5 mr-2 text-blue-500" />
            {t("repair.problemDescription")}
          </h2>
          <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-md">
            <FormField
              control={form.control}
              name="problemDescription"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>{t("repair.problemDescription")}</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <MessageSquare className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                      <Textarea
                        placeholder={t("repair.problemDescription") + "..."}
                        className="min-h-[120px] pl-10"
                        {...field}
                      />
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Observations Section */}
        <div className="mb-6">
          <h2 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center">
            <ClipboardList className="h-5 w-5 mr-2 text-blue-500" />
            {t("repair.observations")}{" "}
            <span className="text-sm font-normal text-gray-500 ml-2">
              {t("repair.observationsOptional")}
            </span>
          </h2>
          <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-md space-y-4">
            <div
              className="flex gap-2"
              style={{ flexDirection: isRTL ? "row-reverse" : "row" }}
            >
              <div className="relative flex-1">
                <ClipboardList className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                <Input
                  placeholder={t("repair.addObservation") + "..."}
                  value={newObservation}
                  onChange={(e) => setNewObservation(e.target.value)}
                  className="flex-1 pl-10"
                />
              </div>
              <Button
                type="button"
                variant="outline"
                size="icon"
                onClick={() => {
                  if (newObservation.trim()) {
                    const currentObservations =
                      form.getValues("observations") || [];
                    form.setValue("observations", [
                      ...currentObservations,
                      newObservation.trim(),
                    ]);
                    setNewObservation("");
                  }
                }}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>

            <div className="space-y-2">
              {form.watch("observations")?.map((observation, index) => (
                <div
                  key={index}
                  className="flex items-center justify-between bg-white dark:bg-gray-700 p-2 rounded-md"
                  style={{ flexDirection: isRTL ? "row-reverse" : "row" }}
                >
                  <span className="text-sm">{observation}</span>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => {
                      const currentObservations = [
                        ...(form.getValues("observations") || []),
                      ];
                      currentObservations.splice(index, 1);
                      form.setValue("observations", currentObservations);
                    }}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Custom Forms Section */}
        {availableForms.length > 0 && (
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center">
              <FileCheck className="h-5 w-5 mr-2 text-blue-500" />
              {t("customForms.title")}
              <span className="text-sm font-normal text-gray-500 ml-2">
                ({t("common.optional")})
              </span>
            </h2>
            <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-md space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  {t("customForms.selectCustomForm")}
                </label>
                <Select
                  value={selectedFormId || "none"}
                  onValueChange={(value) =>
                    setSelectedFormId(value === "none" ? null : value)
                  }
                >
                  <SelectTrigger>
                    <SelectValue
                      placeholder={t("customForms.selectCustomForm")}
                    />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="none">
                      {t("customForms.noCustomForm")}
                    </SelectItem>
                    {availableForms.map((form) => (
                      <SelectItem key={form.id} value={form.id}>
                        {form.name} ({form.device_type})
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedForm && (
                <div className="space-y-4 border-t pt-4">
                  <div>
                    <h3 className="font-medium">{selectedForm.name}</h3>
                    {selectedForm.description && (
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        {selectedForm.description}
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {selectedForm.fields
                      ?.sort((a: any, b: any) => a.order - b.order)
                      .map((field: any) => (
                        <div key={field.id} className="space-y-2">
                          <label className="block text-sm font-medium">
                            {field.name}
                            {field.required && (
                              <span className="text-red-500 ml-1">*</span>
                            )}
                          </label>
                          {renderCustomFormField(field)}
                        </div>
                      ))}
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Stock Items Section */}
        {availableProducts.length > 0 && (
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center">
              <Package className="h-5 w-5 mr-2 text-blue-500" />
              Stock Items
              <span className="text-sm font-normal text-gray-500 ml-2">
                ({t("common.optional")})
              </span>
            </h2>
            <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-md space-y-4">
              <div className="space-y-3">
                <label className="block text-sm font-medium">
                  Add Stock Items
                </label>

                <div className="flex gap-2">
                  <Input
                    placeholder="Search products..."
                    value={productSearch}
                    onChange={(e) => setProductSearch(e.target.value)}
                    className="flex-1"
                  />
                  <Select
                    value={selectedCategory}
                    onValueChange={setSelectedCategory}
                  >
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="All categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All categories</SelectItem>
                      {availableCategories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <Select onValueChange={addStockItem}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a product to add" />
                  </SelectTrigger>
                  <SelectContent>
                    {availableProducts
                      .filter((product) => {
                        const matchesSearch =
                          productSearch === "" ||
                          product.name
                            .toLowerCase()
                            .includes(productSearch.toLowerCase()) ||
                          product.sku
                            .toLowerCase()
                            .includes(productSearch.toLowerCase());
                        const matchesCategory =
                          selectedCategory === "" ||
                          selectedCategory === "all" ||
                          product.category_id === selectedCategory;
                        return matchesSearch && matchesCategory;
                      })
                      .map((product) => (
                        <SelectItem key={product.id} value={product.id}>
                          {product.name} ({product.sku}) -{" "}
                          {product.price.toFixed(3)} TND (Stock:{" "}
                          {product.stock_quantity})
                          {!product.is_active && (
                            <span className="text-xs text-orange-600 ml-2">
                              [Repair Only]
                            </span>
                          )}
                        </SelectItem>
                      ))}
                  </SelectContent>
                </Select>
              </div>

              {selectedProducts.length > 0 && (
                <div className="space-y-2 border-t pt-4">
                  <h3 className="font-medium">Selected Items:</h3>
                  {selectedProducts.map((item) => {
                    const product = availableProducts.find(
                      (p) => p.id === item.id
                    );
                    return (
                      <div
                        key={item.id}
                        className="flex items-center justify-between bg-white dark:bg-gray-700 p-3 rounded-lg"
                      >
                        <div className="flex-1">
                          <span className="font-medium">{product?.name}</span>
                          <span className="text-sm text-gray-600 dark:text-gray-400 ml-2">
                            {product?.price.toFixed(3)} TND each
                          </span>
                        </div>
                        <div className="flex items-center gap-2">
                          <Input
                            type="number"
                            min="1"
                            max={product?.stock_quantity}
                            value={item.quantity}
                            onChange={(e) =>
                              updateStockQuantity(
                                item.id,
                                parseInt(e.target.value) || 0
                              )
                            }
                            className="w-16 h-8"
                          />
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeStockItem(item.id)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                  <div className="text-right pt-2 border-t">
                    <span className="font-medium">
                      Total:{" "}
                      {selectedProducts
                        .reduce((total, item) => {
                          const product = availableProducts.find(
                            (p) => p.id === item.id
                          );
                          return total + (product?.price || 0) * item.quantity;
                        }, 0)
                        .toFixed(3)}{" "}
                      TND
                    </span>
                  </div>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Technician Assignment Section */}
        {availableTechnicians.length > 0 && (
          <div className="mb-6">
            <h2 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-4 flex items-center">
              <User className="h-5 w-5 mr-2 text-green-500" />
              {t("assignTechnician.assignTechnician")}
              <span className="text-sm font-normal text-gray-500 ml-2">
                ({t("common.optional")})
              </span>
            </h2>
            <div className="bg-gray-50 dark:bg-gray-800/50 p-4 rounded-md">
              <label className="block text-sm font-medium mb-2">
                {t("assignTechnician.selectTechnician")}
              </label>
              <Select
                value={assignedTechnician || "none"}
                onValueChange={(value) =>
                  setAssignedTechnician(value === "none" ? "" : value)
                }
              >
                <SelectTrigger>
                  <SelectValue
                    placeholder={t("assignTechnician.chooseTechnician")}
                  />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="none">
                    {t("assignTechnician.noAssignment")}
                  </SelectItem>
                  {availableTechnicians.map((tech) => (
                    <SelectItem key={tech.user_id} value={tech.user_id}>
                      {tech.email} ({tech.role})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        )}

        <Button
          type="submit"
          className={`w-full py-2 rounded-md transition-colors flex items-center justify-center gap-2 ${
            isPremium 
              ? 'premium-button premium-glow' 
              : 'bg-blue-600 hover:bg-blue-700 text-white'
          }`}
          disabled={isLoading}
        >
          {isLoading ? (
            <>
              <Loader2 className="h-4 w-4 animate-spin" />
              {t("common.processing")}
            </>
          ) : (
            <>
              <FileText className="h-4 w-4" />
              {t("repair.submitRepair")}
            </>
          )}
        </Button>
      </form>
    </Form>
  );
};

interface RepairFormProps {
  initialData?: RepairFormData;
  onSubmit: (data: RepairFormData) => void;
  isLoading?: boolean;
  isRTL?: boolean;
}

export default RepairForm;
