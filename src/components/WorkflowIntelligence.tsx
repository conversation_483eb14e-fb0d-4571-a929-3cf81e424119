import React, { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { useRepairShopContext } from "@/context/RepairShopContext";
import jsPDF from 'jspdf';
import 'jspdf-autotable';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import { Clock, BarChart3, Download, TrendingUp, Target, Lightbulb, Users } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axi<PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Responsive<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  Line,
} from "recharts";

export default function WorkflowIntelligence() {
  const { user, hasPermission } = useAuth();
  const { repairShop } = useRepairShopContext();
  const [activeTab, setActiveTab] = useState("overview");
  const [timeEntries, setTimeEntries] = useState<any[]>([]);
  const [technicians, setTechnicians] = useState<any[]>([]);
  const [technicianStats, setTechnicianStats] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [dateFilter, setDateFilter] = useState("today");
  const [selectedTechnician, setSelectedTechnician] = useState<string>("all");
  const [exportLoading, setExportLoading] = useState(false);

  const canTrackTime = hasPermission("track_time");
  const canViewReports = hasPermission("view_reports");

  useEffect(() => {
    if (!canTrackTime && !canViewReports) return;
    if (!repairShop) return;

    const loadData = async () => {
      await fetchTechnicians();
      await fetchTimeEntries();
    };

    loadData();
  }, [canTrackTime, canViewReports, repairShop]);

  useEffect(() => {
    if (technicians.length > 0) {
      fetchTechnicianStats();
    }
  }, [technicians, timeEntries]);

  const fetchTimeEntries = async () => {
    if (!repairShop) return;

    try {
      const { data, error } = await supabase.rpc(
        "get_technician_time_tracking",
        {
          shop_id: repairShop.id,
        }
      );

      if (!error && data) {
        setTimeEntries(data);
      }
    } catch (error) {
      console.error("Error fetching time entries:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchTechnicians = async () => {
    if (!repairShop) return;

    try {
      const { data, error } = await supabase.rpc("get_shop_users", {
        shop_id: repairShop.id,
      });

      if (!error && data) {
        const techs = data.filter(
          (u) => u.role === "technician" || u.role === "administrator"
        );
        setTechnicians(techs);
      }
    } catch (error) {
      console.error("Error fetching technicians:", error);
    }
  };

  const fetchTechnicianStats = async () => {
    if (!repairShop) return;

    try {
      const { data, error } = await supabase.rpc(
        "get_technician_time_tracking",
        {
          shop_id: repairShop.id,
        }
      );

      if (!error && data) {
        const stats = data.reduce((acc: any, entry) => {
          const techId = entry.technician_id;
          if (!acc[techId]) {
            acc[techId] = { totalMinutes: 0, entries: 0 };
          }
          acc[techId].totalMinutes += entry.duration_minutes || 0;
          acc[techId].entries += 1;
          return acc;
        }, {});

        const chartData = Object.entries(stats).map(
          ([techId, stat]: [string, any]) => {
            return {
              name: "Team Member",
              hours: Math.round((stat.totalMinutes / 60) * 10) / 10,
              entries: stat.entries,
            };
          }
        );

        setTechnicianStats(chartData);
      }
    } catch (error) {
      console.error("Error fetching technician stats:", error);
    }
  };

  const getFilteredEntries = () => {
    let filtered = timeEntries;

    if (selectedTechnician !== "all") {
      filtered = filtered.filter(
        (entry) => entry.technician_id === selectedTechnician
      );
    }

    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const weekAgo = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);
    const monthAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);

    switch (dateFilter) {
      case "today":
        filtered = filtered.filter(
          (entry) => new Date(entry.started_at) >= today
        );
        break;
      case "yesterday":
        filtered = filtered.filter((entry) => {
          const entryDate = new Date(entry.started_at);
          return entryDate >= yesterday && entryDate < today;
        });
        break;
      case "week":
        filtered = filtered.filter(
          (entry) => new Date(entry.started_at) >= weekAgo
        );
        break;
      case "month":
        filtered = filtered.filter(
          (entry) => new Date(entry.started_at) >= monthAgo
        );
        break;
    }

    return filtered;
  };

  const exportToPDF = async () => {
    setExportLoading(true);
    try {
      const filteredData = getFilteredEntries();
      const totalMinutes = filteredData.reduce(
        (sum, entry) => sum + (entry.duration_minutes || 0),
        0
      );
      const avgMinutes = filteredData.length > 0 ? totalMinutes / filteredData.length : 0;
      const completedEntries = filteredData.filter(
        (entry) => entry.status_to === "completed"
      );
      const inProgressEntries = filteredData.filter(
        (entry) => entry.status_to === "in_progress"
      );
      const otherEntries = filteredData.length - completedEntries.length - inProgressEntries.length;
      
      const currentDate = new Date();
      const reportData = {
        totalHours: Math.round((totalMinutes / 60) * 100) / 100,
        totalEntries: filteredData.length,
        avgHours: Math.round((avgMinutes / 60) * 100) / 100,
        completedRepairs: completedEntries.length,
        successRate: Math.round((completedEntries.length / filteredData.length) * 100) || 0,
      };
      
      const doc = new jsPDF();
      
      // Header
      doc.setFontSize(20);
      doc.text('Workflow Intelligence Report', 105, 20, { align: 'center' });
      doc.setFontSize(12);
      doc.text(`Period: ${dateFilter.toUpperCase()} | Shop: ${repairShop?.name || 'N/A'}`, 105, 30, { align: 'center' });
      doc.text(`Generated: ${currentDate.toLocaleDateString()} ${currentDate.toLocaleTimeString()}`, 105, 40, { align: 'center' });
      
      // Metrics
      doc.setFontSize(14);
      doc.text('Key Metrics', 20, 60);
      doc.setFontSize(10);
      doc.text(`Total Hours: ${reportData.totalHours}h`, 20, 70);
      doc.text(`Total Processes: ${reportData.totalEntries}`, 20, 80);
      doc.text(`Avg Hours/Process: ${reportData.avgHours}h`, 20, 90);
      doc.text(`Completed: ${reportData.completedRepairs}`, 20, 100);
      doc.text(`Success Rate: ${reportData.successRate}%`, 20, 110);
      
      // Activity Details Table
      const activityData = filteredData.slice(0, 15).map(entry => [
        `#${entry.repair_id || 'N/A'}`,
        entry.status_to || 'Unknown',
        `${Math.floor((entry.duration_minutes || 0) / 60)}h ${Math.round((entry.duration_minutes || 0) % 60)}m`,
        entry.started_at ? new Date(entry.started_at).toLocaleDateString() : 'N/A',
        entry.ended_at ? new Date(entry.ended_at).toLocaleDateString() : 'In Progress'
      ]);
      
      (doc as any).autoTable({
        head: [['Repair ID', 'Status', 'Duration', 'Started', 'Completed']],
        body: activityData,
        startY: 120,
        theme: 'grid',
        headStyles: { fillColor: [102, 126, 234] },
      });
      
      // Summary Statistics Table
      const summaryData = [
        ['Completed Repairs', completedEntries.length.toString(), `${Math.round((completedEntries.length / filteredData.length) * 100) || 0}%`],
        ['In Progress', inProgressEntries.length.toString(), `${Math.round((inProgressEntries.length / filteredData.length) * 100) || 0}%`],
        ['Other Status', otherEntries.toString(), `${Math.round((otherEntries / filteredData.length) * 100) || 0}%`]
      ];
      
      (doc as any).autoTable({
        head: [['Metric', 'Value', 'Percentage']],
        body: summaryData,
        startY: (doc as any).lastAutoTable.finalY + 20,
        theme: 'grid',
        headStyles: { fillColor: [102, 126, 234] },
      });
      
      doc.save(`workflow-report-${dateFilter}-${currentDate.toISOString().split('T')[0]}.pdf`);
      toast.success('PDF report generated successfully');
    } catch (error) {
      console.error('Error generating PDF:', error);
      toast.error('Failed to generate PDF report');
    } finally {
      setExportLoading(false);
    }
  };

  if (!canTrackTime && !canViewReports) {
    return (
      <div className="flex items-center justify-center h-screen">
        Access denied. You need workflow intelligence privileges.
      </div>
    );
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  const filteredEntries = getFilteredEntries();
  const totalMinutes = filteredEntries.reduce(
    (sum, entry) => sum + (entry.duration_minutes || 0),
    0
  );
  const avgMinutes =
    filteredEntries.length > 0 ? totalMinutes / filteredEntries.length : 0;
  const completedEntries = filteredEntries.filter(
    (entry) => entry.status_to === "completed"
  );

  return (
    <div className="container mx-auto py-6">
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-3xl font-bold flex items-center">
            <TrendingUp className="h-8 w-8 mr-3 text-blue-500" />
            Workflow Intelligence
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-2">
            Optimize processes, improve efficiency, and enhance customer service
          </p>
        </div>
        <Button 
          onClick={exportToPDF} 
          disabled={exportLoading}
          className="flex items-center gap-2"
        >
          {exportLoading ? (
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
          ) : (
            <Download className="h-4 w-4" />
          )}
          Export Intelligence Report
        </Button>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="overview">Process Overview</TabsTrigger>
          <TabsTrigger value="analytics">Business Insights</TabsTrigger>
          <TabsTrigger value="improvements">Improvement Opportunities</TabsTrigger>
        </TabsList>

        <TabsContent value="overview">
          <div className="flex flex-wrap gap-4 mb-6 p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
            <div className="flex items-center gap-2">
              <Label className="text-blue-700 dark:text-blue-300">Analysis Period:</Label>
              <Select value={dateFilter} onValueChange={setDateFilter}>
                <SelectTrigger className="w-32">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="today">Today</SelectItem>
                  <SelectItem value="yesterday">Yesterday</SelectItem>
                  <SelectItem value="week">This Week</SelectItem>
                  <SelectItem value="month">This Month</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center gap-2 text-sm text-blue-600 dark:text-blue-400">
              <Lightbulb className="h-4 w-4" />
              <span>Analyzing workflow patterns to optimize operations</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <Card className="bg-gradient-to-r from-blue-500 to-blue-600 text-white">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <Clock className="h-4 w-4 mr-2" />
                  Workshop Capacity
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.floor(totalMinutes / 60)}h {Math.round(totalMinutes % 60)}m
                </div>
                <p className="text-xs opacity-80">Total productive time</p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-green-500 to-green-600 text-white">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <Target className="h-4 w-4 mr-2" />
                  Process Completion
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{completedEntries.length}</div>
                <p className="text-xs opacity-80">Successful outcomes</p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-purple-500 to-purple-600 text-white">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <Users className="h-4 w-4 mr-2" />
                  Active Workflows
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {filteredEntries.filter((entry) => !entry.ended_at).length}
                </div>
                <p className="text-xs opacity-80">Currently in progress</p>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-r from-orange-500 to-orange-600 text-white">
              <CardHeader className="pb-2">
                <CardTitle className="flex items-center text-sm font-medium">
                  <TrendingUp className="h-4 w-4 mr-2" />
                  Process Efficiency
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {Math.floor(avgMinutes / 60)}h {Math.round(avgMinutes % 60)}m
                </div>
                <p className="text-xs opacity-80">Average cycle time</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="analytics">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Business Performance Metrics
              </CardTitle>
              <CardDescription>
                Key indicators for operational excellence and customer satisfaction
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 border rounded bg-blue-50 dark:bg-blue-900/20">
                  <div className="text-2xl font-bold text-blue-600">
                    {Math.round((totalMinutes / filteredEntries.length) * 10) / 10 || 0}min
                  </div>
                  <div className="text-sm text-gray-600">Average Service Time</div>
                  <div className="text-xs text-gray-500 mt-1">Industry benchmark: 45-90min</div>
                </div>
                <div className="text-center p-4 border rounded bg-green-50 dark:bg-green-900/20">
                  <div className="text-2xl font-bold text-green-600">
                    {Math.round((completedEntries.length / filteredEntries.length) * 100) || 0}%
                  </div>
                  <div className="text-sm text-gray-600">Process Success Rate</div>
                  <div className="text-xs text-gray-500 mt-1">Target: >85%</div>
                </div>
                <div className="text-center p-4 border rounded bg-purple-50 dark:bg-purple-900/20">
                  <div className="text-2xl font-bold text-purple-600">
                    {Math.round((completedEntries.length / (totalMinutes / 60)) * 10) / 10 || 0}
                  </div>
                  <div className="text-sm text-gray-600">Throughput (repairs/hour)</div>
                  <div className="text-xs text-gray-500 mt-1">Workshop capacity indicator</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="improvements">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Lightbulb className="h-5 w-5 mr-2 text-yellow-500" />
                Process Improvement Opportunities
              </CardTitle>
              <CardDescription>
                Data-driven insights to enhance workshop efficiency and customer satisfaction
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <h3 className="font-semibold text-green-700 dark:text-green-400">Strengths Identified</h3>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm">High completion rate indicates good process control</span>
                    </div>
                    <div className="flex items-center gap-2 p-3 bg-green-50 dark:bg-green-900/20 rounded">
                      <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                      <span className="text-sm">Consistent service times show standardized procedures</span>
                    </div>
                  </div>
                </div>
                
                <div className="space-y-4">
                  <h3 className="font-semibold text-blue-700 dark:text-blue-400">Optimization Areas</h3>
                  <div className="space-y-2">
                    <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-sm">Consider workflow automation for routine tasks</span>
                    </div>
                    <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 rounded">
                      <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                      <span className="text-sm">Analyze peak hours for better resource planning</span>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}