import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { supabase } from "@/integrations/supabase/client";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { Invoice } from "@/types";
import { toast } from "sonner";
import { FileText, Printer, Eye } from "lucide-react";
import { format } from "date-fns";

interface InvoiceListProps {
  onViewInvoice: (invoice: Invoice) => void;
}

const InvoiceList: React.FC<InvoiceListProps> = ({ onViewInvoice }) => {
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState(true);
  const { repairShop } = useRepairShopContext();

  useEffect(() => {
    fetchInvoices();
  }, []);

  const fetchInvoices = async () => {
    if (!repairShop) return;

    setLoading(true);
    try {
      const { data: invoicesData, error: invoicesError } = await supabase
        .from("invoices")
        .select(`
          *,
          invoice_items (
            *,
            repairs (*)
          )
        `)
        .eq("repair_shop_id", repairShop.id)
        .order("created_at", { ascending: false });

      if (invoicesError) throw invoicesError;

      const formattedInvoices: Invoice[] = invoicesData.map(invoice => ({
        id: invoice.id,
        invoiceNumber: invoice.invoice_number,
        repairShopId: invoice.repair_shop_id,
        customerName: invoice.customer_name,
        customerPhone: invoice.customer_phone,
        subtotal: invoice.subtotal,
        discountAmount: invoice.discount_amount,
        totalAmount: invoice.total_amount,
        status: invoice.status,
        notes: invoice.notes,
        userId: invoice.user_id,
        createdAt: new Date(invoice.created_at),
        updatedAt: new Date(invoice.updated_at),
        items: invoice.invoice_items.map((item: any) => ({
          id: item.id,
          invoiceId: item.invoice_id,
          repairId: item.repair_id,
          description: item.description,
          amount: item.amount,
          repair: item.repairs ? {
            id: item.repairs.id,
            customerName: item.repairs.customer_name,
            customerPhone: item.repairs.customer_phone,
            phoneModel: item.repairs.phone_model,
            problemDescription: item.repairs.problem_description,
            repairPrice: item.repairs.repair_price,
            paymentStatus: item.repairs.payment_status,
            downPayment: item.repairs.down_payment,
            createdAt: new Date(item.repairs.created_at),
            completedAt: item.repairs.completed_at ? new Date(item.repairs.completed_at) : undefined,
            status: item.repairs.status,
            userId: item.repairs.user_id,
            repairShopId: item.repairs.repair_shop_id,
          } : undefined,
        })),
      }));

      setInvoices(formattedInvoices);
    } catch (error) {
      console.error("Error fetching invoices:", error);
      toast.error("Failed to fetch invoices");
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "draft": return "secondary";
      case "sent": return "default";
      case "paid": return "success";
      default: return "secondary";
    }
  };

  if (loading) {
    return <div>Loading invoices...</div>;
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <FileText className="h-5 w-5" />
          Invoices
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {invoices.length === 0 ? (
            <p className="text-muted-foreground text-center py-8">No invoices found</p>
          ) : (
            invoices.map((invoice) => (
              <div key={invoice.id} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div>
                    <h3 className="font-semibold">{invoice.invoiceNumber}</h3>
                    <p className="text-sm text-muted-foreground">{invoice.customerName}</p>
                  </div>
                  <Badge variant={getStatusColor(invoice.status)}>
                    {invoice.status}
                  </Badge>
                </div>
                
                <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                  <div>
                    <span className="text-muted-foreground">Date: </span>
                    {format(invoice.createdAt, "MMM dd, yyyy")}
                  </div>
                  <div>
                    <span className="text-muted-foreground">Total: </span>
                    <span className="font-medium">{invoice.totalAmount.toFixed(2)} TND</span>
                  </div>
                  <div>
                    <span className="text-muted-foreground">Items: </span>
                    {invoice.items.length}
                  </div>
                  {invoice.discountAmount > 0 && (
                    <div>
                      <span className="text-muted-foreground">Discount: </span>
                      {invoice.discountAmount.toFixed(2)} TND
                    </div>
                  )}
                </div>

                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onViewInvoice(invoice)}
                  >
                    <Eye className="h-4 w-4 mr-1" />
                    View
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => onViewInvoice(invoice)}
                  >
                    <Printer className="h-4 w-4 mr-1" />
                    Print
                  </Button>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default InvoiceList;