import React, { useRef } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useRepairContext } from "@/context/RepairContext";
import { exportToCSV, importFromCSV } from "@/utils/csvUtils";
import { toast } from "sonner";
import { Download, Upload } from "lucide-react";
import { useTranslation } from "react-i18next";

const ImportExportData: React.FC = () => {
  const { repairs, importRepairs } = useRepairContext();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const { t } = useTranslation();

  const handleExport = () => {
    exportToCSV(repairs);
  };

  const handleImportClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    try {
      const importedRepairs = await importFromCSV(file);
      importRepairs(importedRepairs);
      toast.success(`Successfully imported ${importedRepairs.length} repairs`);

      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    } catch (error) {
      console.error("Import error:", error);
      toast.error("Failed to import data. Please check the file format.");
    }
  };

  return (
    <div className="flex flex-col sm:flex-row gap-4">
      <Button
        variant="outline"
        onClick={handleExport}
        className="flex items-center gap-2"
      >
        <Download className="h-4 w-4" />
        {t("data.export")}
      </Button>

      <Button
        variant="outline"
        onClick={handleImportClick}
        className="flex items-center gap-2"
      >
        <Upload className="h-4 w-4" />
        {t("data.import")}
      </Button>

      <Input
        ref={fileInputRef}
        type="file"
        accept=".csv"
        onChange={handleFileChange}
        className="hidden"
      />
    </div>
  );
};

export default ImportExportData;
