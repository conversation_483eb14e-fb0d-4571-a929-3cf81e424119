import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useRepairContext } from "@/context/RepairContext";
import { Calendar, Download, RefreshCw, Database } from "lucide-react";
import { toast } from "sonner";

export const RepairDataControls: React.FC = () => {
  const {
    repairs,
    loading,
    hasMore,
    totalCount,
    dateFilter,
    setDateFilter,
    loadMoreRepairs,
    loadAllRepairs,
    refreshRepairs,
  } = useRepairContext();

  const [startDate, setStartDate] = useState(dateFilter?.startDate || "");
  const [endDate, setEndDate] = useState(dateFilter?.endDate || "");

  const handleApplyDateFilter = () => {
    if (startDate || endDate) {
      setDateFilter({
        startDate: startDate || undefined,
        endDate: endDate || undefined,
      });
      toast.success(`Date filter applied: ${startDate || "All"} to ${endDate || "All"}`);
    } else {
      setDateFilter(null);
      toast.success("Date filter cleared");
    }
  };

  const handleClearDateFilter = () => {
    setStartDate("");
    setEndDate("");
    setDateFilter(null);
    toast.success("Date filter cleared");
  };

  const handleLoadAllRepairs = async () => {
    if (window.confirm("This will load ALL repairs from the database. This might take a while for large datasets. Continue?")) {
      await loadAllRepairs();
      toast.success("All repairs loaded successfully!");
    }
  };

  return (
    <Card className="mb-6">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Database className="h-5 w-5" />
          Repair Data Controls
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Date Filter Section */}
          <div className="space-y-2">
            <Label htmlFor="start-date" className="flex items-center gap-1">
              <Calendar className="h-4 w-4" />
              Start Date
            </Label>
            <Input
              id="start-date"
              type="date"
              value={startDate}
              onChange={(e) => setStartDate(e.target.value)}
              placeholder="Start date"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="end-date">End Date</Label>
            <Input
              id="end-date"
              type="date"
              value={endDate}
              onChange={(e) => setEndDate(e.target.value)}
              placeholder="End date"
            />
          </div>

          {/* Action Buttons */}
          <div className="space-y-2">
            <Label>Date Filter Actions</Label>
            <div className="flex gap-2">
              <Button
                onClick={handleApplyDateFilter}
                size="sm"
                className="flex-1"
              >
                Apply
              </Button>
              <Button
                onClick={handleClearDateFilter}
                variant="outline"
                size="sm"
                className="flex-1"
              >
                Clear
              </Button>
            </div>
          </div>

          {/* Data Actions */}
          <div className="space-y-2">
            <Label>Data Actions</Label>
            <div className="flex flex-col gap-2">
              <Button
                onClick={refreshRepairs}
                variant="outline"
                size="sm"
                disabled={loading}
                className="flex items-center gap-1"
              >
                <RefreshCw className={`h-4 w-4 ${loading ? "animate-spin" : ""}`} />
                Refresh
              </Button>
              
              {hasMore && (
                <Button
                  onClick={loadMoreRepairs}
                  variant="outline"
                  size="sm"
                  disabled={loading}
                  className="flex items-center gap-1"
                >
                  <Download className="h-4 w-4" />
                  Load More
                </Button>
              )}

              <Button
                onClick={handleLoadAllRepairs}
                variant="outline"
                size="sm"
                disabled={loading}
                className="flex items-center gap-1"
              >
                <Database className="h-4 w-4" />
                Load All
              </Button>
            </div>
          </div>
        </div>

        {/* Status Information */}
        <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium">Loaded:</span> {repairs.length}
            </div>
            <div>
              <span className="font-medium">Total:</span> {totalCount}
            </div>
            <div>
              <span className="font-medium">Has More:</span> {hasMore ? "Yes" : "No"}
            </div>
            <div>
              <span className="font-medium">Status:</span> {loading ? "Loading..." : "Ready"}
            </div>
          </div>
          
          {dateFilter && (
            <div className="mt-2 text-sm text-blue-600 dark:text-blue-400">
              <span className="font-medium">Active Filter:</span> {" "}
              {dateFilter.startDate || "All"} to {dateFilter.endDate || "All"}
            </div>
          )}
        </div>

        {/* Instructions */}
        <div className="mt-4 text-sm text-gray-600 dark:text-gray-400">
          <p><strong>Pagination:</strong> By default, repairs are loaded in pages of 100. Use "Load More" to get additional pages.</p>
          <p><strong>Date Filter:</strong> Filter repairs by creation date range. Leave empty for no date restriction.</p>
          <p><strong>Load All:</strong> Loads all repairs matching the current filter (use carefully with large datasets).</p>
        </div>
      </CardContent>
    </Card>
  );
};
