import React from "react";
import { Link, useLocation } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useAuth } from "@/context/AuthContext";
import { Button } from "@/components/ui/button";
import {
  Home,
  Plus,
  Search,
  Package,
  Users,
  Clock,
  Boxes,
  FormInput,
  Crown,
  ChevronLeft,
  ChevronRight,
  FileText,
  Building2,
} from "lucide-react";
import Logo from "./Logo";

interface PremiumSidebarProps {
  collapsed: boolean;
  onToggle: () => void;
}

const PremiumSidebar: React.FC<PremiumSidebarProps> = ({
  collapsed,
  onToggle,
}) => {
  const { t } = useTranslation();
  const { hasPermission } = useAuth();
  const location = useLocation();

  const isActive = (path: string) => {
    if (path === "/") return location.pathname === "/";
    if (path === "/repair") return location.pathname.startsWith("/repair/");
    return location.pathname === path;
  };

  const navItems = [
    { path: "/", icon: Home, label: t("common.dashboard") },
    { path: "/new-repair", icon: Plus, label: t("common.newRepair") },
    { path: "/search", icon: Search, label: t("common.search") },
    { path: "/stock", icon: Package, label: t("common.stock") },
    { path: "/invoices", icon: FileText, label: "Invoices" },
  ];

  const premiumItems = [
    {
      path: "/users",
      icon: Users,
      label: t("userManagement.title"),
      permission: "manage_users",
    },
    {
      path: "/branches",
      icon: Building2,
      label: "Store Branches",
      permission: "manage_settings",
    },
    {
      path: "/time-tracking",
      icon: Clock,
      label: t("performanceMonitor.title"),
      permission: "track_time",
    },
    {
      path: "/advanced-stock",
      icon: Boxes,
      label: t("advancedStock.title"),
      permission: "manage_stock",
    },
    {
      path: "/custom-forms",
      icon: FormInput,
      label: t("customForms.title"),
      permission: "manage_settings",
    },
  ];

  return (
    <div
      className={`premium-sidebar bg-card border-r border-border transition-all duration-300 ${
        collapsed ? "w-16" : "w-64"
      } h-screen flex flex-col`}
    >
      {/* Header */}
      <div className="px-3 py-4 border-b border-border flex items-center justify-between">
        {!collapsed && (
          <div className="flex items-center gap-2">
            <Logo size={24} className="h-6 w-6" />
            <span className="font-bold text-lg">{t("app.title")}</span>
          </div>
        )}
        <Button
          variant="ghost"
          size="icon"
          onClick={onToggle}
          className="h-8 w-8"
        >
          {collapsed ? (
            <ChevronRight className="h-4 w-4" />
          ) : (
            <ChevronLeft className="h-4 w-4" />
          )}
        </Button>
      </div>

      {/* Navigation */}
      <nav className="flex-1 px-3 py-4 space-y-2">
        {/* Main Navigation */}
        <div>
          {navItems.map((item) => {
            const Icon = item.icon;
            return (
              <div key={item.path} className="mb-3">
                <Link to={item.path}>
                  <Button
                    variant={isActive(item.path) ? "default" : "ghost"}
                    className={`w-full justify-start premium-button h-9 text-sm ${
                      collapsed ? "px-2" : "px-4"
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    {!collapsed && (
                      <span className="ml-2 truncate">{item.label}</span>
                    )}
                  </Button>
                </Link>
              </div>
            );
          })}
        </div>

        {/* Advanced Features */}
        {!collapsed && (
          <div className="pt-3 mt-3 border-t border-border">
            <div className="flex items-center gap-2 px-3 py-1 text-xs font-medium text-muted-foreground uppercase tracking-wide">
              Advanced Features
            </div>
          </div>
        )}

        <div>
          {premiumItems.map((item) => {
            if (!hasPermission(item.permission)) return null;
            const Icon = item.icon;
            return (
              <div key={item.path} className="mb-3">
                <Link to={item.path}>
                  <Button
                    variant={isActive(item.path) ? "default" : "ghost"}
                    className={`w-full justify-start premium-button h-9 text-sm ${
                      collapsed ? "px-2" : "px-4"
                    }`}
                  >
                    <Icon className="h-4 w-4" />
                    {!collapsed && (
                      <span className="ml-2 truncate">{item.label}</span>
                    )}
                  </Button>
                </Link>
              </div>
            );
          })}
        </div>
      </nav>
    </div>
  );
};

export default PremiumSidebar;
