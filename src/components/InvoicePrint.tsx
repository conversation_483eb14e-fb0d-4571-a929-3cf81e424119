import React from "react";
import { Invoice } from "@/types";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { format } from "date-fns";

interface InvoicePrintProps {
  invoice: Invoice;
}

const InvoicePrint: React.FC<InvoicePrintProps> = ({ invoice }) => {
  const { repairShop } = useRepairShopContext();

  const printStyles = `
    @media print {
      @page { size: A4 landscape; margin: 5mm; }
      .invoice-print { 
        display: flex !important;
        gap: 10px !important;
        margin: 0 !important;
        padding: 5px !important;
        font-size: 12px !important;
        color: black !important;
        background: white !important;
      }
      .invoice-copy { flex: 1; }
      .no-print { display: none !important; }
    }
  `;

  return (
    <>
      <style>{printStyles}</style>
      <div className="invoice-print bg-white dark:bg-gray-900 p-8 text-black dark:text-white">
        {/* First Invoice */}
        <div className="invoice-copy text-black dark:text-white">
          {/* Header */}
          <div className="text-center mb-4">
            <h1 className="text-2xl font-bold">{repairShop?.name}</h1>
            {repairShop?.address && (
              <p className="text-sm">{repairShop.address}</p>
            )}
            {repairShop?.phone && (
              <p className="text-sm">Phone: {repairShop.phone}</p>
            )}
          </div>

          {/* Invoice Info */}
          <div className="grid grid-cols-2 gap-4 mb-3">
            <div>
              <h2 className="text-xl font-semibold mb-2">INVOICE</h2>
              <p>
                <strong>Invoice #:</strong> {invoice.invoiceNumber}
              </p>
              <p>
                <strong>Date:</strong>{" "}
                {format(invoice.createdAt, "MMM dd, yyyy")}
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Bill To:</h3>
              <p>{invoice.customerName}</p>
              {invoice.customerPhone && <p>{invoice.customerPhone}</p>}
            </div>
          </div>

          {/* Items */}
          <div className="mb-3">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 p-2 text-left">
                    Description
                  </th>
                  <th className="border border-gray-300 p-2 text-right">
                    Amount
                  </th>
                </tr>
              </thead>
              <tbody>
                {invoice.items.map((item) => (
                  <tr key={item.id}>
                    <td className="border border-gray-300 p-2">
                      {item.description}
                    </td>
                    <td className="border border-gray-300 p-2 text-right">
                      {item.amount.toFixed(2)} TND
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Totals */}
          <div className="flex justify-end mb-3">
            <div className="w-64">
              <div className="flex justify-between py-1">
                <span>Subtotal:</span>
                <span>{invoice.subtotal.toFixed(2)} TND</span>
              </div>
              {invoice.discountAmount > 0 && (
                <div className="flex justify-between py-1">
                  <span>Discount:</span>
                  <span>-{invoice.discountAmount.toFixed(2)} TND</span>
                </div>
              )}
              <div className="flex justify-between py-2 border-t font-bold">
                <span>Total:</span>
                <span>{invoice.totalAmount.toFixed(2)} TND</span>
              </div>
            </div>
          </div>

          {/* Notes */}
          {invoice.notes && (
            <div className="mb-3">
              <h3 className="font-semibold mb-2">Notes:</h3>
              <p className="text-sm">{invoice.notes}</p>
            </div>
          )}

          {/* Footer */}
          <div className="text-center text-sm text-gray-600 mt-2">
            <p>Thank you for your business!</p>
          </div>
        </div>

        {/* Second Copy */}
        <div className="invoice-copy border-l-2 border-dashed border-gray-400 dark:border-gray-600 pl-4 text-black dark:text-white">
          <div className="text-center mb-4">
            <h1 className="text-2xl font-bold">{repairShop?.name}</h1>
            {repairShop?.address && (
              <p className="text-sm">{repairShop.address}</p>
            )}
            {repairShop?.phone && (
              <p className="text-sm">Phone: {repairShop.phone}</p>
            )}
          </div>

          <div className="grid grid-cols-2 gap-4 mb-3">
            <div>
              <h2 className="text-xl font-semibold mb-2">INVOICE</h2>
              <p>
                <strong>Invoice #:</strong> {invoice.invoiceNumber}
              </p>
              <p>
                <strong>Date:</strong>{" "}
                {format(invoice.createdAt, "MMM dd, yyyy")}
              </p>
            </div>
            <div>
              <h3 className="font-semibold mb-2">Bill To:</h3>
              <p>{invoice.customerName}</p>
              {invoice.customerPhone && <p>{invoice.customerPhone}</p>}
            </div>
          </div>

          <div className="mb-3">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 p-2 text-left">
                    Description
                  </th>
                  <th className="border border-gray-300 p-2 text-right">
                    Amount
                  </th>
                </tr>
              </thead>
              <tbody>
                {invoice.items.map((item) => (
                  <tr key={item.id}>
                    <td className="border border-gray-300 p-2">
                      {item.description}
                    </td>
                    <td className="border border-gray-300 p-2 text-right">
                      {item.amount.toFixed(2)} TND
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className="flex justify-end mb-3">
            <div className="w-64">
              <div className="flex justify-between py-1">
                <span>Subtotal:</span>
                <span>{invoice.subtotal.toFixed(2)} TND</span>
              </div>
              {invoice.discountAmount > 0 && (
                <div className="flex justify-between py-1">
                  <span>Discount:</span>
                  <span>-{invoice.discountAmount.toFixed(2)} TND</span>
                </div>
              )}
              <div className="flex justify-between py-2 border-t font-bold">
                <span>Total:</span>
                <span>{invoice.totalAmount.toFixed(2)} TND</span>
              </div>
            </div>
          </div>

          {invoice.notes && (
            <div className="mb-3">
              <h3 className="font-semibold mb-2">Notes:</h3>
              <p className="text-sm">{invoice.notes}</p>
            </div>
          )}

          <div className="text-center text-sm text-gray-600 mt-2">
            <p>Thank you for your business!</p>
          </div>
        </div>
      </div>
    </>
  );
};

export default InvoicePrint;
