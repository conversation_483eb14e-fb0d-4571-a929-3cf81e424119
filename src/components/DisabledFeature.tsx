import React from "react";
import { useTranslation } from "react-i18next";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Lock, AlertTriangle } from "lucide-react";

interface DisabledFeatureProps {
  featureName: string;
  description?: string;
  icon?: React.ReactNode;
  className?: string;
}

/**
 * Component to display when a feature is disabled
 * Shows a clear message to users about the disabled functionality
 */
const DisabledFeature: React.FC<DisabledFeatureProps> = ({
  featureName,
  description,
  icon,
  className = "",
}) => {
  const { t } = useTranslation();

  return (
    <div className={`flex items-center justify-center min-h-[400px] ${className}`}>
      <Card className="max-w-md mx-auto">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <div className="p-3 bg-gray-100 dark:bg-gray-800 rounded-full">
              {icon || <Lock className="h-8 w-8 text-gray-500" />}
            </div>
          </div>
          <CardTitle className="text-xl text-gray-700 dark:text-gray-300">
            {featureName} {t("common.disabled")}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              {description || 
                t("common.featureDisabledMessage", { feature: featureName })
              }
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    </div>
  );
};

export default DisabledFeature;
