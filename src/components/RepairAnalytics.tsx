// src/components/RepairAnalytics.tsx

import React from "react";
import { useRepairContext } from "@/context/RepairContext";
import { useTranslation } from "react-i18next";
import {
  Line,
  LineChart,
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  PieChart,
  Pie,
  Cell,
  Legend,
} from "recharts";

interface MonthlyRevenue {
  month: string;
  revenue: number;
}

const COLORS = ["#00C49F", "#FFBB28", "#FF4C4C"];

const RepairAnalytics = () => {
  const { repairs } = useRepairContext();

  const { t } = useTranslation();

  const paymentData = [
    {
      name: t("repair.payment.paid"),
      value: repairs.filter((repair) => repair.paymentStatus === "paid").length,
    },
    {
      name: t("repair.payment.partial"),
      value: repairs.filter((repair) => repair.paymentStatus === "partial")
        .length,
    },
    {
      name: t("repair.payment.unpaid"),
      value: repairs.filter((repair) => repair.paymentStatus === "unpaid")
        .length,
    },
  ];

  const totalAmountPaid = repairs.reduce((acc, repair) => {
    if (repair.paymentStatus === "paid") {
      return acc + repair.repairPrice;
    }
    return acc;
  }, 0);

  const averageAmountPaid = totalAmountPaid / paymentData[0].value;

  const monthlyRevenue: MonthlyRevenue[] = repairs.reduce((acc, repair) => {
    const month = new Date(repair.createdAt).toLocaleString("default", {
      month: "long",
    });
    if (!acc.find((item) => item.month === month)) {
      acc.push({ month, revenue: 0 });
    }
    const index = acc.findIndex((item) => item.month === month);
    acc[index].revenue += repair.repairPrice;
    return acc;
  }, []);

  return (
    <div className="flex flex-col md:flex-row md:justify-around gap-8">
      <div className="w-1/2">
        <h2>{t("analytics.title")}</h2>
        <PieChart width={400} height={300}>
          <Pie
            data={paymentData}
            cx="50%"
            cy="50%"
            labelLine={false}
            label={({ name, percent }) =>
              `${name} ${(percent * 100).toFixed(0)}%`
            }
            outerRadius={100}
            fill="#8884d8"
            dataKey="value"
          >
            {paymentData.map((entry, index) => (
              <Cell
                key={`cell-${index}`}
                fill={COLORS[index % COLORS.length]}
              />
            ))}
          </Pie>
          <Legend />
        </PieChart>
        <p>
          {t("analytics.totalAmountPaid")} {totalAmountPaid.toFixed(2)}TND
        </p>
        <p>
          {t("analytics.averageAmountPaid")} {averageAmountPaid.toFixed(2)}TND
        </p>
      </div>
      <h2>{t("analytics.monthlyRevenue")}</h2>
      <LineChart width={300} height={300} data={monthlyRevenue}>
        <CartesianGrid strokeDasharray="3 3" />
        <XAxis dataKey="month" />
        <YAxis />
        <Tooltip />
        <Line
          type="monotone"
          dataKey="revenue"
          stroke="#82ca9d"
          strokeWidth={3}
          dot={{ r: 5 }}
          activeDot={{ r: 8 }}
        />
      </LineChart>
    </div>
  );
};

export default RepairAnalytics;
