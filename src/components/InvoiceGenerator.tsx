import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Checkbox } from "@/components/ui/checkbox";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { RepairItem, InvoiceFormData } from "@/types";
import { toast } from "sonner";
import { FileText, Printer } from "lucide-react";
import { useTranslation } from "react-i18next";

const InvoiceGenerator: React.FC = () => {
  const [repairs, setRepairs] = useState<RepairItem[]>([]);
  const [selectedRepairs, setSelectedRepairs] = useState<string[]>([]);
  const [discountAmount, setDiscountAmount] = useState(0);
  const [notes, setNotes] = useState("");
  const [customerName, setCustomerName] = useState("");
  const [customerPhone, setCustomerPhone] = useState("");
  const [searchTerm, setSearchTerm] = useState("");
  const [loading, setLoading] = useState(false);
  const { user } = useAuth();
  const { repairShop } = useRepairShopContext();
  const { t } = useTranslation();

  useEffect(() => {
    fetchCompletedRepairs();
  }, []);

  const fetchCompletedRepairs = async () => {
    if (!repairShop) return;

    const { data, error } = await supabase
      .from("repairs")
      .select("*")
      .eq("repair_shop_id", repairShop.id)
      .order("created_at", { ascending: false });

    if (error) {
      toast.error("Failed to fetch repairs");
      return;
    }

    setRepairs(data.map(repair => ({
      id: repair.id,
      customerName: repair.customer_name,
      customerPhone: repair.customer_phone,
      phoneModel: repair.phone_model,
      problemDescription: repair.problem_description,
      repairPrice: repair.repair_price,
      paymentStatus: repair.payment_status,
      downPayment: repair.down_payment,
      createdAt: new Date(repair.created_at),
      completedAt: repair.completed_at ? new Date(repair.completed_at) : undefined,
      status: repair.status,
      userId: repair.user_id,
      repairShopId: repair.repair_shop_id,
    })));
  };

  const handleRepairSelection = (repairId: string, checked: boolean) => {
    if (checked) {
      setSelectedRepairs([...selectedRepairs, repairId]);
      // Auto-fill customer info from first selected repair
      if (selectedRepairs.length === 0) {
        const repair = repairs.find(r => r.id === repairId);
        if (repair) {
          setCustomerName(repair.customerName);
          setCustomerPhone(repair.customerPhone);
        }
      }
    } else {
      setSelectedRepairs(selectedRepairs.filter(id => id !== repairId));
      // Clear customer info if no repairs selected
      if (selectedRepairs.length === 1) {
        setCustomerName("");
        setCustomerPhone("");
      }
    }
  };

  const calculateTotal = () => {
    const subtotal = repairs
      .filter(repair => selectedRepairs.includes(repair.id))
      .reduce((sum, repair) => sum + (repair.repairPrice - repair.downPayment), 0);
    return subtotal - discountAmount;
  };

  const generateInvoice = async () => {
    if (selectedRepairs.length === 0) {
      toast.error("Please select at least one repair");
      return;
    }

    if (!user || !repairShop) return;

    setLoading(true);
    try {
      const selectedRepairItems = repairs.filter(repair => selectedRepairs.includes(repair.id));
      const subtotal = selectedRepairItems.reduce((sum, repair) => sum + (repair.repairPrice - repair.downPayment), 0);
      const totalAmount = subtotal - discountAmount;

      // Generate invoice number
      const { data: invoiceNumberData, error: invoiceNumberError } = await supabase
        .rpc('generate_invoice_number');

      if (invoiceNumberError) throw invoiceNumberError;

      // Create invoice
      const { data: invoice, error: invoiceError } = await supabase
        .from("invoices")
        .insert({
          invoice_number: invoiceNumberData,
          repair_shop_id: repairShop.id,
          customer_name: customerName,
          customer_phone: customerPhone,
          subtotal,
          discount_amount: discountAmount,
          total_amount: totalAmount,
          status: "draft",
          notes,
          user_id: user.id,
        })
        .select()
        .single();

      if (invoiceError) throw invoiceError;

      // Create invoice items
      const invoiceItems = selectedRepairItems.map(repair => ({
        invoice_id: invoice.id,
        repair_id: repair.id,
        description: `${repair.phoneModel} - ${repair.problemDescription}`,
        amount: repair.repairPrice - repair.downPayment,
      }));

      const { error: itemsError } = await supabase
        .from("invoice_items")
        .insert(invoiceItems);

      if (itemsError) throw itemsError;

      toast.success("Invoice generated successfully");
      setSelectedRepairs([]);
      setDiscountAmount(0);
      setNotes("");
      setCustomerName("");
      setCustomerPhone("");
      fetchCompletedRepairs();
    } catch (error) {
      console.error("Error generating invoice:", error);
      toast.error("Failed to generate invoice");
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            {t("invoices.invoiceGenerator")}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label>{t("invoices.selectRepairs")}</Label>
            <Input
              placeholder={`${t("common.search")}...`}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="mt-2 mb-2"
            />
            <div className="mt-2 space-y-2 max-h-60 overflow-y-auto">
              {repairs.filter(repair => 
                repair.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                repair.customerPhone.toLowerCase().includes(searchTerm.toLowerCase()) ||
                repair.phoneModel.toLowerCase().includes(searchTerm.toLowerCase()) ||
                repair.problemDescription.toLowerCase().includes(searchTerm.toLowerCase())
              ).map((repair) => (
                <div key={repair.id} className="flex items-center space-x-2 p-2 border rounded">
                  <Checkbox
                    checked={selectedRepairs.includes(repair.id)}
                    onCheckedChange={(checked) => handleRepairSelection(repair.id, checked as boolean)}
                  />
                  <div className="flex-1">
                    <div className="font-medium">{repair.customerName}</div>
                    <div className="text-sm text-muted-foreground">
                      {repair.phoneModel} - {repair.problemDescription}
                    </div>
                    <div className="text-sm font-medium">
                      Amount Due: {(repair.repairPrice - repair.downPayment).toFixed(2)} TND
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div>
              <Label htmlFor="customerName">{t("common.customerName")}</Label>
              <Input
                id="customerName"
                value={customerName}
                onChange={(e) => setCustomerName(e.target.value)}
                placeholder="Customer name"
              />
            </div>
            <div>
              <Label htmlFor="customerPhone">{t("common.customerPhone")}</Label>
              <Input
                id="customerPhone"
                value={customerPhone}
                onChange={(e) => setCustomerPhone(e.target.value)}
                placeholder="Customer phone"
              />
            </div>
          </div>

          <div>
            <Label htmlFor="discount">{t("invoices.discountAmount")}</Label>
            <Input
              id="discount"
              type="number"
              min="0"
              step="0.01"
              value={discountAmount}
              onChange={(e) => setDiscountAmount(parseFloat(e.target.value) || 0)}
            />
          </div>

          <div>
            <Label htmlFor="notes">{t("common.notes")}</Label>
            <Textarea
              id="notes"
              value={notes}
              onChange={(e) => setNotes(e.target.value)}
              placeholder="Additional notes for the invoice..."
            />
          </div>

          <div className="border-t pt-4">
            <div className="text-right space-y-1">
              <div>{t("invoices.subtotal")}: {repairs.filter(r => selectedRepairs.includes(r.id)).reduce((sum, r) => sum + (r.repairPrice - r.downPayment), 0).toFixed(2)} TND</div>
              <div>{t("invoices.discount")}: -{discountAmount.toFixed(2)} TND</div>
              <div className="font-bold text-lg">{t("invoices.total")}: {calculateTotal().toFixed(2)} TND</div>
            </div>
          </div>

          <Button 
            onClick={generateInvoice} 
            disabled={loading || selectedRepairs.length === 0 || !customerName.trim()}
            className="w-full"
          >
            <FileText className="h-4 w-4 mr-2" />
            {t("invoices.generateInvoice")}
          </Button>
        </CardContent>
      </Card>
    </div>
  );
};

export default InvoiceGenerator;