import React from "react";
import { useTranslation } from "react-i18next";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Keyboard, Info } from "lucide-react";
import { Shortcut } from "@/hooks/useKeyboardShortcuts";

interface KeyboardShortcutsHelpProps {
  shortcuts: Shortcut[];
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const KeyboardShortcutsHelp: React.FC<KeyboardShortcutsHelpProps> = ({
  shortcuts,
  open,
  onOpenChange,
}) => {
  const { t } = useTranslation();

  // Format key combination for display
  const formatKeyCombination = (shortcut: Shortcut) => {
    const keys = [];
    if (shortcut.ctrlKey) keys.push("Ctrl");
    if (shortcut.altKey) keys.push("Alt");
    if (shortcut.shiftKey) keys.push("Shift");
    keys.push(shortcut.key.toUpperCase());
    return keys.join(" + ");
  };

  // Filter shortcuts to only show the ones we want to display
  const allowedActions = [
    "goToDashboard",
    "newRepair",
    "printCurrentRepair",
    "help",
  ];
  const filteredShortcuts = shortcuts.filter((shortcut) =>
    allowedActions.includes(shortcut.action)
  );

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle className="flex items-center">
            <Keyboard className="h-5 w-5 mr-2 text-blue-500" />
            {t("shortcuts.title")}
          </DialogTitle>
          <DialogDescription>{t("shortcuts.description")}</DialogDescription>
        </DialogHeader>
        <div className="py-4">
          <div className="space-y-4">
            {filteredShortcuts.map((shortcut, index) => (
              <div
                key={index}
                className="flex justify-between items-center p-2 hover:bg-gray-50 rounded-md"
              >
                <div className="flex items-center">
                  <span className="text-sm font-medium">
                    {t(shortcut.description)}
                  </span>
                </div>
                <div className="flex items-center">
                  <kbd className="px-2 py-1.5 text-xs font-semibold text-gray-800 bg-gray-100 border border-gray-200 rounded-lg">
                    {formatKeyCombination(shortcut)}
                  </kbd>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="flex items-center text-sm text-gray-500 bg-blue-50 p-3 rounded-md">
          <Info className="h-4 w-4 mr-2 text-blue-500" />
          {t("shortcuts.tip")}
        </div>
        <DialogFooter>
          <Button onClick={() => onOpenChange(false)}>
            {t("common.close")}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default KeyboardShortcutsHelp;
