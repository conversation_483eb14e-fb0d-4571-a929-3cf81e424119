// src/components/UniversalScanner.tsx
import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Camera, Barcode } from "lucide-react";
import QRScanner from "./QRScanner";
import HenexScanner from "./HenexScanner";

interface UniversalScannerProps {
  onScan: (value: string) => void;
}

const UniversalScanner: React.FC<UniversalScannerProps> = ({ onScan }) => {
  const [activeTab, setActiveTab] = useState<string>("henex"); // Default to HENEX scanner
  const [autoStartHenex, setAutoStartHenex] = useState<boolean>(false); // Don't auto-start HENEX scanner by default
  const { t } = useTranslation();

  // When tab changes, make sure HENEX scanner is stopped
  useEffect(() => {
    // Always ensure HENEX scanner is stopped when switching tabs
    // The user will need to press the Start Scanning button to begin
    setAutoStartHenex(false);
  }, [activeTab]);

  return (
    <Tabs defaultValue="henex" onValueChange={setActiveTab}>
      <TabsList className="grid w-full grid-cols-2">
        <TabsTrigger value="henex">
          <Barcode className="mr-2 h-4 w-4" />
          {t("scanner.henexScanner")}
        </TabsTrigger>
        <TabsTrigger value="camera">
          <Camera className="mr-2 h-4 w-4" />
          {t("scanner.cameraScanner")}
        </TabsTrigger>
      </TabsList>

      <TabsContent value="henex" className="mt-4">
        <div className="space-y-2">
          <HenexScanner
            onScan={onScan}
            autoStart={autoStartHenex}
            showStartButton={true}
          />
        </div>
      </TabsContent>

      <TabsContent value="camera" className="mt-4">
        <QRScanner onScan={onScan} />
      </TabsContent>
    </Tabs>
  );
};

export default UniversalScanner;
