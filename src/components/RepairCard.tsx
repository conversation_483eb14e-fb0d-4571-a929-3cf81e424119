import React, { useState, useEffect } from "react";
import { Link } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { RepairItem } from "@/types";
import {
  <PERSON>,
  Card<PERSON>ontent,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON>er,
  <PERSON><PERSON>itle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { generateQRCodeDataURL } from "@/utils/qrCodeUtils";
import { formatDistanceToNow } from "date-fns";
import { Phone, FileText, CreditCard, AlertCircle } from "lucide-react";
import { useTheme } from "@/context/ThemeContext";

interface RepairCardProps {
  repair: RepairItem;
}

const statusColors = {
  pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
  inProgress: "bg-blue-100 text-blue-800 border-blue-200",
  completed: "bg-green-100 text-green-800 border-green-200",
  cancelled: "bg-red-100 text-red-800 border-red-200",
  returned: "bg-purple-100 text-purple-800 border-purple-200",
};

const paymentStatusColors = {
  paid: "bg-green-100 text-green-800 border-green-200",
  partial: "bg-blue-100 text-blue-800 border-blue-200",
  unpaid: "bg-red-100 text-red-800 border-red-200",
};

const RepairCard: React.FC<RepairCardProps> = ({ repair }) => {
  const [qrCode, setQrCode] = useState<string | null>(null);
  const { t, i18n } = useTranslation();
  const { isPremium } = useTheme();

  useEffect(() => {
    const generateQR = async () => {
      try {
        const qrDataUrl = await generateQRCodeDataURL(repair.id);
        setQrCode(qrDataUrl);
      } catch (error) {
        console.error("Failed to generate QR code:", error);
      }
    };

    generateQR();
  }, [repair.id]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(i18n.language, {
      style: "currency",
      currency: "TND",
    }).format(amount);
  };

  const getStatusText = (status: string) => {
    return t(`repair.status.${status}`);
  };

  return (
    <Card
      className={`overflow-hidden ${
        isPremium ? "premium-card premium-glow" : ""
      }`}
    >
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg">{repair.customerName}</CardTitle>
            <p className="text-sm text-gray-500">{repair.customerPhone}</p>
          </div>
          <Badge className={statusColors[repair.status]}>
            {getStatusText(repair.status)}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pb-2">
        <div className="flex justify-between items-start">
          <div className="space-y-3 flex-1">
            <div className="flex items-center space-x-2">
              <Phone className="h-4 w-4 text-gray-500" />
              <span className="text-sm">{repair.phoneModel}</span>
            </div>

            {repair.branch && (
              <div className="flex items-center space-x-2">
                <div className="h-4 w-4 text-gray-500 flex items-center justify-center">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                </div>
                <span className="text-sm text-blue-600 font-medium">
                  {repair.branch.name} ({repair.branch.code})
                </span>
              </div>
            )}

            <div className="flex items-start space-x-2">
              <FileText className="h-4 w-4 text-gray-500 mt-0.5" />
              <p className="text-sm text-gray-600 line-clamp-2">
                {repair.problemDescription}
              </p>
            </div>

            <div className="flex items-center space-x-2">
              <CreditCard className="h-4 w-4 text-gray-500" />
              <div className="flex flex-wrap gap-2 items-center">
                {repair.priceModifications &&
                repair.priceModifications.length > 0 ? (
                  <div className="flex items-center gap-1">
                    <span className="text-sm font-medium">
                      {formatCurrency(
                        repair.repairPrice +
                          repair.priceModifications.reduce(
                            (sum, mod) => sum + mod.amount,
                            0
                          )
                      )}
                    </span>
                    <span
                      className="inline-flex items-center cursor-help"
                      title={`${t("repair.priceModified")}\n${t(
                        "repair.initialPrice"
                      )}: ${formatCurrency(
                        repair.repairPrice
                      )}\n${repair.priceModifications
                        .map(
                          (mod) =>
                            `${mod.reason}: ${formatCurrency(mod.amount)}`
                        )
                        .join("\n")}`}
                    >
                      <AlertCircle className="h-3 w-3 text-blue-600 ml-1" />
                    </span>
                  </div>
                ) : (
                  <span className="text-sm font-medium">
                    {formatCurrency(repair.repairPrice)}
                  </span>
                )}
                <Badge className={paymentStatusColors[repair.paymentStatus]}>
                  {t(`repair.payment.${repair.paymentStatus}`)}
                </Badge>
                {repair.downPayment > 0 && (
                  <span className="text-xs text-gray-500">
                    {t("repair.downPayment")}:{" "}
                    {formatCurrency(repair.downPayment)}
                  </span>
                )}
              </div>
            </div>
          </div>

          {qrCode && (
            <div className="ml-4">
              <img
                src={qrCode}
                alt="QR Code"
                className="w-24 h-24 object-contain"
              />
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter className="pt-2 flex justify-between">
        <div className="text-xs text-gray-500">
          {t("repair.addedAgo", {
            time: formatDistanceToNow(new Date(repair.createdAt), {
              addSuffix: true,
            }),
          })}
        </div>
        <Button
          asChild
          variant="ghost"
          size="sm"
          className={isPremium ? "premium-button" : ""}
        >
          <Link to={`/repair/${repair.id}`}>{t("repair.viewDetails")}</Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

export default RepairCard;
