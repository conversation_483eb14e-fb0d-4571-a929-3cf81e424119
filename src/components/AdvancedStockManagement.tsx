import React, { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { toast } from "sonner";
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import { useTranslation } from "react-i18next";

interface Product {
  id: string;
  name: string;
  description: string | null;
  sku: string;
  barcode: string | null;
  category_id: string;
  price: number;
  cost: number;
  stock_quantity: number;
  min_stock_level: number;
  max_stock_level: number | null;
  is_active: boolean;
  image_url: string | null;
  repair_shop_id: string;
  created_at: string;
  updated_at: string;
  category?: {
    id: string;
    name: string;
  };
}

interface Category {
  id: string;
  name: string;
  description: string | null;
  icon: string | null;
  color: string | null;
  repair_shop_id: string;
}

interface StockMovement {
  id: string;
  product_id: string;
  type: 'in' | 'out' | 'adjustment';
  quantity: number;
  reason: string;
  reference: string | null;
  user_id: string;
  repair_shop_id: string;
  created_at: string;
  product?: {
    name: string;
    sku: string;
  };
  users?: {
    email: string;
  };
}

export default function AdvancedStockManagement() {
  const { user, hasPermission, logUserAction } = useAuth();
  const { t } = useTranslation();
  const [activeTab, setActiveTab] = useState("inventory");
  const [products, setProducts] = useState<Product[]>([]);
  const [categories, setCategories] = useState<Category[]>([]);
  const [stockMovements, setStockMovements] = useState<StockMovement[]>([]);
  const [selectedShop, setSelectedShop] = useState<string | null>(null);
  const [shops, setShops] = useState<any[]>([]);
  const [lowStockProducts, setLowStockProducts] = useState<Product[]>([]);
  const [productDialogOpen, setProductDialogOpen] = useState(false);
  const [categoryDialogOpen, setCategoryDialogOpen] = useState(false);
  const [movementDialogOpen, setMovementDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<Category | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterCategory, setFilterCategory] = useState<string | null>(null);
  
  // Form states for product
  const [productName, setProductName] = useState("");
  const [productDescription, setProductDescription] = useState("");
  const [productSKU, setProductSKU] = useState("");
  const [productBarcode, setProductBarcode] = useState("");
  const [productCategoryId, setProductCategoryId] = useState("");
  const [productPrice, setProductPrice] = useState("");
  const [productCost, setProductCost] = useState("");
  const [productStockQuantity, setProductStockQuantity] = useState("");
  const [productMinStockLevel, setProductMinStockLevel] = useState("");
  const [productMaxStockLevel, setProductMaxStockLevel] = useState("");
  
  // Form states for category
  const [categoryName, setCategoryName] = useState("");
  const [categoryDescription, setCategoryDescription] = useState("");
  const [categoryIcon, setCategoryIcon] = useState("");
  const [categoryColor, setCategoryColor] = useState("#6366F1");
  
  // Form states for stock movement
  const [movementProductId, setMovementProductId] = useState("");
  const [movementType, setMovementType] = useState<'in' | 'out' | 'adjustment'>('in');
  const [movementQuantity, setMovementQuantity] = useState("");
  const [movementReason, setMovementReason] = useState("");
  const [movementReference, setMovementReference] = useState("");

  const canManageStock = hasPermission("manage_stock");
  const canViewStock = hasPermission("view_stock");

  useEffect(() => {
    if (!user) return;
    
    // Get all shops the user has access to
    const fetchShops = async () => {
      const { data, error } = await supabase
        .from("repair_shops")
        .select("id, name")
        .order("name");
      
      if (!error && data) {
        setShops(data);
        if (!selectedShop && data.length > 0) {
          setSelectedShop(data[0].id);
        }
      }
    };
    
    fetchShops();
  }, [user, selectedShop]);

  useEffect(() => {
    if (!user || !selectedShop || !canViewStock) return;
    
    // Get all categories for the selected shop
    const fetchCategories = async () => {
      const { data, error } = await supabase
        .from("product_categories")
        .select("*")
        .eq("repair_shop_id", selectedShop)
        .order("name");
      
      if (!error && data) {
        setCategories(data);
      }
    };
    
    fetchCategories();
  }, [user, selectedShop, canViewStock]);

  useEffect(() => {
    if (!user || !selectedShop || !canViewStock) return;
    
    // Get all products for the selected shop
    const fetchProducts = async () => {
      let query = supabase
        .from("products")
        .select("*, category:category_id(id, name)")
        .eq("repair_shop_id", selectedShop);
      
      if (filterCategory) {
        query = query.eq("category_id", filterCategory);
      }
      
      if (searchTerm) {
        query = query.or(`name.ilike.%${searchTerm}%,sku.ilike.%${searchTerm}%,barcode.ilike.%${searchTerm}%`);
      }
      
      const { data, error } = await query.order("name");
      
      if (!error && data) {
        setProducts(data);
        
        // Filter low stock products
        const lowStock = data.filter(product => 
          product.stock_quantity <= product.min_stock_level
        );
        setLowStockProducts(lowStock);
      }
    };
    
    fetchProducts();
  }, [user, selectedShop, filterCategory, searchTerm, canViewStock]);

  useEffect(() => {
    if (!user || !selectedShop || activeTab !== "movements" || !canViewStock) return;
    
    // Get stock movements for the selected shop
    const fetchStockMovements = async () => {
      const { data, error } = await supabase
        .from("stock_movements")
        .select(`
          *,
          product:product_id(name, sku)
        `)
        .eq("repair_shop_id", selectedShop)
        .order("created_at", { ascending: false })
        .limit(100);
      
      if (!error && data) {
        setStockMovements(data);
      }
    };
    
    fetchStockMovements();
  }, [user, selectedShop, activeTab, canViewStock]);

  const handleProductSelect = (product: Product) => {
    setSelectedProduct(product);
    setProductName(product.name);
    setProductDescription(product.description || "");
    setProductSKU(product.sku);
    setProductBarcode(product.barcode || "");
    setProductCategoryId(product.category_id);
    setProductPrice(product.price.toString());
    setProductCost(product.cost.toString());
    setProductStockQuantity(product.stock_quantity.toString());
    setProductMinStockLevel(product.min_stock_level.toString());
    setProductMaxStockLevel(product.max_stock_level?.toString() || "");
    setProductDialogOpen(true);
  };

  const handleCategorySelect = (category: Category) => {
    setSelectedCategory(category);
    setCategoryName(category.name);
    setCategoryDescription(category.description || "");
    setCategoryIcon(category.icon || "");
    setCategoryColor(category.color || "#6366F1");
    setCategoryDialogOpen(true);
  };

  const resetProductForm = () => {
    setSelectedProduct(null);
    setProductName("");
    setProductDescription("");
    setProductSKU("");
    setProductBarcode("");
    setProductCategoryId("");
    setProductPrice("");
    setProductCost("");
    setProductStockQuantity("");
    setProductMinStockLevel("");
    setProductMaxStockLevel("");
  };

  const resetCategoryForm = () => {
    setSelectedCategory(null);
    setCategoryName("");
    setCategoryDescription("");
    setCategoryIcon("");
    setCategoryColor("#6366F1");
  };

  const resetMovementForm = () => {
    setMovementProductId("");
    setMovementType("in");
    setMovementQuantity("");
    setMovementReason("");
    setMovementReference("");
  };

  const handleSaveProduct = async () => {
    if (!selectedShop || !productName || !productSKU || !productCategoryId) {
      toast.error("Please fill in all required fields");
      return;
    }
    
    try {
      const productData = {
        name: productName,
        description: productDescription || null,
        sku: productSKU,
        barcode: productBarcode || null,
        category_id: productCategoryId,
        price: parseFloat(productPrice) || 0,
        cost: parseFloat(productCost) || 0,
        stock_quantity: parseInt(productStockQuantity) || 0,
        min_stock_level: parseInt(productMinStockLevel) || 0,
        max_stock_level: productMaxStockLevel ? parseInt(productMaxStockLevel) : null,
        repair_shop_id: selectedShop
      };
      
      let result;
      
      if (selectedProduct) {
        // Update existing product
        const { data, error } = await supabase
          .from("products")
          .update(productData)
          .eq("id", selectedProduct.id)
          .select()
          .single();
        
        if (error) throw error;
        result = data;
        
        await logUserAction("update_product", { 
          product_id: selectedProduct.id,
          product_name: productName
        });
        
        toast.success("Product updated successfully");
      } else {
        // Create new product
        const { data, error } = await supabase
          .from("products")
          .insert({
            ...productData,
            is_active: true
          })
          .select()
          .single();
        
        if (error) throw error;
        result = data;
        
        await logUserAction("create_product", { 
          product_id: result.id,
          product_name: productName
        });
        
        toast.success("Product created successfully");
      }
      
      resetProductForm();
      setProductDialogOpen(false);
      
      // Refresh products list
      const { data: updatedProducts } = await supabase
        .from("products")
        .select("*, category:category_id(id, name)")
        .eq("repair_shop_id", selectedShop)
        .order("name");
      
      if (updatedProducts) {
        setProducts(updatedProducts);
        
        // Update low stock products
        const lowStock = updatedProducts.filter(product => 
          product.stock_quantity <= product.min_stock_level
        );
        setLowStockProducts(lowStock);
      }
    } catch (error) {
      toast.error("Failed to save product");
      console.error(error);
    }
  };

  const handleSaveCategory = async () => {
    if (!selectedShop || !categoryName) {
      toast.error("Please fill in all required fields");
      return;
    }
    
    try {
      const categoryData = {
        name: categoryName,
        description: categoryDescription || null,
        icon: categoryIcon || null,
        color: categoryColor || null,
        repair_shop_id: selectedShop
      };
      
      let result;
      
      if (selectedCategory) {
        // Update existing category
        const { data, error } = await supabase
          .from("product_categories")
          .update(categoryData)
          .eq("id", selectedCategory.id)
          .select()
          .single();
        
        if (error) throw error;
        result = data;
        
        await logUserAction("update_category", { 
          category_id: selectedCategory.id,
          category_name: categoryName
        });
        
        toast.success("Category updated successfully");
      } else {
        // Create new category
        const { data, error } = await supabase
          .from("product_categories")
          .insert(categoryData)
          .select()
          .single();
        
        if (error) throw error;
        result = data;
        
        await logUserAction("create_category", { 
          category_id: result.id,
          category_name: categoryName
        });
        
        toast.success("Category created successfully");
      }
      
      resetCategoryForm();
      setCategoryDialogOpen(false);
      
      // Refresh categories list
      const { data: updatedCategories } = await supabase
        .from("product_categories")
        .select("*")
        .eq("repair_shop_id", selectedShop)
        .order("name");
      
      if (updatedCategories) {
        setCategories(updatedCategories);
      }
    } catch (error) {
      toast.error("Failed to save category");
      console.error(error);
    }
  };

  const handleSaveMovement = async () => {
    if (!selectedShop || !movementProductId || !movementReason || !movementQuantity) {
      toast.error("Please fill in all required fields");
      return;
    }
    
    try {
      const quantity = parseInt(movementQuantity);
      if (isNaN(quantity) || quantity <= 0) {
        toast.error("Quantity must be a positive number");
        return;
      }
      
      // Get current product stock
      const { data: product } = await supabase
        .from("products")
        .select("stock_quantity, name")
        .eq("id", movementProductId)
        .single();
      
      if (!product) {
        toast.error("Product not found");
        return;
      }
      
      // Calculate new stock quantity
      let newQuantity = product.stock_quantity;
      
      switch (movementType) {
        case 'in':
          newQuantity += quantity;
          break;
        case 'out':
          if (product.stock_quantity < quantity) {
            toast.error("Not enough stock available");
            return;
          }
          newQuantity -= quantity;
          break;
        case 'adjustment':
          newQuantity = quantity; // Direct set to the new value
          break;
      }
      
      // Create stock movement record
      const { error: movementError } = await supabase
        .from("stock_movements")
        .insert({
          product_id: movementProductId,
          type: movementType,
          quantity: quantity,
          reason: movementReason,
          reference: movementReference || null,
          user_id: user!.id,
          repair_shop_id: selectedShop
        });
      
      if (movementError) throw movementError;
      
      // Update product stock quantity
      const { error: updateError } = await supabase
        .from("products")
        .update({ stock_quantity: newQuantity })
        .eq("id", movementProductId);
      
      if (updateError) throw updateError;
      
      await logUserAction("stock_movement", { 
        product_id: movementProductId,
        type: movementType,
        quantity: quantity,
        new_quantity: newQuantity
      });
      
      toast.success(`Stock ${movementType === 'in' ? 'added' : movementType === 'out' ? 'removed' : 'adjusted'} successfully`);
      
      resetMovementForm();
      setMovementDialogOpen(false);
      
      // Refresh products and movements
      if (activeTab === "movements") {
        const { data: updatedMovements } = await supabase
          .from("stock_movements")
          .select(`
            *,
            product:product_id(name, sku)
          `)
          .eq("repair_shop_id", selectedShop)
          .order("created_at", { ascending: false })
          .limit(100);
        
        if (updatedMovements) {
          setStockMovements(updatedMovements);
        }
      }
      
      // Refresh products list
      const { data: updatedProducts } = await supabase
        .from("products")
        .select("*, category:category_id(id, name)")
        .eq("repair_shop_id", selectedShop)
        .order("name");
      
      if (updatedProducts) {
        setProducts(updatedProducts);
        
        // Update low stock products
        const lowStock = updatedProducts.filter(product => 
          product.stock_quantity <= product.min_stock_level
        );
        setLowStockProducts(lowStock);
      }
    } catch (error) {
      toast.error("Failed to process stock movement");
      console.error(error);
    }
  };

  if (!canViewStock) {
    return (
      <div className="flex items-center justify-center h-screen">
        Access denied. You need stock management privileges.
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">{t("advancedStock.title")}</h1>
      
      <div className="flex items-center space-x-4 mb-6">
        <div className="flex-1">
          <Label htmlFor="shop-select">Select Shop</Label>
          <Select 
            value={selectedShop || ""} 
            onValueChange={(value) => setSelectedShop(value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a shop" />
            </SelectTrigger>
            <SelectContent>
              {shops.map((shop) => (
                <SelectItem key={shop.id} value={shop.id}>
                  {shop.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>
      
      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="inventory">{t("advancedStock.inventory")}</TabsTrigger>
          <TabsTrigger value="low-stock">{t("advancedStock.lowStock")}</TabsTrigger>
          <TabsTrigger value="categories">{t("advancedStock.categories")}</TabsTrigger>
          <TabsTrigger value="movements">{t("advancedStock.movements")}</TabsTrigger>
        </TabsList>
        
        <TabsContent value="inventory">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>{t("advancedStock.productInventory")}</CardTitle>
                <CardDescription>
                  {t("advancedStock.productInventory")}
                </CardDescription>
              </div>
              {canManageStock && (
                <Button onClick={() => {
                  resetProductForm();
                  setProductDialogOpen(true);
                }}>
                  {t("advancedStock.addProduct")}
                </Button>
              )}
            </CardHeader>
            <CardContent>
              <div className="flex flex-col md:flex-row gap-4 mb-4">
                <div className="flex-1">
                  <Label htmlFor="search">Search Products</Label>
                  <Input
                    id="search"
                    placeholder="Search by name, SKU, or barcode"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
                <div className="w-full md:w-64">
                  <Label htmlFor="category-filter">Filter by Category</Label>
                  <Select 
                    value={filterCategory || "all"} 
                    onValueChange={(value) => setFilterCategory(value === "all" ? null : value)}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="All Categories" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">All Categories</SelectItem>
                      {categories.map((category) => (
                        <SelectItem key={category.id} value={category.id}>
                          {category.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>
              
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="p-2 text-left font-medium">Name</th>
                      <th className="p-2 text-left font-medium">SKU</th>
                      <th className="p-2 text-left font-medium">Category</th>
                      <th className="p-2 text-left font-medium">Stock</th>
                      <th className="p-2 text-left font-medium">Price</th>
                      <th className="p-2 text-left font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {products.length === 0 ? (
                      <tr>
                        <td colSpan={6} className="p-4 text-center text-muted-foreground">
                          No products found.
                        </td>
                      </tr>
                    ) : (
                      products.map((product) => (
                        <tr key={product.id} className="border-b">
                          <td className="p-2">
                            {product.name}
                            {product.description && (
                              <span className="block text-xs text-muted-foreground">
                                {product.description}
                              </span>
                            )}
                          </td>
                          <td className="p-2">
                            {product.sku}
                            {product.barcode && (
                              <span className="block text-xs text-muted-foreground">
                                Barcode: {product.barcode}
                              </span>
                            )}
                          </td>
                          <td className="p-2">
                            {product.category?.name || 'Uncategorized'}
                          </td>
                          <td className="p-2">
                            <span className={`font-medium ${
                              product.stock_quantity <= product.min_stock_level
                                ? 'text-red-500 dark:text-red-400'
                                : ''
                            }`}>
                              {product.stock_quantity}
                            </span>
                            <span className="block text-xs text-muted-foreground">
                              Min: {product.min_stock_level}
                              {product.max_stock_level && `, Max: ${product.max_stock_level}`}
                            </span>
                          </td>
                          <td className="p-2">
                            {product.price.toFixed(3)} TND
                            <span className="block text-xs text-muted-foreground">
                              Cost: {product.cost.toFixed(3)} TND
                            </span>
                          </td>
                          <td className="p-2">
                            <div className="flex space-x-2">
                              {canManageStock && (
                                <>
                                  <Button 
                                    variant="outline" 
                                    size="sm"
                                    onClick={() => handleProductSelect(product)}
                                  >
                                    Edit
                                  </Button>
                                  <Button 
                                    variant="outline" 
                                    size="sm"
                                    onClick={() => {
                                      setMovementProductId(product.id);
                                      setMovementDialogOpen(true);
                                    }}
                                  >
                                    Adjust
                                  </Button>
                                </>
                              )}
                            </div>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="low-stock">
          <Card>
            <CardHeader>
              <CardTitle>Low Stock Alerts</CardTitle>
              <CardDescription>
                Products that need to be restocked
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="p-2 text-left font-medium">Name</th>
                      <th className="p-2 text-left font-medium">SKU</th>
                      <th className="p-2 text-left font-medium">Category</th>
                      <th className="p-2 text-left font-medium">Current Stock</th>
                      <th className="p-2 text-left font-medium">Min Level</th>
                      <th className="p-2 text-left font-medium">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {lowStockProducts.length === 0 ? (
                      <tr>
                        <td colSpan={6} className="p-4 text-center text-muted-foreground">
                          No low stock products found.
                        </td>
                      </tr>
                    ) : (
                      lowStockProducts.map((product) => (
                        <tr key={product.id} className="border-b">
                          <td className="p-2">
                            {product.name}
                            {product.description && (
                              <span className="block text-xs text-muted-foreground">
                                {product.description}
                              </span>
                            )}
                          </td>
                          <td className="p-2">{product.sku}</td>
                          <td className="p-2">
                            {product.category?.name || 'Uncategorized'}
                          </td>
                          <td className="p-2 text-red-500 dark:text-red-400 font-medium">
                            {product.stock_quantity}
                          </td>
                          <td className="p-2">{product.min_stock_level}</td>
                          <td className="p-2">
                            {canManageStock && (
                              <Button 
                                variant="outline" 
                                size="sm"
                                onClick={() => {
                                  setMovementProductId(product.id);
                                  setMovementType('in');
                                  setMovementDialogOpen(true);
                                }}
                              >
                                Restock
                              </Button>
                            )}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="categories">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Product Categories</CardTitle>
                <CardDescription>
                  Manage your product categories
                </CardDescription>
              </div>
              {canManageStock && (
                <Button onClick={() => {
                  resetCategoryForm();
                  setCategoryDialogOpen(true);
                }}>
                  Add Category
                </Button>
              )}
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {categories.length === 0 ? (
                  <div className="col-span-full p-4 text-center text-muted-foreground">
                    No categories found.
                  </div>
                ) : (
                  categories.map((category) => (
                    <Card key={category.id} className="overflow-hidden">
                      <div 
                        className="h-2" 
                        style={{ backgroundColor: category.color || '#6366F1' }}
                      />
                      <CardHeader className="pb-2">
                        <CardTitle className="flex items-center">
                          {category.icon && (
                            <span className="mr-2">{category.icon}</span>
                          )}
                          {category.name}
                        </CardTitle>
                        {category.description && (
                          <CardDescription>{category.description}</CardDescription>
                        )}
                      </CardHeader>
                      <CardContent>
                        {canManageStock && (
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleCategorySelect(category)}
                          >
                            Edit Category
                          </Button>
                        )}
                      </CardContent>
                    </Card>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
        
        <TabsContent value="movements">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>{t("advancedStock.stockMovements")}</CardTitle>
                <CardDescription>
                  {t("advancedStock.trackAllStockChanges")}
                </CardDescription>
              </div>
              {canManageStock && (
                <Button onClick={() => {
                  resetMovementForm();
                  setMovementDialogOpen(true);
                }}>
                  Record Movement
                </Button>
              )}
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="p-2 text-left font-medium">Date & Time</th>
                      <th className="p-2 text-left font-medium">Product</th>
                      <th className="p-2 text-left font-medium">Type</th>
                      <th className="p-2 text-left font-medium">Quantity</th>
                      <th className="p-2 text-left font-medium">Reason</th>
                      <th className="p-2 text-left font-medium">User</th>
                    </tr>
                  </thead>
                  <tbody>
                    {stockMovements.length === 0 ? (
                      <tr>
                        <td colSpan={6} className="p-4 text-center text-muted-foreground">
                          No stock movements found.
                        </td>
                      </tr>
                    ) : (
                      stockMovements.map((movement) => (
                        <tr key={movement.id} className="border-b">
                          <td className="p-2">
                            {new Date(movement.created_at).toLocaleString()}
                          </td>
                          <td className="p-2">
                            {movement.product?.name || 'Unknown Product'}
                            <span className="block text-xs text-muted-foreground">
                              SKU: {movement.product?.sku || 'N/A'}
                            </span>
                          </td>
                          <td className="p-2">
                            <Badge variant={
                              movement.type === 'in' ? 'default' : 
                              movement.type === 'out' ? 'destructive' : 
                              'outline'
                            }>
                              {movement.type === 'in' ? 'Stock In' : 
                               movement.type === 'out' ? 'Stock Out' : 
                               'Adjustment'}
                            </Badge>
                          </td>
                          <td className="p-2 font-medium">
                            {movement.quantity}
                          </td>
                          <td className="p-2">
                            {movement.reason}
                            {movement.reference && (
                              <span className="block text-xs text-muted-foreground">
                                Ref: {movement.reference}
                              </span>
                            )}
                          </td>
                          <td className="p-2">
                            {movement.user_id || 'Unknown User'}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
      
      {/* Product Dialog */}
      <Dialog open={productDialogOpen} onOpenChange={setProductDialogOpen}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>
              {selectedProduct ? 'Edit Product' : 'Add New Product'}
            </DialogTitle>
            <DialogDescription>
              {selectedProduct 
                ? 'Update the product details below' 
                : 'Fill in the product details below'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="product-name">Product Name *</Label>
              <Input
                id="product-name"
                value={productName}
                onChange={(e) => setProductName(e.target.value)}
                placeholder="Product name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="product-sku">SKU *</Label>
              <Input
                id="product-sku"
                value={productSKU}
                onChange={(e) => setProductSKU(e.target.value)}
                placeholder="Stock keeping unit"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="product-barcode">Barcode</Label>
              <Input
                id="product-barcode"
                value={productBarcode}
                onChange={(e) => setProductBarcode(e.target.value)}
                placeholder="Optional barcode"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="product-category">Category *</Label>
              <Select 
                value={productCategoryId} 
                onValueChange={setProductCategoryId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a category" />
                </SelectTrigger>
                <SelectContent>
                  {categories.map((category) => (
                    <SelectItem key={category.id} value={category.id}>
                      {category.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="product-price">Price *</Label>
              <Input
                id="product-price"
                type="number"
                min="0"
                step="0.01"
                value={productPrice}
                onChange={(e) => setProductPrice(e.target.value)}
                placeholder="Selling price"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="product-cost">Cost *</Label>
              <Input
                id="product-cost"
                type="number"
                min="0"
                step="0.01"
                value={productCost}
                onChange={(e) => setProductCost(e.target.value)}
                placeholder="Purchase cost"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="product-stock">Current Stock *</Label>
              <Input
                id="product-stock"
                type="number"
                min="0"
                value={productStockQuantity}
                onChange={(e) => setProductStockQuantity(e.target.value)}
                placeholder="Current quantity"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="product-min-stock">Minimum Stock Level *</Label>
              <Input
                id="product-min-stock"
                type="number"
                min="0"
                value={productMinStockLevel}
                onChange={(e) => setProductMinStockLevel(e.target.value)}
                placeholder="Alert threshold"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="product-max-stock">Maximum Stock Level</Label>
              <Input
                id="product-max-stock"
                type="number"
                min="0"
                value={productMaxStockLevel}
                onChange={(e) => setProductMaxStockLevel(e.target.value)}
                placeholder="Optional maximum"
              />
            </div>
            
            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="product-description">Description</Label>
              <Textarea
                id="product-description"
                value={productDescription}
                onChange={(e) => setProductDescription(e.target.value)}
                placeholder="Product description"
                rows={3}
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setProductDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveProduct}>Save Product</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Category Dialog */}
      <Dialog open={categoryDialogOpen} onOpenChange={setCategoryDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {selectedCategory ? 'Edit Category' : 'Add New Category'}
            </DialogTitle>
            <DialogDescription>
              {selectedCategory 
                ? 'Update the category details below' 
                : 'Fill in the category details below'}
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="category-name">Category Name *</Label>
              <Input
                id="category-name"
                value={categoryName}
                onChange={(e) => setCategoryName(e.target.value)}
                placeholder="Category name"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="category-description">Description</Label>
              <Textarea
                id="category-description"
                value={categoryDescription}
                onChange={(e) => setCategoryDescription(e.target.value)}
                placeholder="Category description"
                rows={3}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="category-icon">Icon (Emoji)</Label>
              <Input
                id="category-icon"
                value={categoryIcon}
                onChange={(e) => setCategoryIcon(e.target.value)}
                placeholder="📱"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="category-color">Color</Label>
              <div className="flex items-center gap-2">
                <Input
                  id="category-color"
                  type="color"
                  value={categoryColor}
                  onChange={(e) => setCategoryColor(e.target.value)}
                  className="w-12 h-10 p-1"
                />
                <Input
                  value={categoryColor}
                  onChange={(e) => setCategoryColor(e.target.value)}
                  placeholder="#6366F1"
                />
              </div>
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setCategoryDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveCategory}>Save Category</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      
      {/* Stock Movement Dialog */}
      <Dialog open={movementDialogOpen} onOpenChange={setMovementDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Record Stock Movement</DialogTitle>
            <DialogDescription>
              Add, remove, or adjust product stock levels
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="movement-product">Product *</Label>
              <Select 
                value={movementProductId} 
                onValueChange={setMovementProductId}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select a product" />
                </SelectTrigger>
                <SelectContent>
                  {products.map((product) => (
                    <SelectItem key={product.id} value={product.id}>
                      {product.name} ({product.sku})
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="movement-type">Movement Type *</Label>
              <Select 
                value={movementType} 
                onValueChange={(value: 'in' | 'out' | 'adjustment') => setMovementType(value)}
              >
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="in">Stock In</SelectItem>
                  <SelectItem value="out">Stock Out</SelectItem>
                  <SelectItem value="adjustment">Direct Adjustment</SelectItem>
                </SelectContent>
              </Select>
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="movement-quantity">
                {movementType === 'adjustment' ? 'New Quantity *' : 'Quantity *'}
              </Label>
              <Input
                id="movement-quantity"
                type="number"
                min="0"
                value={movementQuantity}
                onChange={(e) => setMovementQuantity(e.target.value)}
                placeholder={movementType === 'adjustment' ? "New total quantity" : "Quantity to add/remove"}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="movement-reason">Reason *</Label>
              <Input
                id="movement-reason"
                value={movementReason}
                onChange={(e) => setMovementReason(e.target.value)}
                placeholder="Why is this change being made?"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="movement-reference">Reference</Label>
              <Input
                id="movement-reference"
                value={movementReference}
                onChange={(e) => setMovementReference(e.target.value)}
                placeholder="Optional reference number"
              />
            </div>
          </div>
          
          <DialogFooter>
            <Button variant="outline" onClick={() => setMovementDialogOpen(false)}>Cancel</Button>
            <Button onClick={handleSaveMovement}>Save Movement</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}