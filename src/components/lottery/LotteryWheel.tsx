import React, { useState, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { useTranslation } from "react-i18next";
import { RepairItem } from "@/types";
import { Trophy, RotateCcw, Volume2 } from "lucide-react";
import { useLotterySounds } from "@/hooks/useLotterySounds";

interface LotteryWheelProps {
  repairs: RepairItem[];
  onWinnerSelected: (winner: RepairItem) => void;
  onReset: () => void;
}

const LotteryWheel: React.FC<LotteryWheelProps> = ({
  repairs,
  onWinnerSelected,
  onReset,
}) => {
  const { t } = useTranslation();
  const [isSpinning, setIsSpinning] = useState(false);
  const [winner, setWinner] = useState<RepairItem | null>(null);
  const [rotation, setRotation] = useState(0);
  const [soundEnabled, setSoundEnabled] = useState(() => {
    const saved = localStorage.getItem("lottery-sound-enabled");
    return saved !== null ? JSON.parse(saved) : true;
  });

  const { playSpinSound, playWinnerSound, playClickSound, playDrumRoll } =
    useLotterySounds();

  // Save sound preference to localStorage
  const toggleSound = () => {
    const newValue = !soundEnabled;
    setSoundEnabled(newValue);
    localStorage.setItem("lottery-sound-enabled", JSON.stringify(newValue));
    if (newValue) playClickSound(); // Test sound when enabling
  };

  const spinWheel = () => {
    if (repairs.length === 0 || isSpinning) return;

    setIsSpinning(true);
    setWinner(null);

    // Play sounds if enabled
    if (soundEnabled) {
      playClickSound(); // Button click sound
      setTimeout(() => playDrumRoll(), 200); // Drum roll for suspense
      setTimeout(() => playSpinSound(), 500); // Spinning sound
    }

    // Generate random rotation (multiple full rotations + random position)
    const spins = 5 + Math.random() * 5; // 5-10 full rotations
    const finalRotation = spins * 360 + Math.random() * 360;
    setRotation(finalRotation);

    // Select random winner after animation
    setTimeout(() => {
      const randomIndex = Math.floor(Math.random() * repairs.length);
      const selectedWinner = repairs[randomIndex];
      setWinner(selectedWinner);
      setIsSpinning(false);

      // Play winner sound
      if (soundEnabled) {
        setTimeout(() => playWinnerSound(), 200);
      }

      onWinnerSelected(selectedWinner);
    }, 3000); // 3 seconds animation
  };

  const handleReset = () => {
    if (soundEnabled) {
      playClickSound();
    }
    setWinner(null);
    setRotation(0);
    setIsSpinning(false);
    onReset();
  };

  // Create wheel segments based on repairs
  const segmentAngle = 360 / Math.max(repairs.length, 1);
  const segments = repairs.map((repair, index) => ({
    repair,
    angle: index * segmentAngle,
    color: `hsl(${(index * 137.5) % 360}, 70%, 60%)`, // Golden ratio for color distribution
  }));

  return (
    <div className="flex flex-col items-center space-y-6">
      {/* Sound Toggle */}
      <div className="flex items-center justify-center">
        <Button
          variant="outline"
          size="sm"
          onClick={toggleSound}
          className="flex items-center gap-2"
        >
          <Volume2
            className={`h-4 w-4 ${
              soundEnabled ? "text-green-500" : "text-gray-400"
            }`}
          />
          {soundEnabled ? t("lottery.soundOn") : t("lottery.soundOff")}
        </Button>
      </div>

      {/* Wheel Container */}
      <div className="relative">
        {/* Pointer */}
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 -translate-y-2 z-10">
          <div className="w-0 h-0 border-l-4 border-r-4 border-b-8 border-l-transparent border-r-transparent border-b-red-500"></div>
        </div>

        {/* Wheel */}
        <div
          className="relative w-80 h-80 rounded-full border-8 border-gray-300 overflow-hidden transition-transform ease-out"
          style={{
            transform: `rotate(${rotation}deg)`,
            transitionDuration: isSpinning ? "3s" : "0.5s",
          }}
        >
          {repairs.length > 0 ? (
            <svg className="w-full h-full" viewBox="0 0 200 200">
              {segments.map((segment, index) => {
                const startAngle = (segment.angle * Math.PI) / 180;
                const endAngle =
                  ((segment.angle + segmentAngle) * Math.PI) / 180;

                const x1 = 100 + 90 * Math.cos(startAngle);
                const y1 = 100 + 90 * Math.sin(startAngle);
                const x2 = 100 + 90 * Math.cos(endAngle);
                const y2 = 100 + 90 * Math.sin(endAngle);

                const largeArcFlag = segmentAngle > 180 ? 1 : 0;

                const pathData = [
                  `M 100 100`,
                  `L ${x1} ${y1}`,
                  `A 90 90 0 ${largeArcFlag} 1 ${x2} ${y2}`,
                  `Z`,
                ].join(" ");

                const textAngle = segment.angle + segmentAngle / 2;
                const textRadius = 60;
                const textX =
                  100 + textRadius * Math.cos((textAngle * Math.PI) / 180);
                const textY =
                  100 + textRadius * Math.sin((textAngle * Math.PI) / 180);

                return (
                  <g key={segment.repair.id}>
                    <path
                      d={pathData}
                      fill={segment.color}
                      stroke="white"
                      strokeWidth="2"
                    />
                    <text
                      x={textX}
                      y={textY}
                      fill="white"
                      fontSize="10"
                      fontWeight="bold"
                      textAnchor="middle"
                      dominantBaseline="middle"
                      transform={`rotate(${textAngle}, ${textX}, ${textY})`}
                    >
                      #{segment.repair.ticketNumber}
                    </text>
                  </g>
                );
              })}
            </svg>
          ) : (
            <div className="flex items-center justify-center w-full h-full bg-gray-200 text-gray-500">
              {t("lottery.noRepairsInRange")}
            </div>
          )}
        </div>
      </div>

      {/* Controls */}
      <div className="flex flex-col items-center space-y-4">
        {!winner && (
          <Button
            onClick={spinWheel}
            disabled={isSpinning || repairs.length === 0}
            size="lg"
            className="px-8 py-3 text-lg font-bold"
          >
            {isSpinning ? (
              <>
                <RotateCcw className="mr-2 h-5 w-5 animate-spin" />
                {t("lottery.spinning")}
              </>
            ) : (
              <>
                <Trophy className="mr-2 h-5 w-5" />
                {t("lottery.spinWheel")}
              </>
            )}
          </Button>
        )}

        {winner && (
          <div className="text-center space-y-4 animate-winner-bounce">
            <Card className="p-6 bg-gradient-to-r from-yellow-400 to-orange-500 text-white shadow-2xl">
              <div className="flex items-center justify-center mb-4">
                <Trophy className="h-8 w-8 mr-2 animate-bounce" />
                <h2 className="text-2xl font-bold">{t("lottery.winner")}</h2>
                <Trophy className="h-8 w-8 ml-2 animate-bounce" />
              </div>
              <div className="space-y-2">
                <p className="text-lg">
                  <span className="font-semibold">
                    {t("lottery.ticketNumber")}
                  </span>
                  {winner.ticketNumber}
                </p>
                <p className="text-lg">
                  <span className="font-semibold">
                    {t("lottery.customer")}:{" "}
                  </span>
                  {winner.customerName}
                </p>
                <p className="text-sm opacity-90">
                  {winner.phoneModel} - {winner.problemDescription}
                </p>
              </div>
            </Card>
            <Button onClick={handleReset} variant="outline" size="lg">
              {t("lottery.newLottery")}
            </Button>
          </div>
        )}
      </div>

      {/* Entries Info */}
      {repairs.length > 0 && !winner && (
        <div className="text-center text-sm text-gray-600 dark:text-gray-400">
          {t("lottery.totalEntries")}: {repairs.length} {t("lottery.entries")}
        </div>
      )}
    </div>
  );
};

export default LotteryWheel;
