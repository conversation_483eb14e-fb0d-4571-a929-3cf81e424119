import React from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card } from "@/components/ui/card";
import { useTranslation } from "react-i18next";
import { Calendar, Play } from "lucide-react";
import { useLotterySounds } from "@/hooks/useLotterySounds";

interface DateRangePickerProps {
  startDate: string;
  endDate: string;
  onStartDateChange: (date: string) => void;
  onEndDateChange: (date: string) => void;
  onRunLottery: () => void;
  isLoading: boolean;
  totalRepairs: number;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  startDate,
  endDate,
  onStartDateChange,
  onEndDateChange,
  onRunLottery,
  isLoading,
  totalRepairs,
}) => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";
  const { playClickSound } = useLotterySounds();

  const isValidDateRange =
    startDate && endDate && new Date(startDate) <= new Date(endDate);

  const handleRunLottery = () => {
    playClickSound();
    onRunLottery();
  };

  return (
    <Card className="p-6" style={{ direction: isRTL ? "rtl" : "ltr" }}>
      <div className="space-y-4">
        <div className="flex items-center space-x-2 rtl:space-x-reverse">
          <Calendar className="h-5 w-5 text-blue-500" />
          <h3 className="text-lg font-semibold">
            {t("lottery.selectDateRange")}
          </h3>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="startDate">{t("lottery.startDate")}</Label>
            <Input
              id="startDate"
              type="date"
              value={startDate}
              onChange={(e) => onStartDateChange(e.target.value)}
              className="w-full"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="endDate">{t("lottery.endDate")}</Label>
            <Input
              id="endDate"
              type="date"
              value={endDate}
              onChange={(e) => onEndDateChange(e.target.value)}
              className="w-full"
              min={startDate}
            />
          </div>
        </div>

        {isValidDateRange && (
          <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              {t("lottery.totalEntries")}:{" "}
              <span className="font-semibold">{totalRepairs}</span>{" "}
              {t("lottery.entries")}
            </p>
          </div>
        )}

        <Button
          onClick={handleRunLottery}
          disabled={!isValidDateRange || isLoading || totalRepairs === 0}
          className="w-full"
          size="lg"
        >
          <Play className="mr-2 h-4 w-4" />
          {isLoading ? t("common.loading") : t("lottery.runLottery")}
        </Button>

        {!isValidDateRange && startDate && endDate && (
          <p className="text-sm text-red-500">
            {t("lottery.invalidDateRange")}
          </p>
        )}

        {isValidDateRange && totalRepairs === 0 && (
          <p className="text-sm text-yellow-600 dark:text-yellow-400">
            {t("lottery.noRepairsInRange")}
          </p>
        )}
      </div>
    </Card>
  );
};

export default DateRangePicker;
