
// src/components/QRScanner.tsx
import React, { useEffect, useRef, useState } from "react";
import { Html5Qrcode } from "html5-qrcode";
import { Button } from "@/components/ui/button";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Camera } from "lucide-react";
import { useTranslation } from "react-i18next";

interface QRScannerProps {
  onScan: (value: string) => void;
}

const QRScanner: React.FC<QRScannerProps> = ({ onScan }) => {
  const scannerRef = useRef<Html5Qrcode | null>(null);
  const [cameraActive, setCameraActive] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { t } = useTranslation();

  const startScanner = async () => {
    try {
      const devices = await Html5Qrcode.getCameras();

      if (devices && devices.length) {
        const cameraId = devices[0].id;

        scannerRef.current = new Html5Qrcode("qr-reader");

        await scannerRef.current.start(
          cameraId,
          {
            fps: 10,
            qrbox: { width: 350, height: 350 },
          },
          (decodedText) => {
            const audio = new Audio("beep.mp3");
            audio.play(); // 🔊 Play sound on scan

            stopScanner();
            onScan(decodedText);
          },
          (err) => {
            console.warn("QR Code scan error", err);
          }
        );
      } else {
        setError("No camera devices found.");
      }
    } catch (err: any) {
      setError("Failed to start camera: " + err.message);
    }
  };

  const stopScanner = () => {
    if (scannerRef.current) {
      scannerRef.current.stop().then(() => {
        scannerRef.current?.clear();
        scannerRef.current = null;
      });
    }
    setCameraActive(false);
  };

  const toggleCamera = () => {
    if (cameraActive) {
      stopScanner();
    } else {
      setError(null);
      setCameraActive(true);
      startScanner();
    }
  };

  useEffect(() => {
    return () => {
      stopScanner(); // Clean up on unmount
    };
  }, []);

  return (
    <div>
      <Button onClick={toggleCamera}>
        <Camera className="mr-2 h-4 w-4" />
        {cameraActive ? t("common.stopScanning") : t("common.startScanning")}
      </Button>

      {cameraActive && (
        <div className="relative aspect-square w-full max-w-md mx-auto mt-4 overflow-hidden rounded-lg shadow-lg">
          <div id="qr-reader" className="w-full h-full" />

          {/* 🔴 Scanning line */}
          <div className="absolute top-0 left-0 w-full h-full pointer-events-none">
            <div className="absolute top-0 left-0 w-full h-1 bg-red-500 animate-scan" />
          </div>

          {/* 🔲 Corner indicators */}
          <div className="absolute inset-0 pointer-events-none">
            <div className="absolute border-t-4 border-l-4 border-green-500 w-8 h-8 top-0 left-0" />
            <div className="absolute border-t-4 border-r-4 border-green-500 w-8 h-8 top-0 right-0" />
            <div className="absolute border-b-4 border-l-4 border-green-500 w-8 h-8 bottom-0 left-0" />
            <div className="absolute border-b-4 border-r-4 border-green-500 w-8 h-8 bottom-0 right-0" />
          </div>
        </div>
      )}

      {error && (
        <Alert variant="destructive" className="mt-4">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
    </div>
  );
};

export default QRScanner;
