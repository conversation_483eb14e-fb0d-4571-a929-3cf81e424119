import React from "react";
import { Link, useLocation } from "react-router-dom";
import { ChevronRight, Home } from "lucide-react";
import { useTranslation } from "react-i18next";

const PremiumBreadcrumb: React.FC = () => {
  const location = useLocation();
  const { t } = useTranslation();
  
  const pathSegments = location.pathname.split('/').filter(Boolean);
  
  const getBreadcrumbName = (segment: string, index: number) => {
    const pathMap: Record<string, string> = {
      'new-repair': t('common.newRepair'),
      'search': t('common.search'),
      'stock': t('common.stock'),
      'users': t('userManagement.title'),
      'time-tracking': t('timeTracking.title'),
      'advanced-stock': t('advancedStock.title'),
      'custom-forms': t('customForms.title'),
      'repair': t('repair.repairDetails'),
    };
    
    return pathMap[segment] || segment;
  };

  if (pathSegments.length === 0) {
    return (
      <div className="flex items-center space-x-2 text-sm text-muted-foreground">
        <Home className="h-4 w-4" />
        <span>{t('common.dashboard')}</span>
      </div>
    );
  }

  return (
    <div className="flex items-center space-x-2 text-sm">
      <Link to="/" className="flex items-center text-muted-foreground hover:text-foreground">
        <Home className="h-4 w-4" />
      </Link>
      
      {pathSegments.map((segment, index) => {
        const path = '/' + pathSegments.slice(0, index + 1).join('/');
        const isLast = index === pathSegments.length - 1;
        
        return (
          <React.Fragment key={path}>
            <ChevronRight className="h-4 w-4 text-muted-foreground" />
            {isLast ? (
              <span className="font-medium text-foreground">
                {getBreadcrumbName(segment, index)}
              </span>
            ) : (
              <Link 
                to={path} 
                className="text-muted-foreground hover:text-foreground"
              >
                {getBreadcrumbName(segment, index)}
              </Link>
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
};

export default PremiumBreadcrumb;