import React, { useState, useEffect } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { AlertCircle, CheckCircle, Users, Wrench } from "lucide-react";

interface TestResult {
  test: string;
  status: "pass" | "fail" | "pending";
  message: string;
  details?: any;
}

const AdminAccessTest: React.FC = () => {
  const { user, roles } = useAuth();
  const { repairShop } = useRepairShopContext();
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const runTests = async () => {
    if (!user || !repairShop) return;

    setIsRunning(true);
    const results: TestResult[] = [];

    try {
      // Test 1: Check user role
      const userRole = roles.find(r => r.repair_shop_id === repairShop.id)?.role;
      results.push({
        test: "User Role Check",
        status: userRole ? "pass" : "fail",
        message: userRole ? `User has role: ${userRole}` : "No role found for current repair shop",
        details: { role: userRole, userId: user.id, shopId: repairShop.id }
      });

      // Test 2: Check repair access via direct query
      const { data: repairsData, error: repairsError } = await supabase
        .from("repairs")
        .select("id, customer_name, status, user_id, assigned_technician")
        .eq("repair_shop_id", repairShop.id);

      if (repairsError) {
        results.push({
          test: "Repair Access Test",
          status: "fail",
          message: `Database error: ${repairsError.message}`,
          details: repairsError
        });
      } else {
        results.push({
          test: "Repair Access Test",
          status: "pass",
          message: `Can access ${repairsData.length} repairs`,
          details: { 
            repairCount: repairsData.length,
            ownRepairs: repairsData.filter(r => r.user_id === user.id).length,
            assignedRepairs: repairsData.filter(r => r.assigned_technician === user.id).length
          }
        });
      }

      // Test 3: Check user_repair_shops access
      const { data: userShopsData, error: userShopsError } = await supabase
        .from("user_repair_shops")
        .select("*")
        .eq("user_id", user.id);

      if (userShopsError) {
        results.push({
          test: "User Shop Association Test",
          status: "fail",
          message: `Error accessing user shops: ${userShopsError.message}`,
          details: userShopsError
        });
      } else {
        results.push({
          test: "User Shop Association Test",
          status: "pass",
          message: `User belongs to ${userShopsData.length} repair shop(s)`,
          details: userShopsData
        });
      }

      // Test 4: Check if RLS policies are working correctly
      const { data: allUsersData, error: allUsersError } = await supabase
        .from("user_repair_shops")
        .select("*");

      results.push({
        test: "RLS Policy Test",
        status: allUsersError ? "fail" : "pass",
        message: allUsersError 
          ? "RLS is working - cannot see other users' data" 
          : `Can see ${allUsersData?.length || 0} user associations (this might indicate RLS issues if too many)`,
        details: { error: allUsersError?.message, count: allUsersData?.length }
      });

    } catch (error) {
      results.push({
        test: "General Error",
        status: "fail",
        message: `Unexpected error: ${error}`,
        details: error
      });
    }

    setTestResults(results);
    setIsRunning(false);
  };

  const getStatusIcon = (status: TestResult["status"]) => {
    switch (status) {
      case "pass":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "fail":
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
    }
  };

  const getStatusColor = (status: TestResult["status"]) => {
    switch (status) {
      case "pass":
        return "bg-green-100 text-green-800";
      case "fail":
        return "bg-red-100 text-red-800";
      default:
        return "bg-yellow-100 text-yellow-800";
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Users className="h-5 w-5" />
          Administrator Access Test
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          This tool helps diagnose role-based access control issues for administrators.
        </p>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-4">
          <Button 
            onClick={runTests} 
            disabled={isRunning || !user || !repairShop}
            className="flex items-center gap-2"
          >
            <Wrench className="h-4 w-4" />
            {isRunning ? "Running Tests..." : "Run Access Tests"}
          </Button>
          
          {user && repairShop && (
            <div className="text-sm text-muted-foreground">
              Testing as: {user.email} in shop: {repairShop.name}
            </div>
          )}
        </div>

        {testResults.length > 0 && (
          <div className="space-y-3">
            <h3 className="font-semibold">Test Results:</h3>
            {testResults.map((result, index) => (
              <div key={index} className="border rounded-lg p-4">
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2">
                    {getStatusIcon(result.status)}
                    <span className="font-medium">{result.test}</span>
                  </div>
                  <Badge className={getStatusColor(result.status)}>
                    {result.status.toUpperCase()}
                  </Badge>
                </div>
                <p className="text-sm text-muted-foreground mb-2">{result.message}</p>
                {result.details && (
                  <details className="text-xs">
                    <summary className="cursor-pointer text-muted-foreground">
                      View Details
                    </summary>
                    <pre className="mt-2 p-2 bg-muted rounded text-xs overflow-auto">
                      {JSON.stringify(result.details, null, 2)}
                    </pre>
                  </details>
                )}
              </div>
            ))}
          </div>
        )}

        <div className="mt-6 p-4 bg-blue-50 rounded-lg">
          <h4 className="font-semibold text-blue-900 mb-2">Expected Results for Administrators:</h4>
          <ul className="text-sm text-blue-800 space-y-1">
            <li>• User Role Check: Should show "administrator"</li>
            <li>• Repair Access Test: Should show ALL repairs in the shop (not just own/assigned)</li>
            <li>• User Shop Association Test: Should show repair shop membership</li>
            <li>• RLS Policy Test: Should work correctly (limited access to user associations)</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
};

export default AdminAccessTest;
