import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogHeader,
  <PERSON><PERSON>Title,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Mail,
  Send,
  AlertCircle,
  CheckCircle,
  Loader2,
  Eye,
  EyeOff,
} from "lucide-react";
import { RepairItem } from "@/types";
import { useRepairShopContext } from "@/context/RepairShopContext";
import {
  generateCustomEmailTemplate,
  generateRepairCompletionEmailTemplate,
  sendEmail,
  isValidEmail,
} from "@/utils/emailService";
import { toast } from "sonner";

interface EmailDialogProps {
  repair: RepairItem;
  trigger?: React.ReactNode;
  defaultTemplate?: "completion" | "custom";
}

const EmailDialog: React.FC<EmailDialogProps> = ({
  repair,
  trigger,
  defaultTemplate = "custom",
}) => {
  const { t } = useTranslation();
  const { repairShop } = useRepairShopContext();
  const [open, setOpen] = useState(false);
  const [emailAddress, setEmailAddress] = useState(repair.customerEmail || "");
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [templateType, setTemplateType] = useState<"completion" | "custom">(
    defaultTemplate
  );
  const [isSending, setIsSending] = useState(false);
  const [showPreview, setShowPreview] = useState(false);

  const isEmailValid = isValidEmail(emailAddress);
  const canSend =
    isEmailValid &&
    (templateType === "completion" || (subject.trim() && message.trim()));

  const handleSendEmail = async () => {
    if (!canSend) return;

    try {
      setIsSending(true);

      const emailTemplate =
        templateType === "completion"
          ? generateRepairCompletionEmailTemplate(repair, repairShop)
          : generateCustomEmailTemplate(repair, subject, message, repairShop);

      const result = await sendEmail(emailAddress, emailTemplate);

      if (result.success) {
        toast.success(t("repair.emailSentSuccess"));
        setOpen(false);
        // Reset form
        setSubject("");
        setMessage("");
        setShowPreview(false);
      } else {
        toast.error(
          t("repair.emailSentError") + ": " + (result.error || "Unknown error")
        );
      }
    } catch (error) {
      console.error("Error sending email:", error);
      toast.error(t("repair.emailSentError"));
    } finally {
      setIsSending(false);
    }
  };

  const getPreviewTemplate = () => {
    if (templateType === "completion") {
      return generateRepairCompletionEmailTemplate(repair, repairShop);
    } else {
      return generateCustomEmailTemplate(repair, subject, message, repairShop);
    }
  };

  const defaultTrigger = (
    <Button variant="outline" size="sm" className="flex items-center gap-2">
      <Mail className="h-4 w-4" />
      {t("repair.sendEmail")}
    </Button>
  );

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>{trigger || defaultTrigger}</DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Mail className="h-5 w-5" />
            {t("repair.sendEmailToCustomer")} - {repair.customerName}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* Repair Info */}
          <Card>
            <CardContent className="p-4">
              <div className="flex justify-between items-start">
                <div>
                  <h3 className="font-semibold">{repair.customerName}</h3>
                  <p className="text-sm text-gray-600">{repair.phoneModel}</p>
                  <p className="text-sm font-mono">
                    Ticket #{repair.ticketNumber || repair.id.slice(-6)}
                  </p>
                </div>
                <Badge
                  variant={
                    repair.status === "completed" ? "default" : "secondary"
                  }
                >
                  {repair.status}
                </Badge>
              </div>
            </CardContent>
          </Card>

          {/* Email Address */}
          <div className="space-y-2">
            <Label htmlFor="emailAddress">{t("repair.customerEmail")}</Label>
            <div className="relative">
              <Mail className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
              <Input
                id="emailAddress"
                type="email"
                value={emailAddress}
                onChange={(e) => setEmailAddress(e.target.value)}
                placeholder="<EMAIL>"
                className={`pl-10 ${
                  !isEmailValid && emailAddress ? "border-red-500" : ""
                }`}
              />
            </div>
            {!isEmailValid && emailAddress && (
              <div className="flex items-center gap-2 text-red-600 text-sm">
                <AlertCircle className="h-4 w-4" />
                {t("repair.invalidEmailAddress")}
              </div>
            )}
          </div>

          {/* Template Type Selection */}
          <div className="space-y-3">
            <Label>{t("repair.emailTemplate")}</Label>
            <div className="flex gap-3">
              <Button
                variant={templateType === "completion" ? "default" : "outline"}
                onClick={() => setTemplateType("completion")}
                className="flex-1"
              >
                {t("repair.completionTemplate")}
              </Button>
              <Button
                variant={templateType === "custom" ? "default" : "outline"}
                onClick={() => setTemplateType("custom")}
                className="flex-1"
              >
                {t("repair.customTemplate")}
              </Button>
            </div>
          </div>

          {/* Custom Email Fields */}
          {templateType === "custom" && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="subject">{t("repair.emailSubject")}</Label>
                <Input
                  id="subject"
                  value={subject}
                  onChange={(e) => setSubject(e.target.value)}
                  placeholder={t("repair.enterEmailSubject")}
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="message">{t("repair.emailMessage")}</Label>
                <Textarea
                  id="message"
                  value={message}
                  onChange={(e) => setMessage(e.target.value)}
                  placeholder={t("repair.enterEmailMessage")}
                  rows={6}
                />
              </div>
            </div>
          )}

          {/* Preview Toggle */}
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              onClick={() => setShowPreview(!showPreview)}
              className="flex items-center gap-2"
            >
              {showPreview ? (
                <EyeOff className="h-4 w-4" />
              ) : (
                <Eye className="h-4 w-4" />
              )}
              {showPreview ? t("repair.hidePreview") : t("repair.showPreview")}
            </Button>
          </div>

          {/* Email Preview */}
          {showPreview && canSend && (
            <Card>
              <CardContent className="p-4">
                <h4 className="font-semibold mb-3">
                  {t("repair.emailPreview")}
                </h4>
                <div className="space-y-3 text-sm">
                  <div>
                    <strong>{t("repair.to")}:</strong> {emailAddress}
                  </div>
                  <div>
                    <strong>{t("repair.subject")}:</strong>{" "}
                    {getPreviewTemplate().subject}
                  </div>
                  <div className="border-t pt-3">
                    <div
                      className="prose prose-sm max-w-none"
                      dangerouslySetInnerHTML={{
                        __html: getPreviewTemplate().htmlBody,
                      }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          )}

          {/* Actions */}
          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setOpen(false)}>
              {t("common.cancel")}
            </Button>

            <Button
              onClick={handleSendEmail}
              disabled={!canSend || isSending}
              className="flex items-center gap-2"
            >
              {isSending ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  {t("repair.sending")}
                </>
              ) : (
                <>
                  <Send className="h-4 w-4" />
                  {t("repair.sendEmail")}
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default EmailDialog;
