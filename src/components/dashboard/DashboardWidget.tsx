import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { X, Grip } from "lucide-react";
import { WidgetConfig } from "@/types/dashboard";
import RepairStatusWidget from "./widgets/RepairStatusWidget";
import RecentRepairsWidget from "./widgets/RecentRepairsWidget";
import RepairsByStatusWidget from "./widgets/RepairsByStatusWidget";
import RepairsByDeviceWidget from "./widgets/RepairsByDeviceWidget";
import WelcomeWidget from "./widgets/WelcomeWidget";
import PaymentStatusWidget from "./widgets/PaymentStatusWidget";
import IncomeWidget from "./widgets/IncomeWidget";

interface DashboardWidgetProps {
  widget: WidgetConfig;
  isEditing: boolean;
  onRemove: (id: string) => void;
}

const DashboardWidget: React.FC<DashboardWidgetProps> = ({
  widget,
  isEditing,
  onRemove,
}) => {
  // Render the appropriate widget content based on type
  const renderWidgetContent = () => {
    switch (widget.type) {
      case "repair-status":
        return <RepairStatusWidget />;
      case "recent-repairs":
        return <RecentRepairsWidget />;
      case "repairs-by-status":
        return <RepairsByStatusWidget />;
      case "repairs-by-device":
        return <RepairsByDeviceWidget />;
      case "welcome":
        return <WelcomeWidget />;
      case "payment-status":
        return <PaymentStatusWidget />;
      case "income":
        return <IncomeWidget />;
      default:
        return <div>Unknown widget type</div>;
    }
  };

  return (
    <Card
      className="h-full overflow-hidden shadow-md border border-gray-200 dark:border-gray-700"
      style={{ direction: "ltr" }}
    >
      <CardHeader className="p-3 bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-800 dark:to-gray-700">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            {isEditing && (
              <Grip className="h-4 w-4 text-gray-400 cursor-move" />
            )}
            <CardTitle
              className="text-sm font-medium"
              style={{ direction: "inherit" }}
            >
              {widget.title}
            </CardTitle>
          </div>
          {isEditing && (
            <Button
              variant="ghost"
              size="icon"
              className="h-6 w-6 rounded-full"
              onClick={() => onRemove(widget.id)}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent
        className="p-4 h-[calc(100%-40px)] overflow-auto"
        style={{ direction: "inherit" }}
      >
        {renderWidgetContent()}
      </CardContent>
    </Card>
  );
};

export default DashboardWidget;
