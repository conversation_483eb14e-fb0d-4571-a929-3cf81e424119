import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { X, Grip } from "lucide-react";
import { WidgetConfig } from "@/types/dashboard";
import { useTheme } from "@/context/ThemeContext";
import { useAuth } from "@/context/AuthContext";
import RepairStatusWidget from "./widgets/RepairStatusWidget";
import RecentRepairsWidget from "./widgets/RecentRepairsWidget";
import RepairsByStatusWidget from "./widgets/RepairsByStatusWidget";
import RepairsByDeviceWidget from "./widgets/RepairsByDeviceWidget";
import WelcomeWidget from "./widgets/WelcomeWidget";
import PaymentStatusWidget from "./widgets/PaymentStatusWidget";
import IncomeWidget from "./widgets/IncomeWidget";

interface DashboardWidgetProps {
  widget: WidgetConfig;
  isEditing: boolean;
  onRemove: (id: string) => void;
}

const DashboardWidget: React.FC<DashboardWidgetProps> = ({
  widget,
  isEditing,
  onRemove,
}) => {
  const { isPremium } = useTheme();
  const { user, roles } = useAuth();
  
  // Render the appropriate widget content based on type
  const renderWidgetContent = () => {
    switch (widget.type) {
      case "repair-status":
        return <RepairStatusWidget />;
      case "recent-repairs":
        return <RecentRepairsWidget />;
      case "repairs-by-status":
        return <RepairsByStatusWidget />;
      case "repairs-by-device":
        return <RepairsByDeviceWidget />;
      case "welcome":
        return <WelcomeWidget />;
      case "payment-status":
        return <PaymentStatusWidget />;
      case "income":
        // Only show income widget for administrators
        return roles.some(role => role.role === 'administrator') ? <IncomeWidget /> : <div className="flex items-center justify-center h-full text-gray-500">Access restricted to administrators</div>;
      default:
        return <div>Unknown widget type</div>;
    }
  };

  return (
    <Card
      className="h-full overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm ring-2 ring-blue-500/20"
      style={{ direction: "ltr" }}
    >
      <CardHeader className="p-4 bg-gradient-to-r from-blue-50/50 to-purple-50/50 dark:from-blue-900/20 dark:to-purple-900/20 border-b border-gray-100/50 dark:border-gray-700/50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            {isEditing && (
              <div className="p-1 rounded-md bg-gray-200/50 dark:bg-gray-700/50">
                <Grip className="h-4 w-4 text-gray-400 cursor-move" />
              </div>
            )}
            <CardTitle
              className="text-sm font-semibold text-gray-700 dark:text-gray-200"
              style={{ direction: "inherit" }}
            >
              {widget.title}
            </CardTitle>
          </div>
          {isEditing && (
            <Button
              variant="ghost"
              size="icon"
              className="h-7 w-7 rounded-full hover:bg-red-100 dark:hover:bg-red-900/30 hover:text-red-600 transition-colors duration-200"
              onClick={() => onRemove(widget.id)}
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent
        className="p-4 h-[calc(100%-60px)] overflow-auto"
        style={{ direction: "inherit" }}
      >
        {renderWidgetContent()}
      </CardContent>
    </Card>
  );
};

export default DashboardWidget;
