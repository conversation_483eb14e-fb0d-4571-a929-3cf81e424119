import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { WidgetType } from "@/types/dashboard";

interface AddWidgetDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onAddWidget: (type: WidgetType, title: string) => void;
}

const AddWidgetDialog: React.FC<AddWidgetDialogProps> = ({
  open,
  onOpenChange,
  onAddWidget,
}) => {
  const { t } = useTranslation();
  const [widgetType, setWidgetType] = useState<WidgetType>("repair-status");
  const [widgetTitle, setWidgetTitle] = useState("");

  const handleAddWidget = () => {
    if (!widgetTitle.trim()) {
      setWidgetTitle(t(`dashboard.widgets.${widgetType}.defaultTitle`));
    }

    onAddWidget(
      widgetType,
      widgetTitle.trim() || t(`dashboard.widgets.${widgetType}.defaultTitle`)
    );

    // Reset form
    setWidgetType("repair-status");
    setWidgetTitle("");
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>{t("dashboard.addWidget")}</DialogTitle>
          <DialogDescription>
            {t("dashboard.addWidgetDescription")}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="widget-type">{t("dashboard.widgetType")}</Label>
            <Select
              value={widgetType}
              onValueChange={(value) => setWidgetType(value as WidgetType)}
            >
              <SelectTrigger id="widget-type">
                <SelectValue placeholder={t("dashboard.selectWidgetType")} />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="repair-status">
                  {t("dashboard.widgets.repair-status.title")}
                </SelectItem>
                <SelectItem value="recent-repairs">
                  {t("dashboard.widgets.recent-repairs.title")}
                </SelectItem>
                <SelectItem value="repairs-by-status">
                  {t("dashboard.widgets.repairs-by-status.title")}
                </SelectItem>
                <SelectItem value="repairs-by-device">
                  {t("dashboard.widgets.repairs-by-device.title")}
                </SelectItem>
                <SelectItem value="payment-status">
                  {t("dashboard.widgets.payment-status.title")}
                </SelectItem>
                <SelectItem value="income">
                  {t("dashboard.widgets.income.title")}
                </SelectItem>
                <SelectItem value="welcome">
                  {t("dashboard.widgets.welcome.title")}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="grid gap-2">
            <Label htmlFor="widget-title">{t("dashboard.widgetTitle")}</Label>
            <Input
              id="widget-title"
              value={widgetTitle}
              onChange={(e) => setWidgetTitle(e.target.value)}
              placeholder={t(`dashboard.widgets.${widgetType}.defaultTitle`)}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            {t("common.cancel")}
          </Button>
          <Button onClick={handleAddWidget}>{t("dashboard.addWidget")}</Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default AddWidgetDialog;
