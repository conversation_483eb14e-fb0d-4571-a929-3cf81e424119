import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";
import { useRepairContext } from "@/context/RepairContext";
import { Loader2 } from "lucide-react";
import { Progress } from "@/components/ui/progress";

const RepairStatusWidget: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const { repairs } = useRepairContext();
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    pending: 0,
    inProgress: 0,
    completed: 0,
    cancelled: 0,
    returned: 0,
    total: 0,
  });

  useEffect(() => {
    if (repairs) {
      // Calculate stats
      const newStats = {
        pending: repairs.filter((r) => r.status === "pending").length,
        inProgress: repairs.filter((r) => r.status === "inProgress").length,
        completed: repairs.filter((r) => r.status === "completed").length,
        cancelled: repairs.filter((r) => r.status === "cancelled").length,
        returned: repairs.filter((r) => r.status === "returned").length,
        total: repairs.length,
      };
      setStats(newStats);
      setLoading(false);
    }
  }, [repairs]);

  // Handle click on status cards
  const handleStatusClick = (status: string) => {
    // Navigate to search page with status filter
    navigate(`/search?status=${status}`);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div
          className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg cursor-pointer hover:bg-blue-100 dark:hover:bg-blue-900/30 transition-colors"
          onClick={() => handleStatusClick("pending")}
          title={t("common.clickToView")}
        >
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {t("repair.status.pending")}
          </div>
          <div className="text-2xl font-bold">{stats.pending}</div>
          <Progress
            value={(stats.pending / stats.total) * 100}
            className="h-1 mt-2"
          />
        </div>
        <div
          className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg cursor-pointer hover:bg-yellow-100 dark:hover:bg-yellow-900/30 transition-colors"
          onClick={() => handleStatusClick("inProgress")}
          title={t("common.clickToView")}
        >
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {t("repair.status.inProgress")}
          </div>
          <div className="text-2xl font-bold">{stats.inProgress}</div>
          <Progress
            value={(stats.inProgress / stats.total) * 100}
            className="h-1 mt-2 bg-yellow-100 dark:bg-yellow-900/30"
          />
        </div>
        <div
          className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg cursor-pointer hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors"
          onClick={() => handleStatusClick("completed")}
          title={t("common.clickToView")}
        >
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {t("repair.status.completed")}
          </div>
          <div className="text-2xl font-bold">{stats.completed}</div>
          <Progress
            value={(stats.completed / stats.total) * 100}
            className="h-1 mt-2 bg-green-100 dark:bg-green-900/30"
          />
        </div>
        <div
          className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg cursor-pointer hover:bg-purple-100 dark:hover:bg-purple-900/30 transition-colors"
          onClick={() => handleStatusClick("returned")}
          title={t("common.clickToView")}
        >
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {t("repair.status.returned")}
          </div>
          <div className="text-2xl font-bold">{stats.returned}</div>
          <Progress
            value={(stats.returned / stats.total) * 100}
            className="h-1 mt-2 bg-purple-100 dark:bg-purple-900/30"
          />
        </div>
      </div>

      <div className="mt-4 text-center text-sm text-gray-500 dark:text-gray-400">
        {t("dashboard.totalRepairs")}:{" "}
        <span className="font-bold">{stats.total}</span>
      </div>
    </div>
  );
};

export default RepairStatusWidget;
