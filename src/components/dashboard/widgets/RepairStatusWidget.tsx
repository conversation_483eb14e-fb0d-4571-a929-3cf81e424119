import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useRepairContext } from "@/context/RepairContext";
import { Loader2 } from "lucide-react";
import { Progress } from "@/components/ui/progress";

const RepairStatusWidget: React.FC = () => {
  const { t } = useTranslation();
  const { repairs, loading } = useRepairContext();

  // Memoize the stats computation
  const stats = useMemo(() => {
    if (!repairs || repairs.length === 0) {
      return {
        pending: 0,
        inProgress: 0,
        completed: 0,
        cancelled: 0,
        returned: 0,
        total: 0,
      };
    }

    console.log("Computing repair status stats");

    // Calculate stats efficiently in a single pass
    const newStats = {
      pending: 0,
      inProgress: 0,
      completed: 0,
      cancelled: 0,
      returned: 0,
      total: repairs.length,
    };

    repairs.forEach((repair) => {
      switch (repair.status) {
        case "pending":
          newStats.pending++;
          break;
        case "inProgress":
          newStats.inProgress++;
          break;
        case "completed":
          newStats.completed++;
          break;
        case "cancelled":
          newStats.cancelled++;
          break;
        case "returned":
          newStats.returned++;
          break;
      }
    });

    return newStats;
  }, [repairs]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4">
        <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {t("repair.status.pending")}
          </div>
          <div className="text-2xl font-bold">{stats.pending}</div>
          <Progress
            value={(stats.pending / stats.total) * 100}
            className="h-1 mt-2"
          />
        </div>
        <div className="bg-yellow-50 dark:bg-yellow-900/20 p-3 rounded-lg">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {t("repair.status.inProgress")}
          </div>
          <div className="text-2xl font-bold">{stats.inProgress}</div>
          <Progress
            value={(stats.inProgress / stats.total) * 100}
            className="h-1 mt-2 bg-yellow-100 dark:bg-yellow-900/30"
          />
        </div>
        <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {t("repair.status.completed")}
          </div>
          <div className="text-2xl font-bold">{stats.completed}</div>
          <Progress
            value={(stats.completed / stats.total) * 100}
            className="h-1 mt-2 bg-green-100 dark:bg-green-900/30"
          />
        </div>
        <div className="bg-purple-50 dark:bg-purple-900/20 p-3 rounded-lg">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {t("repair.status.returned")}
          </div>
          <div className="text-2xl font-bold">{stats.returned}</div>
          <Progress
            value={(stats.returned / stats.total) * 100}
            className="h-1 mt-2 bg-purple-100 dark:bg-purple-900/30"
          />
        </div>
      </div>

      <div className="mt-4 text-center text-sm text-gray-500 dark:text-gray-400">
        {t("dashboard.totalRepairs")}:{" "}
        <span className="font-bold">{stats.total}</span>
      </div>
    </div>
  );
};

export default RepairStatusWidget;
