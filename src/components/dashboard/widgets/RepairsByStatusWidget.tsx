import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useRepairContext } from "@/context/RepairContext";
import { Loader2 } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from "recharts";

interface ChartDataItem {
  name: string;
  value: number;
  color: string;
}

const RepairsByStatusWidget: React.FC = () => {
  const { t } = useTranslation();
  const { repairs, loading } = useRepairContext();

  // Status colors for the chart - memoized to prevent recreation
  const statusColors: Record<string, string> = useMemo(
    () => ({
      pending: "#fbbf24", // yellow-400
      inProgress: "#3b82f6", // blue-500
      completed: "#10b981", // green-500
      cancelled: "#ef4444", // red-500
      returned: "#8b5cf6", // purple-500
    }),
    []
  );

  // Memoize the chart data computation
  const data = useMemo((): ChartDataItem[] => {
    if (!repairs || repairs.length === 0) return [];

    // Count repairs by status
    const counts: Record<string, number> = {
      pending: 0,
      inProgress: 0,
      completed: 0,
      cancelled: 0,
      returned: 0,
    };

    repairs.forEach((repair) => {
      if (repair.status in counts) {
        counts[repair.status]++;
      }
    });

    // Convert to chart data format
    return Object.entries(counts)
      .filter(([_, count]) => count > 0) // Only include statuses with repairs
      .map(([status, count]) => ({
        name: t(`repair.status.${status}`),
        value: count,
        color: statusColors[status],
      }));
  }, [repairs, t, statusColors]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="text-center text-gray-500 dark:text-gray-400 py-4">
        {t("dashboard.noRepairsFound")}
      </div>
    );
  }

  return (
    <div className="h-full" dir="ltr">
      {" "}
      {/* Force LTR direction for chart */}
      <ResponsiveContainer width="100%" height={200}>
        <PieChart>
          <Pie
            data={data}
            cx="50%"
            cy="50%"
            labelLine={false}
            outerRadius={80}
            fill="#8884d8"
            dataKey="value"
          >
            {data.map((entry, index) => (
              <Cell key={`cell-${index}`} fill={entry.color} />
            ))}
          </Pie>
          <Tooltip
            formatter={(value: number) => [value, t("dashboard.repairs")]}
            contentStyle={{
              backgroundColor: "rgba(255, 255, 255, 0.9)",
              borderRadius: "4px",
              border: "1px solid #e2e8f0",
              padding: "8px",
            }}
          />
          <Legend />
        </PieChart>
      </ResponsiveContainer>
    </div>
  );
};

export default RepairsByStatusWidget;
