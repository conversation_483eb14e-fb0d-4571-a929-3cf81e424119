import React, { useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useRepairContext } from "@/context/RepairContext";
import { Loader2, TrendingUp, DollarSign, Calendar } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON>Axis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";
import {
  format,
  startOfMonth,
  endOfMonth,
  eachDayOfInterval,
  isSameDay,
  subMonths,
} from "date-fns";
import { RepairItem } from "@/types";

// Helper function to calculate the final price of a repair including modifications
const calculateFinalPrice = (repair: RepairItem): number => {
  // Start with the base repair price
  let finalPrice = repair.repairPrice || 0;

  // Add all price modifications if they exist
  if (repair.priceModifications && repair.priceModifications.length > 0) {
    const modificationsTotal = repair.priceModifications.reduce(
      (sum, mod) => sum + (mod.amount || 0),
      0
    );
    finalPrice += modificationsTotal;
  }

  return finalPrice;
};

interface IncomeData {
  date: string;
  amount: number;
  formattedDate: string;
}

const IncomeWidget: React.FC = () => {
  const { t } = useTranslation();
  const { repairs, loading } = useRepairContext();

  // Memoize the income calculations
  const { data, stats } = useMemo(() => {
    if (!repairs || repairs.length === 0) {
      return {
        data: [],
        stats: {
          totalIncome: 0,
          monthlyIncome: 0,
          previousMonthIncome: 0,
          percentChange: 0,
        },
      };
    }

    // Get paid repairs - consider ALL repairs that are marked as paid
    // This ensures we capture all paid amounts regardless of repair status
    const paidRepairs = repairs.filter((r) => r.paymentStatus === "paid");

    // Calculate total income using the final price (including modifications)
    const totalIncome = paidRepairs.reduce((sum, repair) => {
      return sum + calculateFinalPrice(repair);
    }, 0);

    // Calculate current month income
    const now = new Date();
    const currentMonthStart = startOfMonth(now);
    const currentMonthEnd = endOfMonth(now);

    // For current month, use completedAt date for completed repairs with paid status
    const currentMonthRepairs = paidRepairs.filter(
      (r) =>
        r.completedAt &&
        new Date(r.completedAt) >= currentMonthStart &&
        new Date(r.completedAt) <= currentMonthEnd
    );

    const monthlyIncome = currentMonthRepairs.reduce((sum, repair) => {
      return sum + calculateFinalPrice(repair);
    }, 0);

    // Calculate previous month income
    const previousMonthStart = startOfMonth(subMonths(now, 1));
    const previousMonthEnd = endOfMonth(subMonths(now, 1));

    // For previous month, use completedAt date for completed repairs with paid status
    const previousMonthRepairs = paidRepairs.filter(
      (r) =>
        r.completedAt &&
        new Date(r.completedAt) >= previousMonthStart &&
        new Date(r.completedAt) <= previousMonthEnd
    );

    const previousMonthIncome = previousMonthRepairs.reduce((sum, repair) => {
      return sum + calculateFinalPrice(repair);
    }, 0);

    // Calculate percent change
    const percentChange =
      previousMonthIncome === 0
        ? 100
        : ((monthlyIncome - previousMonthIncome) / previousMonthIncome) * 100;

    // Prepare chart data for current month
    const daysInMonth = eachDayOfInterval({
      start: currentMonthStart,
      end: currentMonthEnd,
    });

    const dailyData = daysInMonth.map((day) => {
      const dayRepairs = paidRepairs.filter(
        (r) => r.completedAt && isSameDay(new Date(r.completedAt), day)
      );

      const amount = dayRepairs.reduce((sum, repair) => {
        return sum + calculateFinalPrice(repair);
      }, 0);

      return {
        date: format(day, "yyyy-MM-dd"),
        formattedDate: format(day, "dd"),
        amount,
      };
    });

    // Group by week to reduce data points
    const weeklyData: IncomeData[] = [];
    for (let i = 0; i < dailyData.length; i += 7) {
      const weekData = dailyData.slice(i, i + 7);
      const weekAmount = weekData.reduce((sum, day) => sum + day.amount, 0);
      const startDate = weekData[0].formattedDate;
      const endDate = weekData[weekData.length - 1].formattedDate;

      weeklyData.push({
        date: weekData[0].date,
        formattedDate: `${startDate}-${endDate}`,
        amount: weekAmount,
      });
    }

    return {
      data: weeklyData,
      stats: {
        totalIncome,
        monthlyIncome,
        previousMonthIncome,
        percentChange,
      },
    };
  }, [repairs]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  const formatCurrency = (value: number) => {
    return new Intl.NumberFormat("ar-TN", {
      style: "currency",
      currency: "TND",
      maximumFractionDigits: 3,
    }).format(value);
  };

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-2 gap-4" dir="ltr">
        {" "}
        {/* Force LTR direction for grid layout */}
        <div className="bg-blue-50 dark:bg-blue-900/20 p-3 rounded-lg">
          <div className="flex items-center">
            <Calendar className="h-4 w-4 mr-1 text-blue-500" />
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {t("dashboard.monthlyIncome")}
            </div>
          </div>
          <div className="text-2xl font-bold">
            {formatCurrency(stats.monthlyIncome)}
          </div>
          <div className="flex items-center text-xs mt-1">
            {stats.percentChange > 0 ? (
              <TrendingUp className="h-3 w-3 mr-1 text-green-500" />
            ) : (
              <TrendingUp className="h-3 w-3 mr-1 text-red-500 transform rotate-180" />
            )}
            <span
              className={
                stats.percentChange > 0 ? "text-green-500" : "text-red-500"
              }
            >
              {stats.percentChange.toFixed(1)}%{" "}
              {t(
                stats.percentChange > 0
                  ? "dashboard.increase"
                  : "dashboard.decrease"
              )}
            </span>
          </div>
        </div>
        <div className="bg-emerald-50 dark:bg-emerald-900/20 p-3 rounded-lg">
          <div className="flex items-center">
            <DollarSign className="h-4 w-4 mr-1 text-emerald-500" />
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {t("dashboard.totalIncome")}
            </div>
          </div>
          <div className="text-2xl font-bold">
            {formatCurrency(stats.totalIncome)}
          </div>
          <div className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            {t("dashboard.allTime")}
          </div>
        </div>
      </div>

      <div className="h-[120px] mt-4" dir="ltr">
        {" "}
        {/* Force LTR direction for chart */}
        <ResponsiveContainer width="100%" height="100%">
          <BarChart
            data={data}
            margin={{
              top: 5,
              right: 5,
              left: 0,
              bottom: 5,
            }}
          >
            <CartesianGrid strokeDasharray="3 3" vertical={false} />
            <XAxis dataKey="formattedDate" tick={{ fontSize: 10 }} />
            <YAxis
              tickFormatter={(value) => `${value} TND`}
              tick={{ fontSize: 10 }}
            />
            <Tooltip
              formatter={(value: number) => [
                formatCurrency(value),
                t("dashboard.income"),
              ]}
              contentStyle={{
                backgroundColor: "rgba(255, 255, 255, 0.9)",
                borderRadius: "4px",
                border: "1px solid #e2e8f0",
                padding: "8px",
              }}
            />
            <Bar dataKey="amount" fill="#3b82f6" radius={[4, 4, 0, 0]} />
          </BarChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default IncomeWidget;
