import React, { useEffect, useState, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { useRepairContext } from "@/context/RepairContext";
import { Loader2, Phone, Calendar, FileText } from "lucide-react";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { Link } from "react-router-dom";
import { RepairItem } from "@/types";

const RecentRepairsWidget: React.FC = () => {
  const { t } = useTranslation();
  const { repairs, loading: repairsLoading } = useRepairContext();

  // Status colors for badges - memoized to prevent recreation
  const statusColors: Record<string, string> = useMemo(
    () => ({
      pending:
        "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300",
      inProgress:
        "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300",
      completed:
        "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300",
      cancelled: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300",
      returned:
        "bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-300",
    }),
    []
  );

  // Memoize the computation of recent repairs
  const recentRepairs = useMemo(() => {
    if (!repairs || repairs.length === 0) return [];

    // Get the 5 most recent repairs
    return [...repairs]
      .sort(
        (a, b) =>
          new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
      )
      .slice(0, 5);
  }, [repairs]);

  if (repairsLoading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (recentRepairs.length === 0) {
    return (
      <div className="text-center text-gray-500 dark:text-gray-400 py-4">
        {t("dashboard.noRepairsFound")}
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {recentRepairs.map((repair) => (
        <Link
          key={repair.id}
          to={`/repair/${repair.id}`}
          className="block p-3 rounded-lg border border-gray-100 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors"
        >
          <div className="flex justify-between items-start">
            <div className="flex-1">
              <div className="font-medium">{repair.customerName}</div>
              <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mt-1">
                <Phone className="h-3 w-3 mr-1" />
                {repair.phoneModel}
              </div>
              <div className="flex items-start text-xs text-gray-600 dark:text-gray-400 mt-1">
                <FileText className="h-3 w-3 mr-1 mt-0.5 flex-shrink-0" />
                <span className="line-clamp-2 leading-tight">
                  {repair.problemDescription}
                </span>
              </div>
              <div className="flex items-center text-xs text-gray-400 dark:text-gray-500 mt-1">
                <Calendar className="h-3 w-3 mr-1" />
                {format(new Date(repair.createdAt), "dd/MM/yyyy HH:mm")}
              </div>
            </div>
            <Badge className={statusColors[repair.status]}>
              {t(`repair.status.${repair.status}`)}
            </Badge>
          </div>
        </Link>
      ))}
    </div>
  );
};

export default RecentRepairsWidget;
