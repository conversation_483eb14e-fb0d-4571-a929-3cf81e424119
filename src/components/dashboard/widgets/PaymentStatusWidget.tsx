import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useRepairContext } from "@/context/RepairContext";
import { Loader2, DollarSign, AlertCircle } from "lucide-react";
import {
  <PERSON><PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Legend,
  Tooltip,
} from "recharts";
import { Progress } from "@/components/ui/progress";
import { RepairItem, PriceModification } from "@/types";

// Helper function to calculate the final price of a repair including modifications
const calculateFinalPrice = (repair: RepairItem): number => {
  // Start with the base repair price
  let finalPrice = repair.repairPrice || 0;

  // Add all price modifications if they exist
  if (repair.priceModifications && repair.priceModifications.length > 0) {
    const modificationsTotal = repair.priceModifications.reduce(
      (sum, mod) => sum + (mod.amount || 0),
      0
    );
    finalPrice += modificationsTotal;
  }

  return finalPrice;
};

interface PaymentData {
  name: string;
  value: number;
  color: string;
}

const PaymentStatusWidget: React.FC = () => {
  const { t } = useTranslation();
  const { repairs } = useRepairContext();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<PaymentData[]>([]);
  const [stats, setStats] = useState({
    paid: 0,
    unpaid: 0,
    partial: 0,
    total: 0,
    totalAmount: 0,
    paidAmount: 0,
    unpaidAmount: 0,
    partialAmount: 0,
  });

  useEffect(() => {
    if (repairs) {
      // Calculate payment stats
      // Consider ALL repairs regardless of status for payment tracking
      // This ensures we capture all amounts, even for pending repairs

      // A repair is considered paid if its paymentStatus is "paid"
      const paidRepairs = repairs.filter((r) => r.paymentStatus === "paid");

      // A repair is considered unpaid if its paymentStatus is "unpaid"
      const unpaidRepairs = repairs.filter((r) => r.paymentStatus === "unpaid");

      // A repair is considered partially paid if its paymentStatus is "partial"
      const partialPaidRepairs = repairs.filter(
        (r) => r.paymentStatus === "partial"
      );

      // Calculate total amounts using the final price (including modifications)
      const paidAmount = paidRepairs.reduce((sum, repair) => {
        return sum + calculateFinalPrice(repair);
      }, 0);

      const unpaidAmount = unpaidRepairs.reduce((sum, repair) => {
        return sum + calculateFinalPrice(repair);
      }, 0);

      const partialAmount = partialPaidRepairs.reduce((sum, repair) => {
        return sum + calculateFinalPrice(repair);
      }, 0);

      const totalAmount = paidAmount + unpaidAmount + partialAmount;

      setStats({
        paid: paidRepairs.length,
        unpaid: unpaidRepairs.length,
        partial: partialPaidRepairs.length,
        total:
          paidRepairs.length + unpaidRepairs.length + partialPaidRepairs.length,
        paidAmount,
        unpaidAmount,
        partialAmount,
        totalAmount,
      });

      // Prepare chart data
      const chartData: PaymentData[] = [
        {
          name: t("dashboard.paid"),
          value: paidRepairs.length,
          color: "#10b981", // green-500
        },
        {
          name: t("dashboard.unpaid"),
          value: unpaidRepairs.length,
          color: "#ef4444", // red-500
        },
        {
          name: t("dashboard.partial"),
          value: partialPaidRepairs.length,
          color: "#f59e0b", // amber-500
        },
      ];

      setData(chartData.filter((item) => item.value > 0));
      setLoading(false);
    }
  }, [repairs, t]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (stats.total === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center text-gray-500 dark:text-gray-400 py-4">
        <AlertCircle className="h-8 w-8 mb-2" />
        <p>{t("dashboard.noPaymentData")}</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="grid grid-cols-3 gap-4" dir="ltr">
        {" "}
        {/* Force LTR direction for grid layout */}
        <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg">
          <div className="flex items-center">
            <DollarSign className="h-4 w-4 mr-1 text-green-500" />
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {t("dashboard.paid")}
            </div>
          </div>
          <div className="text-2xl font-bold">{stats.paid}</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {new Intl.NumberFormat("ar-TN", {
              style: "currency",
              currency: "TND",
              maximumFractionDigits: 3,
            }).format(stats.paidAmount)}
          </div>
          <Progress
            value={(stats.paid / stats.total) * 100}
            className="h-1 mt-2 bg-green-100 dark:bg-green-900/30"
          />
        </div>
        <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 mr-1 text-red-500" />
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {t("dashboard.unpaid")}
            </div>
          </div>
          <div className="text-2xl font-bold">{stats.unpaid}</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {new Intl.NumberFormat("ar-TN", {
              style: "currency",
              currency: "TND",
              maximumFractionDigits: 3,
            }).format(stats.unpaidAmount)}
          </div>
          <Progress
            value={(stats.unpaid / stats.total) * 100}
            className="h-1 mt-2 bg-red-100 dark:bg-red-900/30"
          />
        </div>
        <div className="bg-amber-50 dark:bg-amber-900/20 p-3 rounded-lg">
          <div className="flex items-center">
            <AlertCircle className="h-4 w-4 mr-1 text-amber-500" />
            <div className="text-sm text-gray-500 dark:text-gray-400">
              {t("dashboard.partial")}
            </div>
          </div>
          <div className="text-2xl font-bold">{stats.partial}</div>
          <div className="text-sm text-gray-500 dark:text-gray-400">
            {new Intl.NumberFormat("ar-TN", {
              style: "currency",
              currency: "TND",
              maximumFractionDigits: 3,
            }).format(stats.partialAmount)}
          </div>
          <Progress
            value={(stats.partial / stats.total) * 100}
            className="h-1 mt-2 bg-amber-100 dark:bg-amber-900/30"
          />
        </div>
      </div>

      <div className="h-[120px] mt-4" dir="ltr">
        {" "}
        {/* Force LTR direction for chart */}
        <ResponsiveContainer width="100%" height="100%">
          <PieChart>
            <Pie
              data={data}
              cx="50%"
              cy="50%"
              innerRadius={25}
              outerRadius={45}
              paddingAngle={5}
              dataKey="value"
            >
              {data.map((entry, index) => (
                <Cell key={`cell-${index}`} fill={entry.color} />
              ))}
            </Pie>
            <Tooltip
              formatter={(value: number) => [value, t("dashboard.repairs")]}
              contentStyle={{
                backgroundColor: "rgba(255, 255, 255, 0.9)",
                borderRadius: "4px",
                border: "1px solid #e2e8f0",
                padding: "8px",
              }}
            />
            <Legend />
          </PieChart>
        </ResponsiveContainer>
      </div>
    </div>
  );
};

export default PaymentStatusWidget;
