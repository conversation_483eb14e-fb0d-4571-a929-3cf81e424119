import React from 'react';
import { useTranslation } from 'react-i18next';
import { useAuth } from '@/context/AuthContext';
import { Button } from '@/components/ui/button';
import { Link } from 'react-router-dom';
import { PlusCircle, Clock } from 'lucide-react';

const WelcomeWidget: React.FC = () => {
  const { t } = useTranslation();
  const { user } = useAuth();
  
  // Get current time to display appropriate greeting
  const getGreeting = () => {
    const hour = new Date().getHours();
    if (hour < 12) return t('dashboard.goodMorning');
    if (hour < 18) return t('dashboard.goodAfternoon');
    return t('dashboard.goodEvening');
  };

  return (
    <div className="flex flex-col h-full justify-between">
      <div>
        <h3 className="text-xl font-bold mb-2">
          {getGreeting()}, {user?.email?.split('@')[0] || t('dashboard.user')}!
        </h3>
        <p className="text-gray-600 dark:text-gray-300">
          {t('dashboard.welcomeMessage')}
        </p>
      </div>
      
      <div className="mt-4 space-y-3">
        <div className="flex items-center text-sm text-gray-500 dark:text-gray-400">
          <Clock className="h-4 w-4 mr-2" />
          <span>{new Date().toLocaleDateString()} - {new Date().toLocaleTimeString()}</span>
        </div>
        
        <Link to="/new-repair">
          <Button className="w-full flex items-center gap-2">
            <PlusCircle className="h-4 w-4" />
            {t('common.newRepair')}
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default WelcomeWidget;
