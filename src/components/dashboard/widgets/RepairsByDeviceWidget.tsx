import React, { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useRepairContext } from "@/context/RepairContext";
import { Loader2 } from "lucide-react";
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
} from "recharts";

interface DeviceCount {
  name: string;
  count: number;
}

const RepairsByDeviceWidget: React.FC = () => {
  const { t } = useTranslation();
  const { repairs } = useRepairContext();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<DeviceCount[]>([]);

  useEffect(() => {
    if (repairs) {
      // Count repairs by device model
      const deviceCounts: Record<string, number> = {};

      repairs.forEach((repair) => {
        const model = repair.phoneModel || t("dashboard.unknownDevice");
        deviceCounts[model] = (deviceCounts[model] || 0) + 1;
      });

      // Convert to chart data format and sort by count
      const chartData = Object.entries(deviceCounts)
        .map(([model, count]) => ({
          name: model,
          count,
        }))
        .sort((a, b) => b.count - a.count)
        .slice(0, 5); // Only show top 5 devices

      setData(chartData);
      setLoading(false);
    }
  }, [repairs, t]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <Loader2 className="h-8 w-8 animate-spin text-gray-400" />
      </div>
    );
  }

  if (data.length === 0) {
    return (
      <div className="text-center text-gray-500 dark:text-gray-400 py-4">
        {t("dashboard.noRepairsFound")}
      </div>
    );
  }

  return (
    <div className="h-full" dir="ltr">
      {" "}
      {/* Force LTR direction for chart */}
      <ResponsiveContainer width="100%" height={200}>
        <BarChart
          data={data}
          margin={{
            top: 5,
            right: 30,
            left: 20,
            bottom: 5,
          }}
        >
          <CartesianGrid strokeDasharray="3 3" />
          <XAxis
            dataKey="name"
            tick={{ fontSize: 10 }}
            tickFormatter={(value) =>
              value.length > 10 ? `${value.substring(0, 10)}...` : value
            }
          />
          <YAxis allowDecimals={false} />
          <Tooltip
            formatter={(value: number) => [value, t("dashboard.repairs")]}
            labelFormatter={(label) => label}
            contentStyle={{
              backgroundColor: "rgba(255, 255, 255, 0.9)",
              borderRadius: "4px",
              border: "1px solid #e2e8f0",
              padding: "8px",
            }}
          />
          <Bar dataKey="count" fill="#8884d8" />
        </BarChart>
      </ResponsiveContainer>
    </div>
  );
};

export default RepairsByDeviceWidget;
