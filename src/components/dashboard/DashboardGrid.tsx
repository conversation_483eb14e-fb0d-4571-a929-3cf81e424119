import React, { useState, useEffect } from "react";
import { Responsive, WidthProvider } from "react-grid-layout";
import "react-grid-layout/css/styles.css";
import "react-resizable/css/styles.css";
import { useTranslation } from "react-i18next";
import { Button } from "@/components/ui/button";
import { Save, RotateCcw, Plus } from "lucide-react";
import { toast } from "sonner";
import { useLocalStorage } from "@/hooks/useLocalStorage";
import { WidgetConfig, WidgetType } from "@/types/dashboard";
import DashboardWidget from "./DashboardWidget";
import AddWidgetDialog from "./AddWidgetDialog";

// Apply width provider to make the grid responsive
const ResponsiveGridLayout = WidthProvider(Responsive);

interface DashboardGridProps {
  defaultWidgets?: WidgetConfig[];
}

const DashboardGrid: React.FC<DashboardGridProps> = ({
  defaultWidgets = [],
}) => {
  const { t, i18n } = useTranslation();
  const [isEditing, setIsEditing] = useState(false);
  const [showAddWidget, setShowAddWidget] = useState(false);
  const [layouts, setLayouts] = useState({});
  const [widgets, setWidgets, resetWidgets] = useLocalStorage<WidgetConfig[]>(
    "dashboard-widgets",
    defaultWidgets
  );

  // Determine if the current language is RTL
  const isRTL = i18n.dir() === "rtl";

  // Generate layouts from widgets when component mounts or widgets change
  useEffect(() => {
    const newLayouts = {
      lg: widgets.map((widget) => ({
        i: widget.id,
        x: widget.position.x,
        y: widget.position.y,
        w: widget.size.w,
        h: widget.size.h,
        minW: widget.minSize?.w || 2,
        minH: widget.minSize?.h || 2,
      })),
    };
    setLayouts(newLayouts);
  }, [widgets]);

  // Handle layout change
  const handleLayoutChange = (currentLayout: any, allLayouts: any) => {
    if (!isEditing) return;

    // Update widget positions based on the new layout
    const updatedWidgets = widgets.map((widget) => {
      const layoutItem = currentLayout.find(
        (item: any) => item.i === widget.id
      );
      if (layoutItem) {
        return {
          ...widget,
          position: {
            x: layoutItem.x,
            y: layoutItem.y,
          },
          size: {
            w: layoutItem.w,
            h: layoutItem.h,
          },
        };
      }
      return widget;
    });

    setWidgets(updatedWidgets);
    setLayouts(allLayouts);
  };

  // Add a new widget
  const handleAddWidget = (type: WidgetType, title: string) => {
    const newWidget: WidgetConfig = {
      id: `widget-${Date.now()}`,
      type,
      title,
      position: { x: 0, y: 0 }, // Will be placed automatically
      size: { w: 6, h: 4 },
      minSize: { w: 2, h: 2 },
    };

    setWidgets([...widgets, newWidget]);
    setShowAddWidget(false);
    toast.success(t("dashboard.widgetAdded"));
  };

  // Remove a widget
  const handleRemoveWidget = (id: string) => {
    setWidgets(widgets.filter((widget) => widget.id !== id));
    toast.success(t("dashboard.widgetRemoved"));
  };

  // Reset to default widgets
  const handleResetDashboard = () => {
    resetWidgets();
    toast.success(t("dashboard.dashboardReset"));
  };

  return (
    <div className="dashboard-container" style={{ direction: "ltr" }}>
      {" "}
      {/* Force LTR for entire dashboard */}
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-2xl font-bold">{t("dashboard.title")}</h2>
        <div className="flex gap-2">
          {isEditing ? (
            <>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowAddWidget(true)}
                className="flex items-center gap-1"
              >
                <Plus className="h-4 w-4" />
                {t("dashboard.addWidget")}
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={handleResetDashboard}
                className="flex items-center gap-1"
              >
                <RotateCcw className="h-4 w-4" />
                {t("dashboard.reset")}
              </Button>
              <Button
                variant="default"
                size="sm"
                onClick={() => setIsEditing(false)}
                className="flex items-center gap-1"
              >
                <Save className="h-4 w-4" />
                {t("dashboard.saveLayout")}
              </Button>
            </>
          ) : (
            <Button
              variant="outline"
              size="sm"
              onClick={() => setIsEditing(true)}
            >
              {t("dashboard.editLayout")}
            </Button>
          )}
        </div>
      </div>
      <ResponsiveGridLayout
        className="layout"
        layouts={layouts}
        breakpoints={{ lg: 1200, md: 996, sm: 768, xs: 480, xxs: 0 }}
        cols={{ lg: 12, md: 10, sm: 6, xs: 4, xxs: 2 }}
        rowHeight={100}
        isDraggable={isEditing}
        isResizable={isEditing}
        onLayoutChange={handleLayoutChange}
        margin={[16, 16]}
        isRTL={isRTL}
        containerPadding={[16, 16]}
      >
        {widgets.map((widget) => (
          <div key={widget.id} className="dashboard-widget">
            <DashboardWidget
              widget={widget}
              isEditing={isEditing}
              onRemove={handleRemoveWidget}
            />
          </div>
        ))}
      </ResponsiveGridLayout>
      <AddWidgetDialog
        open={showAddWidget}
        onOpenChange={setShowAddWidget}
        onAddWidget={handleAddWidget}
      />
    </div>
  );
};

export default DashboardGrid;
