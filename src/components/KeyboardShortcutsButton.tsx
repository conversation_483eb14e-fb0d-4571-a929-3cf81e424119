import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Button } from '@/components/ui/button';
import { Keyboard } from 'lucide-react';
import KeyboardShortcutsHelp from './KeyboardShortcutsHelp';
import { useKeyboardShortcuts } from '@/hooks/useKeyboardShortcuts';

const KeyboardShortcutsButton: React.FC = () => {
  const { t } = useTranslation();
  const [isOpen, setIsOpen] = useState(false);
  const { shortcuts, showHelp, setShowHelp } = useKeyboardShortcuts();

  // When the help dialog is triggered by the '?' shortcut
  React.useEffect(() => {
    if (showHelp) {
      setIsOpen(true);
      setShowHelp(false);
    }
  }, [showHelp, setShowHelp]);

  return (
    <>
      <Button
        variant="outline"
        size="sm"
        className="h-8"
        onClick={() => setIsOpen(true)}
        title={t('shortcuts.showShortcuts')}
      >
        <Keyboard className="h-4 w-4" />
        <span className="sr-only md:not-sr-only md:ml-1">{t('shortcuts.keyboard')}</span>
      </Button>
      
      <KeyboardShortcutsHelp
        shortcuts={shortcuts}
        open={isOpen}
        onOpenChange={setIsOpen}
      />
    </>
  );
};

export default KeyboardShortcutsButton;
