import React from "react";
import AdminAccessTest from "@/components/AdminAccessTest";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useNavigate } from "react-router-dom";

const AdminTest: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center gap-4">
        <Button 
          variant="outline" 
          onClick={() => navigate(-1)}
          className="flex items-center gap-2"
        >
          <ArrowLeft className="h-4 w-4" />
          Back
        </Button>
        <h1 className="text-2xl font-bold">Administrator Access Test</h1>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Instructions</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
            <h3 className="font-semibold text-yellow-800 mb-2">Before Running Tests:</h3>
            <ol className="text-sm text-yellow-700 space-y-1 list-decimal list-inside">
              <li>Apply the database migration: <code>migrations/fix_administrator_repair_access.sql</code></li>
              <li>Verify your user has the "administrator" role in the repair shop</li>
              <li>Refresh the page after applying the migration</li>
            </ol>
          </div>
          
          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
            <h3 className="font-semibold text-blue-800 mb-2">How to Apply the Migration:</h3>
            <ol className="text-sm text-blue-700 space-y-1 list-decimal list-inside">
              <li>Go to your Supabase project dashboard</li>
              <li>Navigate to the SQL Editor</li>
              <li>Copy the content from <code>migrations/fix_administrator_repair_access.sql</code></li>
              <li>Paste and run the SQL query</li>
              <li>If needed, use <code>migrations/verify_and_fix_admin_roles.sql</code> to assign admin roles</li>
            </ol>
          </div>

          <div className="bg-green-50 border border-green-200 rounded-lg p-4">
            <h3 className="font-semibold text-green-800 mb-2">Expected Results After Fix:</h3>
            <ul className="text-sm text-green-700 space-y-1 list-disc list-inside">
              <li>Administrators should see ALL repairs in their repair shop</li>
              <li>Technicians should see only assigned repairs when toggle is OFF</li>
              <li>Technicians should see all repairs when toggle is ON</li>
              <li>New admin accounts should immediately have access to all repairs</li>
            </ul>
          </div>
        </CardContent>
      </Card>

      <AdminAccessTest />

      <Card>
        <CardHeader>
          <CardTitle>Quick Role Assignment</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="bg-gray-50 border rounded-lg p-4">
            <h3 className="font-semibold mb-2">To assign administrator role to a user:</h3>
            <p className="text-sm text-muted-foreground mb-2">
              Run this SQL query in Supabase SQL Editor (replace with actual email):
            </p>
            <code className="block bg-gray-100 p-2 rounded text-sm">
              SELECT assign_admin_role('<EMAIL>');
            </code>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default AdminTest;
