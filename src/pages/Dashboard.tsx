import React, { useState } from "react";
import { useRepairContext } from "@/context/RepairContext";
import RepairCard from "@/components/RepairCard";
import UniversalScanner from "@/components/UniversalScanner";
import ImportExportData from "@/components/ImportExportData";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useTranslation } from "react-i18next";
import DashboardGrid from "@/components/dashboard/DashboardGrid";
import { WidgetConfig } from "@/types/dashboard";

import { useNavigate } from "react-router-dom";
import { Search, PlusCircle } from "lucide-react";

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const { repairs, searchRepairs, loading, getRepairByTicketNumber } =
    useRepairContext();
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();

  // Default widgets configuration
  const defaultWidgets: WidgetConfig[] = [
    {
      id: "welcome",
      type: "welcome",
      title: t("dashboard.widgets.welcome.title"),
      position: { x: 0, y: 0 },
      size: { w: 6, h: 4 },
    },
    {
      id: "repair-status",
      type: "repair-status",
      title: t("dashboard.widgets.repair-status.title"),
      position: { x: 6, y: 0 },
      size: { w: 6, h: 4 },
    },
    {
      id: "payment-status",
      type: "payment-status",
      title: t("dashboard.widgets.payment-status.title"),
      position: { x: 0, y: 4 },
      size: { w: 6, h: 5 },
    },
    {
      id: "income",
      type: "income",
      title: t("dashboard.widgets.income.title"),
      position: { x: 6, y: 4 },
      size: { w: 6, h: 5 },
    },
    {
      id: "recent-repairs",
      type: "recent-repairs",
      title: t("dashboard.widgets.recent-repairs.title"),
      position: { x: 0, y: 9 },
      size: { w: 6, h: 6 },
    },
    {
      id: "repairs-by-status",
      type: "repairs-by-status",
      title: t("dashboard.widgets.repairs-by-status.title"),
      position: { x: 6, y: 9 },
      size: { w: 6, h: 6 },
    },
  ];

  const filteredRepairs = searchTerm ? searchRepairs(searchTerm) : repairs;

  const pendingRepairs = filteredRepairs.filter(
    (r) => r.status === "pending" || r.status === "inProgress"
  );
  const completedRepairs = filteredRepairs.filter(
    (r) => r.status === "completed"
  );
  const returnedRepairs = filteredRepairs.filter(
    (r) => r.status === "returned"
  );

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Already filtered by the searchTerm state
  };

  const handleQRScan = async (data: string) => {
    // Check if the scanned data is a numeric ticket number (including EAN-13)
    const isNumeric = /^[0-9]+$/.test(data);

    if (isNumeric) {
      // If it's a numeric ticket number, query the database directly
      try {
        console.log("Scanned numeric data:", data);

        // The getRepairByTicketNumber function now handles EAN-13 format internally
        const repair = await getRepairByTicketNumber(data);

        if (repair) {
          console.log("Found repair:", repair.id);
          navigate(`/repair/${repair.id}`);
        } else {
          // If no repair is found with that ticket number, show an error or alert
          console.error(`No repair found with ticket number: ${data}`);
          alert(t("repair.noRepairFoundWithTicket"));
        }
      } catch (error) {
        console.error("Error finding repair by ticket number:", error);
        alert(t("repair.scanError"));
      }
    } else {
      // If it's not a numeric ticket number, assume it's a UUID and navigate directly
      console.log("Scanned UUID:", data);
      navigate(`/repair/${data}`);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center p-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  // Return the component JSX with the new customizable dashboard
  return (
    <div className="container mx-auto px-4 py-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
        <h1 className="text-2xl font-bold">{t("app.slogan")}</h1>

        <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
          <form
            onSubmit={handleSearch}
            className="relative w-full sm:w-64 md:w-80"
          >
            <div className="relative">
              <Input
                placeholder={t("common.searchRepairs")}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pr-10 w-full" /* Increased right padding for icon */
              />
              <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                <Search className="h-4 w-4 text-gray-400" />
              </div>
            </div>
          </form>

          <div className="flex gap-2">
            <UniversalScanner onScan={handleQRScan} />
            <Button
              onClick={() => navigate("/new-repair")}
              className="flex items-center gap-2"
            >
              <PlusCircle className="h-4 w-4" />
              {t("common.newRepair")}
            </Button>
          </div>
        </div>
      </div>

      {/* Customizable Dashboard */}
      <div className="mb-8">
        <DashboardGrid defaultWidgets={defaultWidgets} />
      </div>

      <div className="mb-6">
        <ImportExportData />
      </div>

      {/* Repair Tabs */}
      <Tabs defaultValue="active">
        <TabsList className="flex flex-wrap">
          <TabsTrigger value="active">
            {t("repair.active")} ({pendingRepairs.length})
          </TabsTrigger>
          <TabsTrigger value="completed">
            {t("repair.completed")} ({completedRepairs.length})
          </TabsTrigger>
          <TabsTrigger value="returned">
            {t("repair.returned")} ({returnedRepairs.length})
          </TabsTrigger>
          <TabsTrigger value="all">
            {t("repair.all")} ({filteredRepairs.length})
          </TabsTrigger>
        </TabsList>

        <TabsContent value="active" className="mt-6">
          {pendingRepairs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {pendingRepairs.map((repair) => (
                <RepairCard key={repair.id} repair={repair} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">{t("repair.noActiveRepairs")}</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="completed" className="mt-6">
          {completedRepairs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {completedRepairs.map((repair) => (
                <RepairCard key={repair.id} repair={repair} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">{t("repair.noCompletedRepairs")}</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="returned" className="mt-6">
          {returnedRepairs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {returnedRepairs.map((repair) => (
                <RepairCard key={repair.id} repair={repair} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">{t("repair.noReturnedRepairs")}</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="all" className="mt-6">
          {filteredRepairs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredRepairs.map((repair) => (
                <RepairCard key={repair.id} repair={repair} />
              ))}
            </div>
          ) : (
            <div className="text-center py-12">
              <p className="text-gray-500">{t("repair.noRepairsFound")}</p>
              <Button
                variant="outline"
                onClick={() => navigate("/new-repair")}
                className="mt-4"
              >
                {t("repair.createFirstRepair")}
              </Button>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Dashboard;
