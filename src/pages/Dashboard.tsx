import React, { useState } from "react";
import { useRepairContext } from "@/context/RepairContext";
import RepairCard from "@/components/RepairCard";
import UniversalScanner from "@/components/UniversalScanner";
import ImportExportData from "@/components/ImportExportData";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useTranslation } from "react-i18next";
import DashboardGrid from "@/components/dashboard/DashboardGrid";
import { WidgetConfig } from "@/types/dashboard";
import { useTheme } from "@/context/ThemeContext";

import { useNavigate } from "react-router-dom";
import { Search, PlusCircle, CheckCircle2, Package } from "lucide-react";

const Dashboard: React.FC = () => {
  const { t } = useTranslation();
  const { repairs, searchRepairs, loading, getRepairByTicketNumber } =
    useRepairContext();
  const { isPremium } = useTheme();
  const [searchTerm, setSearchTerm] = useState("");
  const navigate = useNavigate();

  // Default widgets configuration
  const defaultWidgets: WidgetConfig[] = [
    {
      id: "welcome",
      type: "welcome",
      title: t("dashboard.widgets.welcome.title"),
      position: { x: 0, y: 0 },
      size: { w: 6, h: 4 },
    },
    {
      id: "repair-status",
      type: "repair-status",
      title: t("dashboard.widgets.repair-status.title"),
      position: { x: 6, y: 0 },
      size: { w: 6, h: 4 },
    },
    {
      id: "payment-status",
      type: "payment-status",
      title: t("dashboard.widgets.payment-status.title"),
      position: { x: 0, y: 4 },
      size: { w: 6, h: 5 },
    },
    {
      id: "income",
      type: "income",
      title: t("dashboard.widgets.income.title"),
      position: { x: 6, y: 4 },
      size: { w: 6, h: 5 },
    },
    {
      id: "recent-repairs",
      type: "recent-repairs",
      title: t("dashboard.widgets.recent-repairs.title"),
      position: { x: 0, y: 9 },
      size: { w: 6, h: 6 },
    },
    {
      id: "repairs-by-status",
      type: "repairs-by-status",
      title: t("dashboard.widgets.repairs-by-status.title"),
      position: { x: 6, y: 9 },
      size: { w: 6, h: 6 },
    },
  ];

  const filteredRepairs = searchTerm ? searchRepairs(searchTerm) : repairs;

  const pendingRepairs = filteredRepairs.filter(
    (r) => r.status === "pending" || r.status === "inProgress"
  );
  const completedRepairs = filteredRepairs.filter(
    (r) => r.status === "completed"
  );
  const returnedRepairs = filteredRepairs.filter(
    (r) => r.status === "returned"
  );

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Already filtered by the searchTerm state
  };

  const handleQRScan = async (data: string) => {
    // Check if the scanned data is a numeric ticket number (including EAN-13)
    const isNumeric = /^[0-9]+$/.test(data);

    if (isNumeric) {
      // If it's a numeric ticket number, query the database directly
      try {
        console.log("Scanned numeric data:", data);

        // The getRepairByTicketNumber function now handles EAN-13 format internally
        const repair = await getRepairByTicketNumber(data);

        if (repair) {
          console.log("Found repair:", repair.id);
          navigate(`/repair/${repair.id}`);
        } else {
          // If no repair is found with that ticket number, show an error or alert
          console.error(`No repair found with ticket number: ${data}`);
          alert(t("repair.noRepairFoundWithTicket"));
        }
      } catch (error) {
        console.error("Error finding repair by ticket number:", error);
        alert(t("repair.scanError"));
      }
    } else {
      // If it's not a numeric ticket number, assume it's a UUID and navigate directly
      console.log("Scanned UUID:", data);
      navigate(`/repair/${data}`);
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center p-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-6">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {t("app.slogan")}
            </h1>
            <p className="text-gray-600 dark:text-gray-400 text-sm">
              {t("dashboard.subtitle", "Manage your repairs efficiently")}
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 w-full md:w-auto">
            <form
              onSubmit={handleSearch}
              className="relative w-full sm:w-64 md:w-80"
            >
              <div className="relative">
                <Input
                  placeholder={t("common.searchRepairs")}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pr-10 w-full bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm border-gray-200 dark:border-gray-700 focus:ring-2 focus:ring-blue-500/20 transition-all duration-200"
                />
                <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                  <Search className="h-4 w-4 text-gray-400" />
                </div>
              </div>
            </form>

            <div className="flex gap-3">
              <UniversalScanner onScan={handleQRScan} />
              <Button
                onClick={() => navigate("/new-repair")}
                className="flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <PlusCircle className="h-4 w-4" />
                {t("common.newRepair")}
              </Button>
            </div>
          </div>
        </div>

        {/* Customizable Dashboard */}
        <div className="mb-10">
          <DashboardGrid defaultWidgets={defaultWidgets} />
        </div>

        <div className="mb-8">
          <ImportExportData />
        </div>

        {/* Repair Tabs */}
        <div className="bg-white/60 dark:bg-gray-800/60 backdrop-blur-sm rounded-2xl p-6 shadow-xl border border-gray-200/50 dark:border-gray-700/50">
          <Tabs defaultValue="active">
            <TabsList className="grid w-full grid-cols-4 bg-gray-100/80 dark:bg-gray-700/80 backdrop-blur-sm rounded-xl p-1">
              <TabsTrigger 
                value="active" 
                className="data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-blue-600 transition-all duration-200"
              >
                <div className="flex flex-col items-center gap-1">
                  <span className="font-medium">{t("repair.active")}</span>
                  <span className="text-xs bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 px-2 py-0.5 rounded-full">
                    {pendingRepairs.length}
                  </span>
                </div>
              </TabsTrigger>
              <TabsTrigger 
                value="completed"
                className="data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-green-600 transition-all duration-200"
              >
                <div className="flex flex-col items-center gap-1">
                  <span className="font-medium">{t("repair.completed")}</span>
                  <span className="text-xs bg-green-100 dark:bg-green-900/30 text-green-600 dark:text-green-400 px-2 py-0.5 rounded-full">
                    {completedRepairs.length}
                  </span>
                </div>
              </TabsTrigger>
              <TabsTrigger 
                value="returned"
                className="data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-purple-600 transition-all duration-200"
              >
                <div className="flex flex-col items-center gap-1">
                  <span className="font-medium">{t("repair.returned")}</span>
                  <span className="text-xs bg-purple-100 dark:bg-purple-900/30 text-purple-600 dark:text-purple-400 px-2 py-0.5 rounded-full">
                    {returnedRepairs.length}
                  </span>
                </div>
              </TabsTrigger>
              <TabsTrigger 
                value="all"
                className="data-[state=active]:bg-white data-[state=active]:shadow-md data-[state=active]:text-gray-600 transition-all duration-200"
              >
                <div className="flex flex-col items-center gap-1">
                  <span className="font-medium">{t("repair.all")}</span>
                  <span className="text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 px-2 py-0.5 rounded-full">
                    {filteredRepairs.length}
                  </span>
                </div>
              </TabsTrigger>
            </TabsList>

        <TabsContent value="active" className="mt-6">
          {pendingRepairs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {pendingRepairs.map((repair) => (
                <RepairCard key={repair.id} repair={repair} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="w-16 h-16 mx-auto mb-4 bg-gray-100 dark:bg-gray-700 rounded-full flex items-center justify-center">
                <Search className="h-8 w-8 text-gray-400" />
              </div>
              <p className="text-gray-500 dark:text-gray-400 text-lg font-medium">{t("repair.noActiveRepairs")}</p>
              <p className="text-gray-400 dark:text-gray-500 text-sm mt-2">Create a new repair to get started</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="completed" className="mt-6">
          {completedRepairs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {completedRepairs.map((repair) => (
                <RepairCard key={repair.id} repair={repair} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="w-16 h-16 mx-auto mb-4 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center">
                <CheckCircle2 className="h-8 w-8 text-green-500" />
              </div>
              <p className="text-gray-500 dark:text-gray-400 text-lg font-medium">{t("repair.noCompletedRepairs")}</p>
              <p className="text-gray-400 dark:text-gray-500 text-sm mt-2">Completed repairs will appear here</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="returned" className="mt-6">
          {returnedRepairs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {returnedRepairs.map((repair) => (
                <RepairCard key={repair.id} repair={repair} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="w-16 h-16 mx-auto mb-4 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center">
                <Package className="h-8 w-8 text-purple-500" />
              </div>
              <p className="text-gray-500 dark:text-gray-400 text-lg font-medium">{t("repair.noReturnedRepairs")}</p>
              <p className="text-gray-400 dark:text-gray-500 text-sm mt-2">Returned repairs will appear here</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="all" className="mt-6">
          {filteredRepairs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredRepairs.map((repair) => (
                <RepairCard key={repair.id} repair={repair} />
              ))}
            </div>
          ) : (
            <div className="text-center py-16">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-blue-100 to-purple-100 dark:from-blue-900/30 dark:to-purple-900/30 rounded-full flex items-center justify-center">
                <PlusCircle className="h-10 w-10 text-blue-500" />
              </div>
              <p className="text-gray-500 dark:text-gray-400 text-xl font-medium mb-2">{t("repair.noRepairsFound")}</p>
              <p className="text-gray-400 dark:text-gray-500 text-sm mb-6">Start by creating your first repair</p>
              <Button
                onClick={() => navigate("/new-repair")}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
              >
                <PlusCircle className="h-4 w-4 mr-2" />
                {t("repair.createFirstRepair")}
              </Button>
            </div>
          )}
        </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
