import React, { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";

import InvoiceGenerator from "@/components/InvoiceGenerator";
import InvoiceList from "@/components/InvoiceList";
import InvoicePrint from "@/components/InvoicePrint";
import { Invoice } from "@/types";
import { FileText, Printer } from "lucide-react";
import { format } from "date-fns";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { useTranslation } from "react-i18next";
import TechnicianToggle from "@/components/TechnicianToggle";

const Invoices: React.FC = () => {
  const [selectedInvoice, setSelectedInvoice] = useState<Invoice | null>(null);
  const [showPrintDialog, setShowPrintDialog] = useState(false);
  const { repairShop } = useRepairShopContext();
  const { t } = useTranslation();

  const handleViewInvoice = (invoice: Invoice) => {
    setSelectedInvoice(invoice);
    setShowPrintDialog(true);
  };

  const handlePrint = () => {
    if (!selectedInvoice) return;
    
    const printWindow = window.open('', '_blank');
    if (!printWindow) return;
    
    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
      <head>
        <title>Invoice ${selectedInvoice.invoiceNumber}</title>
        <style>
          @page { size: A4 landscape; margin: 5mm; }
          body { margin: 0; padding: 10px; font-family: Arial, sans-serif; font-size: 12px; }
          .invoice-container { display: flex; gap: 10px; height: 100vh; }
          .invoice-copy { flex: 1; }
          .invoice-copy:last-child { border-left: 2px dashed #ccc; padding-left: 10px; }
          .header { text-center; margin-bottom: 20px; }
          .info { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 15px; }
          table { width: 100%; border-collapse: collapse; margin-bottom: 15px; }
          th, td { border: 1px solid #ccc; padding: 8px; text-align: left; }
          th { background-color: #f5f5f5; }
          .totals { text-align: right; margin-bottom: 15px; }
          .total-line { display: flex; justify-content: space-between; padding: 2px 0; }
          .total-final { border-top: 1px solid #000; font-weight: bold; padding-top: 5px; }
        </style>
      </head>
      <body>
        <div class="invoice-container">
          ${generateInvoiceHTML(selectedInvoice)}
          ${generateInvoiceHTML(selectedInvoice)}
        </div>
      </body>
      </html>
    `);
    
    printWindow.document.close();
    printWindow.focus();
    printWindow.print();
    printWindow.close();
  };
  
  const generateInvoiceHTML = (invoice: Invoice) => {
    return `
      <div class="invoice-copy">
        <div class="header">
          <h1>${repairShop?.name || ''}</h1>
          ${repairShop?.address ? `<p>${repairShop.address}</p>` : ''}
          ${repairShop?.phone ? `<p>Phone: ${repairShop.phone}</p>` : ''}
        </div>
        <div class="info">
          <div>
            <h2>INVOICE</h2>
            <p><strong>Invoice #:</strong> ${invoice.invoiceNumber}</p>
            <p><strong>Date:</strong> ${format(invoice.createdAt, "MMM dd, yyyy")}</p>
          </div>
          <div>
            <h3>Bill To:</h3>
            <p>${invoice.customerName}</p>
            ${invoice.customerPhone ? `<p>${invoice.customerPhone}</p>` : ''}
          </div>
        </div>
        <table>
          <thead>
            <tr><th>Description</th><th style="text-align: right;">Amount</th></tr>
          </thead>
          <tbody>
            ${invoice.items.map(item => `
              <tr>
                <td>${item.description}</td>
                <td style="text-align: right;">${item.amount.toFixed(2)} TND</td>
              </tr>
            `).join('')}
          </tbody>
        </table>
        <div class="totals">
          <div class="total-line"><span>Subtotal:</span><span>${invoice.subtotal.toFixed(2)} TND</span></div>
          ${invoice.discountAmount > 0 ? `<div class="total-line"><span>Discount:</span><span>-${invoice.discountAmount.toFixed(2)} TND</span></div>` : ''}
          <div class="total-line total-final"><span>Total:</span><span>${invoice.totalAmount.toFixed(2)} TND</span></div>
        </div>
        ${invoice.notes ? `<div><h3>Notes:</h3><p>${invoice.notes}</p></div>` : ''}
        <div style="text-align: center; margin-top: 20px; color: #666;">
          <p>Thank you for your business!</p>
        </div>
      </div>
    `;
  };

  return (
    <div className="container mx-auto p-6">
      <div className="mb-6">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <FileText className="h-8 w-8" />
              {t("invoices.title")}
            </h1>
            <p className="text-muted-foreground">{t("invoices.description")}</p>
          </div>
          <TechnicianToggle />
        </div>
      </div>

      <Tabs defaultValue="generate" className="space-y-6">
        <TabsList>
          <TabsTrigger value="generate">{t("invoices.generateInvoice")}</TabsTrigger>
          <TabsTrigger value="list">{t("invoices.invoiceList")}</TabsTrigger>
        </TabsList>

        <TabsContent value="generate">
          <InvoiceGenerator />
        </TabsContent>

        <TabsContent value="list">
          <InvoiceList onViewInvoice={handleViewInvoice} />
        </TabsContent>
      </Tabs>

      <Dialog open={showPrintDialog} onOpenChange={setShowPrintDialog}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center justify-between">
              <span>{t("invoices.invoice")}</span>
              <Button onClick={handlePrint} size="sm">
                <Printer className="h-4 w-4 mr-2" />
                {t("common.print")}
              </Button>
            </DialogTitle>
          </DialogHeader>
          {selectedInvoice && (
            <InvoicePrint invoice={selectedInvoice} />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default Invoices;