import React, { useState, useEffect } from "react";
import { use<PERSON>ara<PERSON>, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useRepairContext } from "@/context/RepairContext";
import { useRepairShopContext } from "@/context/RepairShopContext";
import { useAuth } from "@/context/AuthContext";

import { useKeyboardShortcuts } from "@/hooks/useKeyboardShortcuts";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { format } from "date-fns";
import { generateQRCodeDataURL } from "@/utils/qrCodeUtils";
import {
  generateRepairBarcode,
  generateBarcodeDataURL,
} from "@/utils/barcodeUtils";
import {
  generateTicketNumber,
  formatTicketNumber,
} from "@/utils/ticketNumberUtils";
import { toast } from "sonner";
import {
  ArrowLeft,
  Printer,
  Download,
  AlertTriangle,
  CheckCircle2,
  Phone,
  Smartphone,
  Calendar,
  Receipt,
  FileText,
  Ticket,
  Plus,
  X,
  ClipboardList,
  Hash,
  QrCode,
  Copy,
  PhoneCall,
  Mail,
  User,
  Clock,
  Wrench,
  Package,
} from "lucide-react";
import {
  RepairItem,
  Observation,
  PriceModification,
  StatusHistory,
} from "@/types";
import RepairTimeline from "@/components/RepairTimeline";
import EmailDialog from "@/components/EmailDialog";
import { logoConfig, logoPrintConfig } from "@/config/appConfig";

const RepairDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { getRepairById, updateRepair, deleteRepair, repairs } =
    useRepairContext();
  const { repairShop } = useRepairShopContext();
  const navigate = useNavigate();
  const [repair, setRepair] = useState<RepairItem | undefined>(undefined);
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [barcode, setBarcode] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [printDialogOpen, setPrintDialogOpen] = useState(false);
  const [deleteCode, setDeleteCode] = useState("");
  const [deleteError, setDeleteError] = useState("");
  const [loading, setLoading] = useState(true);
  const [codeType, setCodeType] = useState<"qr" | "barcode">("qr"); // Default to QR code
  const [newObservation, setNewObservation] = useState("");
  const [newPriceModification, setNewPriceModification] = useState({
    amount: 0,
    reason: "",
  });
  const [customFormResponse, setCustomFormResponse] = useState<any>(null);
  const [repairStockItems, setRepairStockItems] = useState<any[]>([]);
  const [availableTechnicians, setAvailableTechnicians] = useState<any[]>([]);
  const [userFullName, setUserFullName] = useState<string>("");
  const [availableProducts, setAvailableProducts] = useState<any[]>([]);
  const [showAddStockDialog, setShowAddStockDialog] = useState(false);
  const [selectedProductId, setSelectedProductId] = useState<string>("");
  const [stockQuantity, setStockQuantity] = useState<number>(1);
  const { t, i18n } = useTranslation();
  const { user } = useAuth();

  // Set up keyboard shortcuts
  useKeyboardShortcuts({
    onPrint: () => {
      if (repair) {
        handleOpenPrintDialog();
      }
    },
  });

  useEffect(() => {
    const fetchRepair = async () => {
      if (id) {
        setLoading(true);
        try {
          const foundRepair = await getRepairById(id);
          setRepair(foundRepair);

          if (foundRepair) {
            generateQRCode(foundRepair.id);
            generateBarcode(false); // Generate regular barcode for standalone printing
            loadCustomFormResponse(foundRepair.id);
            loadRepairStockItems(foundRepair.id);
            loadAvailableTechnicians();
            loadUserFullName();
            loadAvailableProducts();
          }
        } catch (error) {
          console.error("Error fetching repair:", error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchRepair();

    // Set up real-time subscription for this specific repair
    if (id) {
      const repairSubscription = supabase
        .channel(`repair-${id}`)
        .on(
          "postgres_changes",
          {
            event: "UPDATE",
            schema: "public",
            table: "repairs",
            filter: `id=eq.${id}`,
          },
          async (payload) => {
            console.log("Repair updated:", payload);
            // Fetch the updated repair directly from the database
            const updatedRepair = await getRepairById(id);
            if (updatedRepair) {
              setRepair(updatedRepair);
            }
          }
        )
        .subscribe();

      // Clean up subscription when component unmounts
      return () => {
        supabase.removeChannel(repairSubscription);
      };
    }
  }, [id, getRepairById]);

  const loadCustomFormResponse = async (repairId: string) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("form_responses")
        .select(
          `
          *,
          template:template_id(name, device_type, fields)
        `
        )
        .eq("repair_id", repairId)
        .single();

      if (!error && data) {
        setCustomFormResponse(data);
      }
    } catch (error) {
      console.error("Error loading custom form response:", error);
    }
  };

  const loadRepairStockItems = async (repairId: string) => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("repair_stock_items")
        .select(
          `
          *,
          product:product_id(name, sku, price)
        `
        )
        .eq("repair_id", repairId)
        .order("created_at");

      if (!error && data) {
        setRepairStockItems(data);
      }
    } catch (error) {
      console.error("Error loading repair stock items:", error);
    }
  };

  const loadAvailableTechnicians = async () => {
    if (!user || !repairShop) return;

    try {
      const { data, error } = await supabase.rpc("get_shop_users", {
        shop_id: repairShop.id,
      });

      if (!error && data) {
        const technicians = data.filter(
          (u) => u.role === "technician" || u.role === "administrator"
        );
        setAvailableTechnicians(technicians);
      }
    } catch (error) {
      console.error("Error loading technicians:", error);
    }
  };

  const loadUserFullName = async () => {
    if (!user || !repairShop) return;

    try {
      const { data, error } = await supabase
        .from("user_repair_shops")
        .select("full_name")
        .eq("user_id", user.id)
        .eq("repair_shop_id", repairShop.id)
        .single();

      if (!error && data) {
        setUserFullName(data.full_name || user.email || "");
      }
    } catch (error) {
      console.error("Error fetching user full name:", error);
    }
  };

  const loadAvailableProducts = async () => {
    if (!user) return;

    try {
      const { data, error } = await supabase
        .from("products")
        .select("id, name, sku, price, stock_quantity")
        .gt("stock_quantity", 0)
        .eq("is_active", true)
        .order("name");

      if (!error && data) {
        setAvailableProducts(data);
      }
    } catch (error) {
      console.error("Error loading products:", error);
    }
  };

  const handleRemoveStockItem = async (
    itemId: string,
    productId: string,
    quantity: number
  ) => {
    if (!repair || !id) return;

    try {
      // Get current stock quantity
      const { data: productData, error: fetchError } = await supabase
        .from("products")
        .select("stock_quantity")
        .eq("id", productId)
        .single();

      if (fetchError) throw fetchError;

      // Remove from repair_stock_items
      const { error: deleteError } = await supabase
        .from("repair_stock_items")
        .delete()
        .eq("id", itemId);

      if (deleteError) throw deleteError;

      // Return stock to inventory
      const { error: stockError } = await supabase
        .from("products")
        .update({
          stock_quantity: productData.stock_quantity + quantity,
        })
        .eq("id", productId);

      if (stockError) throw stockError;

      // Record stock movement for return
      const { error: movementError } = await supabase
        .from("stock_movements")
        .insert({
          product_id: productId,
          type: "in",
          quantity: quantity,
          reason: `Returned from repair for ${repair.customerName}`,
          user_id: user?.id,
          repair_shop_id: repairShop?.id,
        });

      if (movementError) {
        console.error("Error recording return movement:", movementError);
      }

      // Refresh the stock items list
      await loadRepairStockItems(id);
      await loadAvailableProducts();
      toast.success("Stock item removed and returned to inventory");
    } catch (error) {
      console.error("Error removing stock item:", error);
      toast.error("Failed to remove stock item");
    }
  };

  const handleAddStockItem = async () => {
    if (
      !repair ||
      !id ||
      !selectedProductId ||
      stockQuantity <= 0 ||
      !repairShop ||
      !user
    )
      return;

    try {
      const product = availableProducts.find((p) => p.id === selectedProductId);
      if (!product) throw new Error("Product not found");

      if (product.stock_quantity < stockQuantity) {
        toast.error("Insufficient stock available");
        return;
      }

      // First, reduce stock from inventory
      const { error: stockError } = await supabase
        .from("products")
        .update({
          stock_quantity: product.stock_quantity - stockQuantity,
        })
        .eq("id", selectedProductId);

      if (stockError) throw stockError;

      // Then add to repair_stock_items
      const { error: insertError } = await supabase
        .from("repair_stock_items")
        .insert({
          repair_id: id,
          product_id: selectedProductId,
          quantity: stockQuantity,
          unit_price: product.price,
          repair_shop_id: repairShop.id,
        });

      if (insertError) {
        // If insert fails, return the stock
        await supabase
          .from("products")
          .update({
            stock_quantity: product.stock_quantity,
          })
          .eq("id", selectedProductId);
        throw insertError;
      }

      // Record stock movement
      const { error: movementError } = await supabase
        .from("stock_movements")
        .insert({
          product_id: selectedProductId,
          type: "out",
          quantity: stockQuantity,
          reason: `Used in repair for ${repair.customerName}`,
          user_id: user?.id,
          repair_shop_id: repairShop.id,
        });

      if (movementError) {
        console.error("Error recording stock movement:", movementError);
      }

      // Reset form and refresh
      setSelectedProductId("");
      setStockQuantity(1);
      setShowAddStockDialog(false);
      await loadRepairStockItems(id);
      await loadAvailableProducts();
      toast.success("Stock item added to repair");
    } catch (error) {
      console.error("Error adding stock item:", error);
      toast.error("Failed to add stock item");
    }
  };

  const handleTechnicianChange = async (technicianId: string) => {
    if (!repair || !id) return;

    try {
      const { error } = await supabase
        .from("repairs")
        .update({
          assigned_technician: technicianId === "none" ? null : technicianId,
        })
        .eq("id", id);

      if (error) throw error;

      setRepair({
        ...repair,
        assignedTechnician: technicianId === "none" ? undefined : technicianId,
      });
      toast.success("Technician assignment updated");
    } catch (error) {
      console.error("Error updating technician:", error);
      toast.error("Failed to update technician assignment");
    }
  };

  const generateQRCode = async (repairId: string) => {
    try {
      const qrDataUrl = await generateQRCodeDataURL(repairId);
      setQrCode(qrDataUrl);
    } catch (error) {
      console.error("Failed to generate QR code:", error);
    }
  };

  const generateBarcode = async (isForTicket: boolean = false) => {
    try {
      if (!repair) return;

      // Always use the ticket number for the barcode
      const ticketNumber = repair.ticketNumber?.toString() || "";

      if (ticketNumber) {
        console.log("Generating barcode for ticket number:", ticketNumber);

        try {
          // Generate a barcode using ONLY the ticket number
          const barcodeDataUrl = await generateRepairBarcode(
            ticketNumber, // Use ticket number as the content
            isForTicket
          );
          setBarcode(barcodeDataUrl);
        } catch (barcodeError) {
          console.error("Error generating barcode:", barcodeError);
          toast.error(t("repair.scanError"));
        }
      } else {
        // If no ticket number is available, we can't generate a proper barcode
        console.error("No ticket number available for barcode generation");
        toast.error(t("repair.noTicketNumberForBarcode"));
      }
    } catch (error) {
      console.error("Failed to generate barcode:", error);
      toast.error(t("repair.scanError"));
    }
  };

  const handleStatusChange = async (value: string) => {
    if (repair && id) {
      const statusUpdate = value as
        | "pending"
        | "inProgress"
        | "completed"
        | "cancelled"
        | "returned";

      // Don't update if status hasn't changed
      if (statusUpdate === repair.status) {
        return;
      }

      const updates: Partial<RepairItem> = {
        status: statusUpdate,
      };

      if (statusUpdate === "completed" && repair.status !== "completed") {
        updates.completedAt = new Date();
      }

      // If status is changed to "returned" from "completed", keep the completedAt date
      if (
        statusUpdate === "returned" &&
        repair.status !== "returned" &&
        !repair.completedAt
      ) {
        updates.completedAt = new Date();
      }

      try {
        // Only add a status history entry if the status is actually changing
        // and we're not in the initial creation (where status history is already initialized)
        if (repair.statusHistory && repair.statusHistory.length > 0) {
          // Check if the last status entry is different from the new one
          const lastStatusEntry =
            repair.statusHistory[repair.statusHistory.length - 1];

          // Only add a new entry if the status is different
          if (lastStatusEntry.status !== statusUpdate) {
            // Create a new status history entry
            const newStatusHistory: StatusHistory = {
              id: crypto.randomUUID(),
              status: statusUpdate,
              createdAt: new Date(),
              userId: repair.userId,
            };

            // Add to existing history
            const updatedStatusHistory = [
              ...repair.statusHistory,
              newStatusHistory,
            ];
            updates.statusHistory = updatedStatusHistory;
          }
        } else {
          // If no status history exists yet, initialize it
          const newStatusHistory: StatusHistory = {
            id: crypto.randomUUID(),
            status: statusUpdate,
            createdAt: new Date(),
            userId: repair.userId,
          };

          updates.statusHistory = [newStatusHistory];
        }
      } catch (error) {
        console.log("Status history might not be available yet, skipping");
        // Continue without status history
      }

      await updateRepair(id, updates);
      setRepair({ ...repair, ...updates });
      toast.success(t("repair.successUpdated"));
    }
  };

  const handlePaymentStatusChange = async (value: string) => {
    if (repair && id) {
      const paymentStatusUpdate = value as "paid" | "partial" | "unpaid";
      await updateRepair(id, { paymentStatus: paymentStatusUpdate });
      setRepair({ ...repair, paymentStatus: paymentStatusUpdate });
      toast.success(t("repair.successUpdated"));
    }
  };

  const handleAddObservation = async () => {
    if (repair && id && newObservation.trim()) {
      try {
        // Create a new observation object
        const newObservationObj = {
          id: crypto.randomUUID(),
          text: newObservation.trim(),
          createdAt: new Date(),
          userId: user?.id || repair.userId,
        };

        // Add to existing observations
        const updatedObservations = [
          ...(repair.observations || []),
          newObservationObj,
        ];

        // Use the RepairContext updateRepair function
        await updateRepair(id, {
          observations: updatedObservations,
        });

        // Update local state
        setRepair({
          ...repair,
          observations: updatedObservations,
        });

        // Clear the input
        setNewObservation("");
        toast.success(t("repair.observationAdded"));
      } catch (error) {
        console.error("Error adding observation:", error);
        toast.error(t("repair.observationAddError"));
      }
    }
  };

  const handleAddPriceModification = async () => {
    if (
      repair &&
      id &&
      newPriceModification.reason.trim() &&
      newPriceModification.amount !== 0
    ) {
      try {
        // Create a new price modification object
        const newPriceModificationObj = {
          id: crypto.randomUUID(),
          amount: newPriceModification.amount,
          reason: newPriceModification.reason.trim(),
          createdAt: new Date(),
          userId: user?.id || repair.userId,
        };

        // Add to existing price modifications
        const updatedPriceModifications = [
          ...(repair.priceModifications || []),
          newPriceModificationObj,
        ];

        // Use the RepairContext updateRepair function
        await updateRepair(id, {
          priceModifications: updatedPriceModifications,
        });

        // Update local state
        setRepair({
          ...repair,
          priceModifications: updatedPriceModifications,
        });

        // Clear the input
        setNewPriceModification({ amount: 0, reason: "" });
        toast.success(t("repair.priceModificationAdded"));
      } catch (error) {
        console.error("Error adding price modification:", error);
        toast.error(t("repair.priceModificationAddError"));
      }
    }
  };

  const handleDelete = async () => {
    // Get the deletion code from environment variables
    const correctCode = "8397";

    // Reset error state
    setDeleteError("");

    // Check if the entered code matches the correct code
    if (deleteCode !== correctCode) {
      setDeleteError(t("repair.invalidDeleteCode"));
      return;
    }

    if (id) {
      await deleteRepair(id);
      toast.success(t("repair.successDeleted"));
      setDeleteDialogOpen(false);
      setDeleteCode(""); // Reset the code input
      navigate("/");
    }
  };

  const handlePrintQR = () => {
    if (!qrCode) return;

    // Open a window that will adapt to the printer's capabilities
    const printWindow = window.open(
      "",
      "_blank",
      "width=400,height=400,menubar=no,toolbar=no,location=no,scrollbars=yes,status=no,resizable=yes"
    );
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>${t("repair.scanQrCode")} - ${repair?.customerName}</title>
            <style>
              @page {
                margin: 0;
                size: auto;
              }
              html, body {
                height: auto;
                overflow: visible;
              }
              body {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 0;
                margin: 0;
                font-family: Arial, sans-serif;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
              }
              .qr-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                border: 1px solid #000;
                padding: 10px;
                margin-top: 0;
                width: calc(100% - 20px);
                box-sizing: border-box;
              }
              img {
                width: 200px;
                height: 200px;
              }
              .details {
                margin-top: 10px;
                text-align: center;
              }
              h3 {
                margin: 5px 0;
              }
              p {
                margin: 2px 0;
                font-size: 14px;
              }
            </style>
          </head>
          <body>
            <div class="qr-container">
              <img src="${qrCode}" alt="QR Code" />
              <div class="details">
                <h3>${repair?.customerName}</h3>
                <p>${repair?.phoneModel}</p>
                <p>${format(
                  new Date(repair?.createdAt || new Date()),
                  "dd/MM/yyyy - HH:mm"
                )}</p>
              </div>
            </div>
            <script>
              // Print immediately without waiting for full load
              document.addEventListener('DOMContentLoaded', function() {
                // Small timeout to ensure the browser has rendered the content
                setTimeout(function() {
                  window.print();
                  window.onafterprint = function() {
                    window.close();
                  };
                }, 100);
              });

              // Fallback in case DOMContentLoaded doesn't trigger
              window.onload = function() {
                window.print();
                window.onafterprint = function() {
                  window.close();
                };
              };
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  const handlePrintBarcode = async () => {
    if (!repair) return;

    // Generate a standalone barcode (larger, with text)
    await generateBarcode(false);

    if (!barcode) return;

    // Open a window that will adapt to the printer's capabilities
    const printWindow = window.open(
      "",
      "_blank",
      "width=400,height=200,menubar=no,toolbar=no,location=no,scrollbars=yes,status=no,resizable=yes"
    );
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>${t("repair.scanBarcode")} - ${repair?.customerName}</title>
            <style>
              @page {
                margin: 0;
                size: auto;
              }
              html, body {
                height: auto;
                overflow: visible;
              }
              body {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 0;
                margin: 0;
                font-family: Arial, sans-serif;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
              }
              .barcode-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                border: 1px solid #000;
                padding: 10px;
                margin-top: 0;
                width: calc(100% - 20px);
                box-sizing: border-box;
              }
              img {
                width: 95%;
                max-width: 350px;
                height: auto;
                margin-top: 10px; /* Add some space at the top */
              }
              .details {
                margin-top: 10px;
                text-align: center;
              }
              h3 {
                margin: 5px 0;
              }
              p {
                margin: 2px 0;
                font-size: 14px;
              }
            </style>
          </head>
          <body>
            <div class="barcode-container">
              <img src="${barcode}" alt="Barcode" />
              <div class="details">
                <h3>${repair?.customerName}</h3>
                <p>${repair?.phoneModel}</p>
                <p>${format(
                  new Date(repair?.createdAt || new Date()),
                  "dd/MM/yyyy - HH:mm"
                )}</p>
              </div>
            </div>
            <script>
              // Print immediately without waiting for full load
              document.addEventListener('DOMContentLoaded', function() {
                // Small timeout to ensure the browser has rendered the content
                setTimeout(function() {
                  window.print();
                  window.onafterprint = function() {
                    window.close();
                  };
                }, 100);
              });

              // Fallback in case DOMContentLoaded doesn't trigger
              window.onload = function() {
                window.print();
                window.onafterprint = function() {
                  window.close();
                };
              };
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  // Function to print a small barcode sticker for the back of devices
  const handlePrintBarcodeSticker = async () => {
    if (!repair) return;

    // Generate a compact barcode specifically for stickers
    try {
      // Always use the ticket number for the barcode
      const ticketNumber = repair.ticketNumber?.toString() || "";

      if (!ticketNumber) {
        toast.error(t("repair.noTicketNumberForBarcode"));
        return;
      }

      // Generate a compact barcode for stickers
      const barcodeOptions = {
        format: "CODE128",
        width: 1.5, // Thinner bars for smaller sticker
        height: 40, // Shorter height for compact sticker
        displayValue: true, // Show text below barcode
        fontSize: 8, // Smaller font for compact sticker
        margin: 2, // Minimal margin
        textMargin: 1, // Minimal text margin
      };

      // Use the utility function but with custom options
      const stickerBarcode = await generateBarcodeDataURL(
        ticketNumber,
        barcodeOptions
      );

      // Open a window optimized for small sticker printing
      const printWindow = window.open(
        "",
        "_blank",
        "width=200,height=100,menubar=no,toolbar=no,location=no,scrollbars=yes,status=no,resizable=yes"
      );

      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>${t("repair.barcodeSticker")} - ${
          repair?.customerName
        }</title>
              <style>
                @page {
                  margin: 0;
                  size: 30mm 20mm; /* Small sticker size */
                }
                html, body {
                  height: 100%;
                  width: 100%;
                  overflow: hidden;
                  padding: 0;
                  margin: 0;
                }
                body {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  font-family: Arial, sans-serif;
                }
                .sticker-container {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  width: 100%;
                  height: 100%;
                  box-sizing: border-box;
                  padding: 2mm;
                }
                .barcode-img {
                  width: 100%;
                  max-height: 15mm;
                }
                .ticket-number {
                  font-size: 7pt;
                  font-weight: bold;
                  margin-top: 1mm;
                  text-align: center;
                }
                .shop-name {
                  font-size: 6pt;
                  margin-top: 0.5mm;
                  text-align: center;
                }
              </style>
            </head>
            <body>
              <div class="sticker-container">
                <img src="${stickerBarcode}" alt="Barcode" class="barcode-img" />
                <div class="ticket-number">#${repair.ticketNumber}</div>
                <div class="shop-name">${t("app.title")}</div>
              </div>
              <script>
                // Print immediately without waiting for full load
                document.addEventListener('DOMContentLoaded', function() {
                  setTimeout(function() {
                    window.print();
                    window.onafterprint = function() {
                      window.close();
                    };
                  }, 100);
                });

                // Fallback in case DOMContentLoaded doesn't trigger
                window.onload = function() {
                  window.print();
                  window.onafterprint = function() {
                    window.close();
                  };
                };
              </script>
            </body>
          </html>
        `);
        printWindow.document.close();
      }
    } catch (error) {
      console.error("Failed to generate barcode sticker:", error);
      toast.error(t("repair.scanError"));
    }
  };

  const handleDownloadQR = () => {
    if (!qrCode) return;

    const link = document.createElement("a");
    link.href = qrCode;
    link.download = `qr-code-${repair?.id}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleOpenPrintDialog = () => {
    if (!repair || !qrCode) return;
    setPrintDialogOpen(true);
  };

  const handlePrintTicket = async () => {
    if (!repair || !qrCode) return;

    // Generate a ticket-specific barcode (smaller) if barcode is selected
    if (codeType === "barcode") {
      await generateBarcode(true);
    }

    // Use the stored ticket number or generate one if not available
    const ticketNumber = repair.ticketNumber
      ? repair.ticketNumber.toString()
      : generateTicketNumber(repair, repairs);
    const formattedTicketNumber = formatTicketNumber(ticketNumber, "#");

    // Get the absolute URL for the logo
    // This ensures the logo path is correctly resolved in the new window
    const logoUrl = new URL(logoPrintConfig.path, window.location.origin).href;

    const formatCurrency = (amount: number) => {
      return new Intl.NumberFormat(i18n.language, {
        style: "currency",
        currency: "TND",
      }).format(amount);
    };

    // Open a window that matches 80mm thermal printer width
    const printWindow = window.open(
      "",
      "_blank",
      "width=302,height=600,menubar=no,toolbar=no,location=no,scrollbars=yes,status=no,resizable=yes"
    );
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>${t("repair.repairTicket")} - ${repair.customerName}</title>
            <style>
              @page {
                size: 80mm auto; /* Set width to 80mm for thermal printer */
                margin: 0;
              }
              @media print {
                /* Ensure proper margins and padding for 80mm paper */
                html, body {
                  margin: 0 !important;
                  padding: 0 !important;
                  overflow: hidden;
                  width: 80mm !important;
                  max-width: 80mm !important;
                }
                /* Set ticket width for 80mm paper with padding */
                .ticket {
                  width: 72mm !important; /* 80mm - 8mm (4mm padding on each side) */
                  max-width: 72mm !important;
                  margin: 0 4mm !important; /* 4mm margin on each side */
                  padding: 0 !important;
                  box-sizing: border-box !important;
                }
              }
              html, body {
                height: auto;
                overflow: visible;
                margin: 0;
                padding: 0;
                width: 100%;
                max-width: 100%;
              }
              body {
                font-family: 'Arial', sans-serif; /* Better readability than Courier */
                width: 100%; /* Use full width available */
                max-width: 100%; /* Allow printer to determine max width */
                padding: 0;
                margin: 0; /* No margin */
                font-size: 10px; /* Increased base font size for better readability */
                line-height: 1.4; /* Increased line height for better readability */
                -webkit-print-color-adjust: exact; /* Ensure backgrounds print */
                color-adjust: exact;
                position: relative;
                box-sizing: border-box; /* Include padding in width calculation */
                left: 0; /* Ensure left alignment */
                font-weight: 900; /* Make all text extra bold by default */
              }
              .ticket {
                width: 72mm; /* 80mm - 8mm (4mm padding on each side) */
                max-width: 72mm;
                margin: 0 4mm; /* 4mm margin on each side */
                padding: 0;
                position: relative;
                top: 0;
                left: 0; /* Ensure left alignment */
                box-sizing: border-box; /* Include padding in width calculation */
              }
              .header {
                text-align: center;
                margin-bottom: 5px; /* Reduced margin */
                border-bottom: 1px dashed #000;
                padding-bottom: 3px; /* Reduced padding */
              }
              .logo-container {
                text-align: center;
                margin-bottom: 8px;
                padding-top: 5px;
              }
              .logo-container img {
                max-width: 80px;
                max-height: 80px;
                margin: 0 auto;
                display: block;
                object-fit: contain;
              }
              .shop-name {
                font-size: 16px; /* Increased font size */
                font-weight: 900; /* Extra bold */
                text-decoration: underline; /* Add underline for emphasis */
                margin-bottom: 2px; /* Add some spacing */
              }
              .title {
                font-size: 14px; /* Increased font size */
                font-weight: 900; /* Extra bold */
                margin: 3px 0; /* Reduced margin */
                border-bottom: 2px solid #000; /* Thicker solid underline */
                padding: 2px 0;
                text-decoration: underline; /* Add underline for emphasis */
                text-align: center; /* Center text */
              }
              .section {
                margin-bottom: 5px; /* Reduced margin */
                border-bottom: 1px dashed #000;
                padding-bottom: 3px; /* Reduced padding */
              }
              .info-row {
                display: block; /* Changed from flex to block to prevent right content from being cut */
                margin: 1px 0; /* Further reduced margin */
                width: 100%;
                box-sizing: border-box;
                overflow: hidden; /* Prevent overflow */
              }
              .label {
                font-weight: 900; /* Extra bold */
                text-decoration: underline; /* Underline instead of color for emphasis */
                display: inline-block; /* Changed from flex to inline-block */
                width: 40%; /* Fixed width for labels */
                padding-right: 2px; /* Reduced space between label and value */
                overflow: hidden; /* Prevent overflow */
                text-overflow: ellipsis; /* Add ellipsis for overflow */
                vertical-align: top; /* Align to top */
              }
              .value {
                font-weight: 900; /* Extra bold for values */
                text-align: right;
                display: inline-block; /* Changed from flex to inline-block */
                width: 58%; /* Fixed width for values */
                overflow: hidden; /* Prevent overflow */
                text-overflow: ellipsis; /* Add ellipsis for overflow */
                vertical-align: top; /* Align to top */
                font-size: 16px; /* Slightly larger font */
              }
              .qr-code {
                text-align: center;
                margin-top: 5px; /* Reduced margin */
                width: 100%;
                box-sizing: border-box;
              }
              .qr-code img {
                width: 50px; /* Even smaller QR code */
                height: 50px;
                max-width: 100%;
              }
              .barcode {
                text-align: center;
                margin-top: 5px; /* Reduced margin */
                width: 100%;
                box-sizing: border-box;
                border-top: 1px dashed #000;
                padding-top: 5px;
              }
              .barcode img {
                width: 90%; /* Wider barcode */
                max-width: 90%;
                height: 70px; /* Further increased height for better scanning */
                margin: 0 auto; /* Center the barcode */
                display: block;
              }
              .footer {
                text-align: center;
                margin-top: 5px; /* Reduced margin */
                font-size: 10px; /* Increased font size */
                font-weight: 900; /* Extra bold footer text */
                width: 100%;
                box-sizing: border-box;
              }
              .text-center {
                text-align: center;
              }
              p {
                margin: 1px 0; /* Further reduced margin */
                font-weight: 900; /* Semi-bold for all text */
                width: 100%;
                box-sizing: border-box;
                word-wrap: break-word; /* Allow long words to break */
                white-space: normal; /* Allow text to wrap */
                overflow-wrap: break-word; /* Break words to prevent overflow */
              }
              /* Make observation timestamps more visible */
              .timestamp {
                font-size: 8px;
                font-weight: 700; /* Bold */
                width: 100%;
                box-sizing: border-box;
                display: block;
              }
            </style>
          </head>
          <body>
            <div style="width: 80mm; margin: 0; padding: 0; box-sizing: border-box;">
              <div class="ticket">
              <div class="header">
                <div class="logo-container">
                  <img
                    src="${logoUrl}"
                    alt="${logoConfig.alt || t("app.title")}"
                    onerror="this.style.display='none'; document.getElementById('shop-name').style.fontSize='18px';"
                  />
                </div>
                <div id="shop-name" class="shop-name">${t("app.title")}</div>
                <div style="font-size: 10px; font-weight: 900;">97 335 738</div>
                <div>${t("repair.repairTicket")} ${formattedTicketNumber}</div>
                <div>${format(new Date(), "dd/MM/yyyy - HH:mm")}</div>
                ${
                  userFullName
                    ? `<div style="font-size: 9px; margin-top: 2px;">${t(
                        "userManagement.technician"
                      )}: ${userFullName}</div>`
                    : ""
                }
              </div>

              <div class="section">
                <div class="title">${t("repair.customerInfo")}</div>
                <div class="info-row">
                  <span class="label">${t("repair.customerName")}:</span>
                  <span class="value">${repair.customerName}</span>
                </div>
                <div class="info-row">
                  <span class="label">${t("repair.customerPhone")}:</span>
                  <span class="value">${repair.customerPhone}</span>
                </div>
              </div>

              <div class="section">
                <div class="title">${t("repair.repairDetails")}</div>
                <div class="info-row">
                  <span class="label">${t("repair.phoneModel")}:</span>
                  <span class="value">${repair.phoneModel}</span>
                </div>
                ${
                  repair.branch
                    ? `
                <div class="info-row">
                  <span class="label">Branch:</span>
                  <span class="value">${repair.branch.name} (${repair.branch.code})</span>
                </div>
                `
                    : ""
                }
                <div class="info-row">
                  <span class="label">${t("repair.stat")}:</span>
                  <span class="value">${t(
                    `repair.status.${repair.status}`
                  )}</span>
                </div>
                <div class="info-row">
                  <span class="label">${t("repair.createdAt")}:</span>
                  <span class="value">${format(
                    new Date(repair.createdAt),
                    "dd/MM/yyyy - HH:mm"
                  )}</span>
                </div>
                <div style="margin-top: 4px; border: 1px solid #000; padding: 2px;">
                  <p style="font-weight: 900; text-align: center; text-decoration: underline; font-size: 10px; margin-bottom: 2px;">${t(
                    "repair.problemDescription"
                  )}:</p>
                  <p style="font-weight: 800; font-size: 9px; text-align: left;">${
                    repair.problemDescription
                  }</p>
                </div>

                ${
                  customFormResponse &&
                  customFormResponse.responses &&
                  Object.keys(customFormResponse.responses).length > 0
                    ? `
                <div style="margin-top: 3px; border-top: 1px dashed #000; padding-top: 2px;">
                  <p style="font-weight: 900; text-decoration: underline; font-size: 9px; text-align: center;">${
                    customFormResponse.template?.name || "Custom Form"
                  }:</p>
                  ${Object.entries(customFormResponse.responses)
                    .map(
                      ([field, value]) =>
                        `<div style="margin-top: 2px; padding-left: 2px;">
                          <p style="font-weight: 900; font-size: 9px;"><strong>${field}:</strong> ${
                          value === true
                            ? "Yes"
                            : value === false
                            ? "No"
                            : value || "N/A"
                        }</p>
                        </div>`
                    )
                    .join("")}
                </div>
                `
                    : ""
                }

                ${
                  repair.observations &&
                  Array.isArray(repair.observations) &&
                  repair.observations.length > 0
                    ? `
                <div style="margin-top: 3px; border-top: 1px dashed #000; padding-top: 2px;">
                  <p style="font-weight: 900; text-decoration: underline; font-size: 9px;">${t(
                    "repair.observations"
                  )}:</p>
                  ${repair.observations
                    .map(
                      (obs, index) =>
                        `<div style="margin-top: ${
                          index > 0 ? "3px" : "1px"
                        }; padding-left: 2px;">
                          <p style="font-weight: 900; font-size: 10px;">- ${
                            obs.text
                          }</p>
                          <p style="font-size: 8px; font-weight: 700; text-align: right;">${format(
                            new Date(obs.createdAt),
                            "dd/MM/yyyy - HH:mm"
                          )}</p>
                        </div>`
                    )
                    .join("")}
                </div>
                `
                    : ""
                }
              </div>

              <div class="section">
                <div class="title">${t("repair.paymentStatus")}</div>
                <div class="info-row">
                  <span class="label">${t("repair.initialPrice")}:</span>
                  <span class="value" style="font-weight: 800;">${formatCurrency(
                    repair.repairPrice
                  )}</span>
                </div>

                ${
                  repair.priceModifications &&
                  repair.priceModifications.length > 0
                    ? `<div style="margin-top: 2px; border-top: 1px dashed #000; padding-top: 2px;">
                  <p style="font-weight: 900; text-decoration: underline; font-size: 8px;">${t(
                    "repair.priceModifications"
                  )}:</p>
                  ${repair.priceModifications
                    .map(
                      (mod, index) =>
                        `<div style="margin-top: ${index > 0 ? "2px" : "1px"};">
                          <div>
                            <span style="font-weight: 900; font-size: 10px; display: inline-block; width: 65%;">- ${
                              mod.reason
                            }:</span>
                            <span style="font-weight: 900; font-size: 10px; display: inline-block; width: 35%; text-align: right;">
                              ${mod.amount > 0 ? "+" : ""}${formatCurrency(
                          mod.amount
                        )}
                            </span>
                          </div>
                          <p style="font-size: 8px; font-weight: 700; text-align: right; margin: 0;">
                            ${format(
                              new Date(mod.createdAt),
                              "dd/MM/yyyy - HH:mm"
                            )}
                          </p>
                        </div>`
                    )
                    .join("")}
                  <div style="border-top: 1px dotted #000; padding-top: 2px; margin-top: 2px;">
                    <span style="font-weight: 900; display: inline-block; width: 50%;">${t(
                      "repair.totalPrice"
                    )}:</span>
                    <span style="font-weight: 900; display: inline-block; width: 50%; text-align: right;">${formatCurrency(
                      repair.repairPrice +
                        repair.priceModifications.reduce(
                          (sum, mod) => sum + mod.amount,
                          0
                        )
                    )}</span>
                  </div>
                </div>`
                    : ""
                }

                <div style="margin-top: 3px; border-top: 1px dashed #000; padding-top: 2px;">
                  <div style="margin-bottom: 1px;">
                    <span style="font-weight: 700; display: inline-block; width: 50%;">${t(
                      "repair.downPayment"
                    )}:</span>
                    <span style="font-weight: 700; display: inline-block; width: 50%; text-align: right;">${formatCurrency(
                      repair.downPayment
                    )}</span>
                  </div>
                  <div style="margin-bottom: 1px;">
                    <span style="font-weight: 700; display: inline-block; width: 50%;">${t(
                      "common.balance"
                    )}:</span>
                    <span style="font-weight: 800; display: inline-block; width: 50%; text-align: right;">${formatCurrency(
                      Math.max(
                        0,
                        repair.repairPrice +
                          (repair.priceModifications?.reduce(
                            (sum, mod) => sum + mod.amount,
                            0
                          ) || 0) -
                          repair.downPayment
                      )
                    )}</span>
                  </div>
                  <div>
                    <span style="font-weight: 700; display: inline-block; width: 50%;">${t(
                      "repair.paymentStatus"
                    )}:</span>
                    <span style="font-weight: 800; display: inline-block; width: 50%; text-align: right;">${t(
                      `repair.payment.${repair.paymentStatus}`
                    )}</span>
                  </div>
                </div>
              </div>

              ${
                codeType === "qr"
                  ? `<div class="qr-code">
                      <img src="${qrCode}" alt="QR Code" />
                      <p class="text-center" style="font-weight: 900; font-size: 10px;">${t(
                        "repair.scanQrCode"
                      )}: ${repair.id.substring(0, 8)}</p>
                      <p class="text-center" style="font-weight: 900; font-size: 11px;">${t(
                        "repair.ticketNumber"
                      )}: ${formattedTicketNumber}</p>
                    </div>`
                  : `<div class="barcode">
                      <img src="${barcode}" alt="Barcode" />
                      <p class="text-center" style="font-weight: 900; font-size: 10px;">${t(
                        "repair.scanBarcode"
                      )}</p>
                      <p class="text-center" style="font-weight: 900; font-size: 11px;">${t(
                        "repair.ticketNumber"
                      )}: ${formattedTicketNumber}</p>
                    </div>`
              }

              <div class="footer">
                <p style="font-weight: 900; font-size: 11px;">${t(
                  "repair.thankYou"
                )}</p>
                <p style="font-weight: 900; font-size: 10px;">${t(
                  "repair.bringReceipt"
                )}</p>
              </div>
            </div>
            </div>
            <script>
              // Function to adjust content for 80mm thermal printer
              function adjustForPrinting() {
                const ticket = document.querySelector('.ticket');
                if (!ticket) return;

                // Set fixed font size for 80mm paper
                document.body.style.fontSize = '10px';
                document.body.style.fontWeight = '900'; // Make all text extra bold

                // Set body width to 80mm
                document.body.style.width = '80mm';
                document.body.style.maxWidth = '80mm';
                document.body.style.margin = '0';
                document.body.style.padding = '0';

                // Set ticket width to 72mm with 4mm margins on each side
                ticket.style.width = '72mm';
                ticket.style.maxWidth = '72mm';
                ticket.style.margin = '0 4mm';
                ticket.style.padding = '0';
                ticket.style.boxSizing = 'border-box';

                // Ensure all content fits within the ticket width and is bold
                const contentElements = ticket.querySelectorAll('*');
                contentElements.forEach(el => {
                  if (el.style) {
                    el.style.maxWidth = '100%';
                    el.style.boxSizing = 'border-box';
                    el.style.wordWrap = 'break-word';
                    el.style.overflowWrap = 'break-word';

                    // Make all text bold
                    if (el.tagName !== 'IMG') { // Skip images
                      el.style.fontWeight = '900';
                    }
                  }
                });

                // Make specific elements even more readable
                const labels = ticket.querySelectorAll('.label');
                labels.forEach(label => {
                  if (label.style) {
                    label.style.fontWeight = '900';
                    label.style.fontSize = '10px';
                  }
                });

                const values = ticket.querySelectorAll('.value');
                values.forEach(value => {
                  if (value.style) {
                    value.style.fontWeight = '900';
                    value.style.fontSize = '10px';
                  }
                });
              }

              // Preload the logo image to ensure it's loaded before printing
              function preloadLogo() {
                return new Promise((resolve) => {
                  const logoImg = document.querySelector('.logo-container img');
                  if (!logoImg) {
                    resolve(); // No logo found, continue
                    return;
                  }

                  if (logoImg.complete) {
                    resolve(); // Image already loaded
                  } else {
                    logoImg.onload = () => resolve(); // Image loaded successfully
                    logoImg.onerror = () => resolve(); // Image failed to load, continue anyway

                    // Set a timeout in case the image takes too long to load
                    setTimeout(resolve, 1000);
                  }
                });
              }

              // Print after ensuring logo is loaded
              document.addEventListener('DOMContentLoaded', async function() {
                adjustForPrinting();
                await preloadLogo();
                setTimeout(function() {
                  window.print();
                  window.onafterprint = function() {
                    window.close();
                  };
                }, 300);
              });

              // Fallback in case DOMContentLoaded doesn't trigger
              window.onload = async function() {
                adjustForPrinting();
                await preloadLogo();
                window.print();
                window.onafterprint = function() {
                  window.close();
                };
              };

              // Adjust on resize (in case the print dialog changes the window size)
              window.addEventListener('resize', adjustForPrinting);
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(i18n.language, {
      style: "currency",
      currency: "TND",
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-3xl mx-auto">
          <div className="flex items-center justify-center p-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!repair) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-3xl mx-auto">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>{t("common.error")}</AlertTitle>
            <AlertDescription>{t("repair.notFound")}</AlertDescription>
          </Alert>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => navigate("/")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("common.back")} {t("common.dashboard")}
          </Button>
        </div>
      </div>
    );
  }

  const statusColors = {
    pending:
      "bg-yellow-100 text-yellow-800 border-yellow-200 dark:bg-yellow-800 dark:text-white dark:border-yellow-700",
    inProgress:
      "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-800 dark:text-white dark:border-blue-700",
    completed:
      "bg-green-100 text-green-800 border-green-200 dark:bg-green-800 dark:text-white dark:border-green-700",
    cancelled:
      "bg-red-100 text-red-800 border-red-200 dark:bg-red-800 dark:text-white dark:border-red-700",
    returned:
      "bg-purple-100 text-purple-800 border-purple-200 dark:bg-purple-800 dark:text-white dark:border-purple-700",
  };

  const paymentStatusColors = {
    paid: "bg-green-100 text-green-800 border-green-200 dark:bg-green-800 dark:text-white dark:border-green-700",
    partial:
      "bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-800 dark:text-white dark:border-blue-700",
    unpaid:
      "bg-red-100 text-red-800 border-red-200 dark:bg-red-800 dark:text-white dark:border-red-700",
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="max-w-3xl mx-auto">
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => navigate("/")}
            className="mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("common.back")} {t("common.dashboard")}
          </Button>

          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold">
                {t("repair.repairDetails")}
              </h1>
              {repair.ticketNumber && (
                <div className="text-sm font-medium text-gray-500 dark:text-gray-400 mt-1">
                  {t("repair.ticketNumber")}: #{repair.ticketNumber}
                </div>
              )}
            </div>

            <div className="flex gap-2 flex-wrap">
              <Button
                variant="outline"
                onClick={handlePrintQR}
                className="flex items-center gap-2"
              >
                <Printer className="h-4 w-4" />
                {t("common.print")} QR
              </Button>

              <Button
                variant="outline"
                onClick={handlePrintBarcode}
                className="flex items-center gap-2"
              >
                <Printer className="h-4 w-4" />
                {t("common.print")} {t("scanner.barcode")}
              </Button>

              <Button
                variant="outline"
                onClick={handlePrintBarcodeSticker}
                className="flex items-center gap-2"
              >
                <Printer className="h-4 w-4" />
                {t("repair.barcodeSticker")}
              </Button>

              <Button
                variant="outline"
                onClick={handleDownloadQR}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {t("common.download")} QR
              </Button>

              <Button
                variant="default"
                onClick={handleOpenPrintDialog}
                className="flex items-center gap-2"
              >
                <Ticket className="h-4 w-4" />
                {t("repair.printTicket")}
              </Button>

              {/* Email Button - Show if customer has email
              {repair.customerEmail && (
                <EmailDialog
                  repair={repair}
                  defaultTemplate={
                    repair.status === "completed" ? "completion" : "custom"
                  }
                  trigger={
                    <Button
                      variant="outline"
                      className="flex items-center gap-2"
                    >
                      <Mail className="h-4 w-4" />
                      {t("repair.sendEmail")}
                    </Button>
                  }
                />
              )} */}

              <Dialog open={printDialogOpen} onOpenChange={setPrintDialogOpen}>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>{t("repair.selectCodeType")}</DialogTitle>
                    <DialogDescription>
                      {t("repair.selectCodeTypeDescription")}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="flex flex-col space-y-4 py-4">
                    <RadioGroup
                      value={codeType}
                      onValueChange={(value) =>
                        setCodeType(value as "qr" | "barcode")
                      }
                      className="flex flex-col space-y-2"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="qr" id="qr" />
                        <Label htmlFor="qr" className="cursor-pointer">
                          {t("repair.qrCode")}
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="barcode" id="barcode" />
                        <Label htmlFor="barcode" className="cursor-pointer">
                          {t("scanner.barcode")}
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setPrintDialogOpen(false)}
                    >
                      {t("common.cancel")}
                    </Button>
                    <Button
                      onClick={() => {
                        setPrintDialogOpen(false);
                        handlePrintTicket();
                      }}
                    >
                      {t("common.print")}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card className="overflow-hidden bg-card dark:bg-gray-800">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 pb-2 flex flex-row justify-between items-center">
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2 text-blue-500" />
                  {t("repair.customerInfo")}
                </CardTitle>
                <Badge className={statusColors[repair.status]}>
                  {t(`repair.status.${repair.status}`)}
                </Badge>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="space-y-4">
                  {/* Customer Name - Larger and more prominent */}
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-10 h-10 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-3">
                      <User className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-300 font-medium">
                        {t("repair.customerName")}
                      </p>
                      <p className="text-lg font-semibold dark:text-white">
                        {repair.customerName}
                      </p>
                    </div>
                    <div className="ml-auto">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-blue-600"
                        onClick={() => {
                          navigator.clipboard.writeText(repair.customerName);
                          toast.success(t("common.copied"));
                        }}
                        title={t("common.copy")}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Phone Number with Call Button */}
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-10 h-10 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mr-3">
                      <Phone className="h-5 w-5 text-green-600 dark:text-green-400" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-300 font-medium">
                        {t("repair.customerPhone")}
                      </p>
                      <p className="text-base font-medium dark:text-white">
                        {repair.customerPhone}
                      </p>
                    </div>
                    <div className="ml-auto flex">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-green-600"
                        onClick={() => {
                          navigator.clipboard.writeText(repair.customerPhone);
                          toast.success(t("common.copied"));
                        }}
                        title={t("common.copy")}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-green-600"
                        onClick={() =>
                          window.open(`tel:${repair.customerPhone}`)
                        }
                        title={t("common.call")}
                      >
                        <PhoneCall className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Customer Email */}
                  {repair.customerEmail && (
                    <div className="flex items-center">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                        <Mail className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-300 font-medium">
                          {t("repair.customerEmail")}
                        </p>
                        <p className="text-base font-medium dark:text-white">
                          {repair.customerEmail}
                        </p>
                      </div>
                      <div className="ml-auto flex">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-500 hover:text-blue-600"
                          onClick={() => {
                            navigator.clipboard.writeText(
                              repair.customerEmail || ""
                            );
                            toast.success(t("common.copied"));
                          }}
                          title={t("common.copy")}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                        <EmailDialog
                          repair={repair}
                          trigger={
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-8 w-8 text-gray-500 hover:text-blue-600"
                              title={t("repair.sendEmail")}
                            >
                              <Mail className="h-4 w-4" />
                            </Button>
                          }
                        />
                      </div>
                    </div>
                  )}

                  {/* Device Model */}
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                      <Smartphone className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-300 font-medium">
                        {t("repair.phoneModel")}
                      </p>
                      <p className="text-base font-medium dark:text-white">
                        {repair.phoneModel}
                      </p>
                    </div>
                    <div className="ml-auto">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-purple-600"
                        onClick={() => {
                          navigator.clipboard.writeText(repair.phoneModel);
                          toast.success(t("common.copied"));
                        }}
                        title={t("common.copy")}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Branch Information */}
                  {repair.branch && (
                    <div className="flex items-center">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                        <div className="w-3 h-3 bg-blue-600 rounded-full"></div>
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-300 font-medium">
                          Branch
                        </p>
                        <p className="text-base font-medium dark:text-white">
                          {repair.branch.name} ({repair.branch.code})
                        </p>
                      </div>
                      <div className="ml-auto">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-500 hover:text-blue-600"
                          onClick={() => {
                            navigator.clipboard.writeText(
                              `${repair.branch?.name} (${repair.branch?.code})`
                            );
                            toast.success(t("common.copied"));
                          }}
                          title={t("common.copy")}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Ticket Number */}
                  {repair.ticketNumber && (
                    <div className="flex items-center">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center mr-3">
                        <Ticket className="h-5 w-5 text-amber-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-300 font-medium">
                          {t("repair.ticketNumber")}
                        </p>
                        <p className="text-base font-medium dark:text-white">
                          #{repair.ticketNumber}
                        </p>
                      </div>
                      <div className="ml-auto">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-500 hover:text-amber-600"
                          onClick={() => {
                            navigator.clipboard.writeText(
                              repair.ticketNumber?.toString() || ""
                            );
                            toast.success(t("common.copied"));
                          }}
                          title={t("common.copy")}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Creation Date */}
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                      <Calendar className="h-5 w-5 text-gray-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 dark:text-gray-300 font-medium">
                        {t("repair.createdAt")}
                      </p>
                      <p className="text-base font-medium dark:text-white">
                        {format(
                          new Date(repair.createdAt),
                          "dd/MM/yyyy - HH:mm"
                        )}
                      </p>
                    </div>
                  </div>

                  {/* Completion Date (if available) */}
                  {repair.completedAt && (
                    <div className="flex items-center">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 dark:text-gray-300 font-medium">
                          {t("repair.completedAt")}
                        </p>
                        <p className="text-base font-medium dark:text-white">
                          {format(
                            new Date(repair.completedAt),
                            "dd/MM/yyyy - HH:mm"
                          )}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden bg-card dark:bg-gray-800">
              <CardHeader className="bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 pb-2">
                <CardTitle className="flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
                  {t("repair.problemDescription")}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="flex items-start p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex-shrink-0 w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-3">
                    <FileText className="h-5 w-5 text-red-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-gray-700 dark:text-gray-300 whitespace-pre-wrap">
                      {repair.problemDescription}
                    </p>
                  </div>
                  <div className="ml-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-gray-500 hover:text-red-600"
                      onClick={() => {
                        navigator.clipboard.writeText(
                          repair.problemDescription
                        );
                        toast.success(t("common.copied"));
                      }}
                      title={t("common.copy")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden bg-card dark:bg-gray-800">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-cyan-50 dark:from-blue-900/20 dark:to-cyan-900/20 pb-2">
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2 text-blue-500" />
                  {t("repair.repairTimeline")}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <RepairTimeline repair={repair} />
              </CardContent>
            </Card>

            {/* Observations Card */}
            <Card className="overflow-hidden bg-card dark:bg-gray-800">
              <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 dark:from-purple-900/20 dark:to-indigo-900/20 pb-2">
                <CardTitle className="flex items-center">
                  <ClipboardList className="h-5 w-5 mr-2 text-purple-500" />
                  {t("repair.observations")}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4 space-y-4">
                {/* Add new observation */}
                <div className="flex gap-2 p-3 bg-purple-50 dark:bg-purple-900/20 rounded-lg">
                  <Input
                    placeholder={t("repair.addObservation") + "..."}
                    value={newObservation}
                    onChange={(e) => setNewObservation(e.target.value)}
                    className="flex-1 border-purple-200 focus-visible:ring-purple-500"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={handleAddObservation}
                    className="border-purple-200 hover:bg-purple-100 hover:text-purple-700"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                {/* List of observations */}
                <div className="space-y-3">
                  {repair.observations &&
                  Array.isArray(repair.observations) &&
                  repair.observations.length > 0 ? (
                    repair.observations.map((observation) => (
                      <div
                        key={observation.id}
                        className="flex items-start p-4 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600 hover:border-purple-200 dark:hover:border-purple-500 transition-colors"
                      >
                        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                          <ClipboardList className="h-4 w-4 text-purple-600" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium">
                            {observation.text}
                          </p>
                          <p className="text-xs text-gray-500 mt-1 flex items-center">
                            <Calendar className="h-3 w-3 mr-1 inline" />
                            {format(
                              new Date(observation.createdAt),
                              "dd/MM/yyyy - HH:mm"
                            )}
                          </p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 px-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                      <ClipboardList className="h-10 w-10 text-gray-300 dark:text-gray-500 mb-2" />
                      <p className="text-sm text-gray-500 dark:text-gray-300 text-center">
                        {t("repair.noObservations")}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            {/* Custom Form Response Card */}
            {customFormResponse && (
              <Card className="overflow-hidden bg-card dark:bg-gray-800">
                <CardHeader className="bg-gradient-to-r from-teal-50 to-cyan-50 dark:from-teal-900/20 dark:to-cyan-900/20 pb-2">
                  <CardTitle className="flex items-center">
                    <FileText className="h-5 w-5 mr-2 text-teal-500" />
                    {customFormResponse.template?.name || "Custom Form"}
                  </CardTitle>
                  {customFormResponse.template?.device_type && (
                    <p className="text-sm text-gray-600 dark:text-gray-300">
                      Device Type: {customFormResponse.template.device_type}
                    </p>
                  )}
                </CardHeader>
                <CardContent className="pt-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {Object.entries(customFormResponse.responses || {}).map(
                      ([field, value]) => (
                        <div
                          key={field}
                          className="p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                        >
                          <div className="flex items-start justify-between">
                            <div className="flex-1">
                              <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                                {field}
                              </p>
                              <p className="text-base dark:text-white">
                                {value === true
                                  ? "Yes"
                                  : value === false
                                  ? "No"
                                  : value || "N/A"}
                              </p>
                            </div>
                            <Button
                              variant="ghost"
                              size="icon"
                              className="h-6 w-6 text-gray-400 hover:text-teal-600"
                              onClick={() => {
                                navigator.clipboard.writeText(String(value));
                                toast.success(t("common.copied"));
                              }}
                              title={t("common.copy")}
                            >
                              <Copy className="h-3 w-3" />
                            </Button>
                          </div>
                        </div>
                      )
                    )}
                  </div>
                  <div className="mt-4 pt-3 border-t border-gray-200 dark:border-gray-600">
                    <p className="text-xs text-gray-500 dark:text-gray-300 flex items-center">
                      <Calendar className="h-3 w-3 mr-1" />
                      Form completed:{" "}
                      {format(
                        new Date(customFormResponse.created_at),
                        "dd/MM/yyyy - HH:mm"
                      )}
                    </p>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Stock Items Card */}
            <Card className="overflow-hidden bg-card dark:bg-gray-800">
              <CardHeader className="bg-gradient-to-r from-orange-50 to-amber-50 dark:from-orange-900/20 dark:to-amber-900/20 pb-2">
                <div className="flex items-center justify-between">
                  <CardTitle className="flex items-center">
                    <Package className="h-5 w-5 mr-2 text-orange-500" />
                    Stock Items Used
                  </CardTitle>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setShowAddStockDialog(true)}
                    className="text-orange-600 hover:text-orange-700"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Add Item
                  </Button>
                </div>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="space-y-3">
                  {repairStockItems.length === 0 ? (
                    <div className="text-center py-8 text-gray-500">
                      <Package className="h-12 w-12 mx-auto mb-2 opacity-50" />
                      <p>No stock items used</p>
                    </div>
                  ) : (
                    repairStockItems.map((item) => (
                      <div
                        key={item.id}
                        className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg"
                      >
                        <div className="flex-1">
                          <p className="font-medium dark:text-white">
                            {item.product?.name}
                          </p>
                          <p className="text-sm text-gray-600 dark:text-gray-300">
                            SKU: {item.product?.sku} • Unit Price:{" "}
                            {item.unit_price.toFixed(3)} TND
                          </p>
                        </div>
                        <div className="flex items-center gap-3">
                          <div className="text-right">
                            <p className="font-medium dark:text-white">
                              Qty: {item.quantity}
                            </p>
                            <p className="text-sm font-bold text-orange-600 dark:text-orange-400">
                              {item.total_price.toFixed(3)} TND
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() =>
                              handleRemoveStockItem(
                                item.id,
                                item.product_id,
                                item.quantity
                              )
                            }
                            className="text-red-500 hover:text-red-700 hover:bg-red-50"
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))
                  )}
                  {repairStockItems.length > 0 && (
                    <div className="border-t border-gray-200 dark:border-gray-600 pt-3">
                      <div className="flex justify-between items-center">
                        <span className="font-medium dark:text-white">
                          Total Stock Cost:
                        </span>
                        <span className="font-bold text-lg text-orange-600 dark:text-orange-400">
                          {repairStockItems
                            .reduce(
                              (sum, item) => sum + parseFloat(item.total_price),
                              0
                            )
                            .toFixed(3)}{" "}
                          TND
                        </span>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden bg-card dark:bg-gray-800">
              <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 pb-2">
                <CardTitle className="flex items-center">
                  <Receipt className="h-5 w-5 mr-2 text-green-600" />
                  {t("repair.paymentStatus")}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4 space-y-6">
                {/* Initial Price (Read-only) */}
                <div className="flex items-center p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="flex-shrink-0 w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                    <Receipt className="h-5 w-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-500 dark:text-gray-300 font-medium">
                      {t("repair.initialPrice")}
                    </p>
                    <p className="text-lg font-semibold dark:text-white">
                      {formatCurrency(repair.repairPrice)}
                    </p>
                  </div>
                </div>

                {/* Price Modifications */}
                {repair.priceModifications &&
                  repair.priceModifications.length > 0 && (
                    <div className="space-y-3">
                      <h3 className="text-sm font-medium text-gray-500 flex items-center">
                        <AlertTriangle className="h-4 w-4 mr-1 text-amber-500" />
                        {t("repair.priceModifications")}
                      </h3>
                      <div className="space-y-2">
                        {repair.priceModifications.map((mod) => (
                          <div
                            key={mod.id}
                            className="flex items-start p-3 bg-gray-50 dark:bg-gray-700 rounded-lg border border-gray-100 dark:border-gray-600"
                          >
                            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center mr-3">
                              {mod.amount > 0 ? (
                                <ArrowLeft className="h-4 w-4 text-green-600 rotate-[135deg]" />
                              ) : (
                                <ArrowLeft className="h-4 w-4 text-red-600 rotate-[315deg]" />
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="flex justify-between">
                                <span className="text-sm font-medium dark:text-white">
                                  {mod.reason}
                                </span>
                                <span
                                  className={`text-sm font-bold ${
                                    mod.amount > 0
                                      ? "text-green-600"
                                      : "text-red-600"
                                  }`}
                                >
                                  {mod.amount > 0 ? "+" : ""}
                                  {formatCurrency(mod.amount)}
                                </span>
                              </div>
                              <p className="text-xs text-gray-500 dark:text-gray-300 mt-1 flex items-center">
                                <Calendar className="h-3 w-3 mr-1 inline" />
                                {format(
                                  new Date(mod.createdAt),
                                  "dd/MM/yyyy - HH:mm"
                                )}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                {/* Add Price Modification */}
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-gray-500 flex items-center">
                    <Plus className="h-4 w-4 mr-1 text-blue-500" />
                    {t("repair.addPriceModification")}
                  </h3>
                  <div className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div className="flex flex-col gap-3">
                      <Input
                        type="number"
                        placeholder={t("repair.amount")}
                        value={
                          newPriceModification.amount === 0
                            ? ""
                            : newPriceModification.amount
                        }
                        onChange={(e) =>
                          setNewPriceModification({
                            ...newPriceModification,
                            amount: parseFloat(e.target.value) || 0,
                          })
                        }
                        className="flex-1 border-blue-200 focus-visible:ring-blue-500"
                      />
                      <div className="flex gap-2">
                        <Input
                          placeholder={t("repair.reason")}
                          value={newPriceModification.reason}
                          onChange={(e) =>
                            setNewPriceModification({
                              ...newPriceModification,
                              reason: e.target.value,
                            })
                          }
                          className="flex-1 border-blue-200 focus-visible:ring-blue-500"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={handleAddPriceModification}
                          disabled={
                            !newPriceModification.reason.trim() ||
                            newPriceModification.amount === 0
                          }
                          className="border-blue-200 hover:bg-blue-100 hover:text-blue-700"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Summary Section */}
                <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg space-y-3 border border-gray-200 dark:border-gray-600">
                  {/* Total Price (with modifications) */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-300">
                      {t("repair.totalPrice")}
                    </span>
                    <span className="text-lg font-bold dark:text-white">
                      {formatCurrency(
                        repair.repairPrice +
                          (repair.priceModifications?.reduce(
                            (sum, mod) => sum + mod.amount,
                            0
                          ) || 0)
                      )}
                    </span>
                  </div>

                  {/* Down Payment */}
                  <div className="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-600">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-300">
                      {t("repair.downPayment")}
                    </span>
                    <span className="text-base font-medium dark:text-white">
                      {formatCurrency(repair.downPayment)}
                    </span>
                  </div>

                  {/* Balance */}
                  <div className="flex items-center justify-between pt-2 border-t border-gray-200 dark:border-gray-600">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-300">
                      {t("common.balance")}
                    </span>
                    <span className="text-lg font-bold dark:text-white">
                      {formatCurrency(
                        Math.max(
                          0,
                          repair.repairPrice +
                            (repair.priceModifications?.reduce(
                              (sum, mod) => sum + mod.amount,
                              0
                            ) || 0) -
                            repair.downPayment
                        )
                      )}
                    </span>
                  </div>

                  {/* Payment Status */}
                  <div className="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-600">
                    <span className="text-sm font-medium text-gray-500 dark:text-gray-300">
                      {t("repair.paymentStatus")}
                    </span>
                    <Badge
                      className={`${
                        paymentStatusColors[repair.paymentStatus]
                      } px-3 py-1 text-sm`}
                    >
                      {t(`repair.payment.${repair.paymentStatus}`)}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card className="overflow-hidden bg-card dark:bg-gray-800">
              <CardHeader className="bg-gradient-to-r from-indigo-50 to-violet-50 dark:from-indigo-900/20 dark:to-violet-900/20 pb-2">
                <CardTitle className="flex items-center">
                  <QrCode className="h-5 w-5 mr-2 text-indigo-500" />
                  {t("repair.scanQrCode")}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4 flex flex-col items-center">
                {qrCode ? (
                  <div className="p-4 bg-white dark:bg-gray-800 rounded-lg border border-indigo-100 dark:border-indigo-800 shadow-sm">
                    <img
                      src={qrCode}
                      alt="QR Code"
                      className="w-48 h-48 object-contain"
                    />
                  </div>
                ) : (
                  <div className="w-48 h-48 bg-gray-100 dark:bg-gray-800 flex items-center justify-center rounded-lg border border-gray-200 dark:border-gray-700">
                    <div className="animate-pulse flex flex-col items-center">
                      <div className="h-10 w-10 bg-gray-200 rounded-full mb-2"></div>
                      <div className="h-4 w-24 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                )}
                <div className="mt-4 flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700"
                    onClick={handlePrintQR}
                  >
                    <Printer className="h-4 w-4" />
                    {t("common.print")}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700"
                    onClick={handleDownloadQR}
                  >
                    <Download className="h-4 w-4" />
                    {t("common.download")}
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden bg-card dark:bg-gray-800">
              <CardHeader className="bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 pb-2">
                <CardTitle className="flex items-center">
                  <Wrench className="h-5 w-5 mr-2 text-amber-500" />
                  {t("repair.updateStatus")}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4 space-y-4">
                {/* Repair Status */}
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700">
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-300 mb-2 flex items-center">
                    <Clock className="h-4 w-4 mr-1 text-amber-500" />
                    {t("repair.stat")}
                  </div>
                  <Select
                    value={repair.status}
                    onValueChange={handleStatusChange}
                  >
                    <SelectTrigger className="border-amber-200 focus:ring-amber-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                          {t("repair.status.pending")}
                        </div>
                      </SelectItem>
                      <SelectItem value="inProgress">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                          {t("repair.status.inProgress")}
                        </div>
                      </SelectItem>
                      <SelectItem value="completed">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                          {t("repair.status.completed")}
                        </div>
                      </SelectItem>
                      <SelectItem value="returned">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-purple-500 mr-2"></div>
                          {t("repair.status.returned")}
                        </div>
                      </SelectItem>
                      <SelectItem value="cancelled">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                          {t("repair.status.cancelled")}
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Payment Status */}
                <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700">
                  <div className="text-sm font-medium text-gray-500 dark:text-gray-300 mb-2 flex items-center">
                    <Receipt className="h-4 w-4 mr-1 text-green-500" />
                    {t("repair.paymentStatus")}
                  </div>
                  <Select
                    value={repair.paymentStatus}
                    onValueChange={handlePaymentStatusChange}
                  >
                    <SelectTrigger className="border-green-200 focus:ring-green-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="paid">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                          {t("repair.payment.paid")}
                        </div>
                      </SelectItem>
                      <SelectItem value="partial">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                          {t("repair.payment.partial")}
                        </div>
                      </SelectItem>
                      <SelectItem value="unpaid">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                          {t("repair.payment.unpaid")}
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Technician Assignment */}
                {availableTechnicians.length > 0 && (
                  <div className="p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-100 dark:border-gray-700">
                    <div className="text-sm font-medium text-gray-500 dark:text-gray-300 mb-2 flex items-center">
                      <User className="h-4 w-4 mr-1 text-blue-500" />
                      Assigned Technician
                    </div>
                    <Select
                      value={repair.assignedTechnician || "none"}
                      onValueChange={handleTechnicianChange}
                    >
                      <SelectTrigger className="border-blue-200 focus:ring-blue-500">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="none">No assignment</SelectItem>
                        {availableTechnicians.map((tech) => (
                          <SelectItem key={tech.user_id} value={tech.user_id}>
                            {tech.email} ({tech.role})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                )}

                {/* Print Ticket Button */}
                <Button
                  variant="default"
                  onClick={handleOpenPrintDialog}
                  className="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600"
                >
                  <Printer className="h-4 w-4" />
                  {t("repair.printTicket")}
                </Button>
              </CardContent>
              <CardFooter className="pt-0">
                <Dialog
                  open={deleteDialogOpen}
                  onOpenChange={setDeleteDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full text-red-500 hover:bg-red-50 border-red-200 mt-2"
                    >
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      {t("repair.deleteRepair")}
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle className="flex items-center text-red-500">
                        <AlertTriangle className="h-5 w-5 mr-2" />
                        {t("common.areYouSure")}
                      </DialogTitle>
                      <DialogDescription>
                        {t("repair.deleteConfirmation")}
                      </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                      <Label
                        htmlFor="delete-code"
                        className="mb-2 block font-medium"
                      >
                        {t("repair.enterDeleteCode")}:
                      </Label>
                      <Input
                        id="delete-code"
                        type="password"
                        value={deleteCode}
                        onChange={(e) => setDeleteCode(e.target.value)}
                        placeholder="******"
                        className="mb-2 border-red-200 focus-visible:ring-red-500"
                      />
                      {deleteError && (
                        <p className="text-sm text-red-500 mt-1 flex items-center">
                          <X className="h-4 w-4 mr-1" />
                          {deleteError}
                        </p>
                      )}
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setDeleteDialogOpen(false);
                          setDeleteCode(""); // Reset code when canceling
                          setDeleteError(""); // Reset error when canceling
                        }}
                      >
                        {t("common.cancel")}
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={handleDelete}
                        disabled={!deleteCode} // Disable if no code entered
                        className="bg-red-500 hover:bg-red-600"
                      >
                        {t("common.delete")}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>

                {/* Add Stock Item Dialog */}
                <Dialog
                  open={showAddStockDialog}
                  onOpenChange={setShowAddStockDialog}
                >
                  <DialogContent className="sm:max-w-md">
                    <DialogHeader>
                      <DialogTitle>Add Stock Item</DialogTitle>
                      <DialogDescription>
                        Select a product to add to this repair
                      </DialogDescription>
                    </DialogHeader>
                    <div className="space-y-4 py-4">
                      <div>
                        <Label
                          htmlFor="product-select"
                          className="text-sm font-medium"
                        >
                          Product
                        </Label>
                        <Select
                          value={selectedProductId}
                          onValueChange={setSelectedProductId}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select a product" />
                          </SelectTrigger>
                          <SelectContent>
                            {availableProducts.map((product) => (
                              <SelectItem key={product.id} value={product.id}>
                                {product.name} - {product.price.toFixed(3)} TND
                                (Stock: {product.stock_quantity})
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <div>
                        <Label
                          htmlFor="quantity"
                          className="text-sm font-medium"
                        >
                          Quantity
                        </Label>
                        <Input
                          id="quantity"
                          type="number"
                          min="1"
                          value={stockQuantity}
                          onChange={(e) =>
                            setStockQuantity(parseInt(e.target.value) || 1)
                          }
                          className="mt-1"
                        />
                      </div>
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setShowAddStockDialog(false);
                          setSelectedProductId("");
                          setStockQuantity(1);
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={handleAddStockItem}
                        disabled={!selectedProductId || stockQuantity <= 0}
                      >
                        Add Item
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RepairDetail;
