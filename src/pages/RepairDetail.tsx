import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import { useRepairContext } from "@/context/RepairContext";

import { useKeyboardShortcuts } from "@/hooks/useKeyboardShortcuts";
import { supabase } from "@/integrations/supabase/client";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogFooter,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { format } from "date-fns";
import { generateQRCodeDataURL } from "@/utils/qrCodeUtils";
import { generateRepairBarcode } from "@/utils/barcodeUtils";
import {
  generateTicketNumber,
  formatTicketNumber,
} from "@/utils/ticketNumberUtils";
import { toast } from "sonner";
import {
  ArrowLeft,
  Printer,
  Download,
  AlertTriangle,
  CheckCircle2,
  Phone,
  Smartphone,
  Calendar,
  Receipt,
  FileText,
  Ticket,
  Plus,
  X,
  ClipboardList,
  QrCode,
  Copy,
  PhoneCall,
  User,
  Clock,
  Wrench,
} from "lucide-react";
import {
  RepairItem,
  Observation,
  PriceModification,
  StatusHistory,
} from "@/types";
import RepairTimeline from "@/components/RepairTimeline";
import { logoConfig, logoPrintConfig } from "@/config/appConfig";

const RepairDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const { getRepairById, updateRepair, deleteRepair, repairs } =
    useRepairContext();
  const navigate = useNavigate();
  const [repair, setRepair] = useState<RepairItem | undefined>(undefined);
  const [qrCode, setQrCode] = useState<string | null>(null);
  const [barcode, setBarcode] = useState<string | null>(null);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [printDialogOpen, setPrintDialogOpen] = useState(false);
  const [deleteCode, setDeleteCode] = useState("");
  const [deleteError, setDeleteError] = useState("");
  const [loading, setLoading] = useState(true);
  const [codeType, setCodeType] = useState<"qr" | "barcode">("qr"); // Default to QR code
  const [newObservation, setNewObservation] = useState("");
  const [newPriceModification, setNewPriceModification] = useState({
    amount: 0,
    reason: "",
  });
  const { t, i18n } = useTranslation();

  // Set up keyboard shortcuts
  useKeyboardShortcuts({
    onPrint: () => {
      if (repair) {
        handleOpenPrintDialog();
      }
    },
  });

  useEffect(() => {
    const fetchRepair = async () => {
      if (id) {
        setLoading(true);
        try {
          // Use cached data first, only force refresh if needed
          const foundRepair = await getRepairById(id, false);
          setRepair(foundRepair);

          if (foundRepair) {
            generateQRCode(foundRepair.id);
            generateBarcode(false); // Generate regular barcode for standalone printing
          }
        } catch (error) {
          console.error("Error fetching repair:", error);
        } finally {
          setLoading(false);
        }
      }
    };

    fetchRepair();

    // Set up real-time subscription for this specific repair
    if (id) {
      const repairSubscription = supabase
        .channel(`repair-${id}`)
        .on(
          "postgres_changes",
          {
            event: "UPDATE",
            schema: "public",
            table: "repairs",
            filter: `id=eq.${id}`,
          },
          async (payload) => {
            console.log("Repair updated:", payload);
            // Force refresh to get the latest data from database
            const updatedRepair = await getRepairById(id, true);
            if (updatedRepair) {
              setRepair(updatedRepair);
            }
          }
        )
        .subscribe();

      // Clean up subscription when component unmounts
      return () => {
        supabase.removeChannel(repairSubscription);
      };
    }
  }, [id, getRepairById]);

  const generateQRCode = async (repairId: string) => {
    try {
      const qrDataUrl = await generateQRCodeDataURL(repairId);
      setQrCode(qrDataUrl);
    } catch (error) {
      console.error("Failed to generate QR code:", error);
    }
  };

  const generateBarcode = async (isForTicket: boolean = false) => {
    try {
      if (!repair) return;

      // Always use the ticket number for the barcode
      const ticketNumber = repair.ticketNumber?.toString() || "";

      if (ticketNumber) {
        console.log("Generating barcode for ticket number:", ticketNumber);

        try {
          // Generate a barcode using ONLY the ticket number
          const barcodeDataUrl = await generateRepairBarcode(
            ticketNumber, // Use ticket number as the content
            isForTicket
          );
          setBarcode(barcodeDataUrl);
        } catch (barcodeError) {
          console.error("Error generating barcode:", barcodeError);
          toast.error(t("repair.scanError"));
        }
      } else {
        // If no ticket number is available, we can't generate a proper barcode
        console.error("No ticket number available for barcode generation");
        toast.error(t("repair.noTicketNumberForBarcode"));
      }
    } catch (error) {
      console.error("Failed to generate barcode:", error);
      toast.error(t("repair.scanError"));
    }
  };

  const handleStatusChange = async (value: string) => {
    if (repair && id) {
      const statusUpdate = value as
        | "pending"
        | "inProgress"
        | "completed"
        | "cancelled"
        | "returned";

      // Don't update if status hasn't changed
      if (statusUpdate === repair.status) {
        return;
      }

      const updates: Partial<RepairItem> = {
        status: statusUpdate,
      };

      if (statusUpdate === "completed" && repair.status !== "completed") {
        updates.completedAt = new Date();
      }

      // If status is changed to "returned" from "completed", keep the completedAt date
      if (
        statusUpdate === "returned" &&
        repair.status !== "returned" &&
        !repair.completedAt
      ) {
        updates.completedAt = new Date();
      }

      try {
        // Only add a status history entry if the status is actually changing
        // and we're not in the initial creation (where status history is already initialized)
        if (repair.statusHistory && repair.statusHistory.length > 0) {
          // Check if the last status entry is different from the new one
          const lastStatusEntry =
            repair.statusHistory[repair.statusHistory.length - 1];

          // Only add a new entry if the status is different
          if (lastStatusEntry.status !== statusUpdate) {
            // Create a new status history entry
            const newStatusHistory: StatusHistory = {
              id: crypto.randomUUID(),
              status: statusUpdate,
              createdAt: new Date(),
              userId: repair.userId,
            };

            // Add to existing history
            const updatedStatusHistory = [
              ...repair.statusHistory,
              newStatusHistory,
            ];
            updates.statusHistory = updatedStatusHistory;
          }
        } else {
          // If no status history exists yet, initialize it
          const newStatusHistory: StatusHistory = {
            id: crypto.randomUUID(),
            status: statusUpdate,
            createdAt: new Date(),
            userId: repair.userId,
          };

          updates.statusHistory = [newStatusHistory];
        }
      } catch (error) {
        console.log("Status history might not be available yet, skipping");
        // Continue without status history
      }

      await updateRepair(id, updates);
      setRepair({ ...repair, ...updates });
      toast.success(t("repair.successUpdated"));
    }
  };

  const handlePaymentStatusChange = async (value: string) => {
    if (repair && id) {
      const paymentStatusUpdate = value as "paid" | "partial" | "unpaid";
      await updateRepair(id, { paymentStatus: paymentStatusUpdate });
      setRepair({ ...repair, paymentStatus: paymentStatusUpdate });
      toast.success(t("repair.successUpdated"));
    }
  };

  const handleAddObservation = async () => {
    if (repair && id && newObservation.trim()) {
      try {
        // Create a new observation object for local state update
        const newObservationObj = {
          id: crypto.randomUUID(),
          text: newObservation.trim(),
          createdAt: new Date(),
          userId: repair.userId,
        };

        // Send only the new observation text as a string array
        // The updateRepair function will convert it to an Observation object
        await updateRepair(id, {
          observations: [newObservation.trim()] as unknown as Observation[],
        });

        // Clear the input immediately
        setNewObservation("");

        // Fetch the latest repair data to ensure we have the most up-to-date observations
        const updatedRepair = await getRepairById(id);
        if (updatedRepair) {
          setRepair(updatedRepair);
        } else {
          // Fallback to local update if fetch fails
          setRepair({
            ...repair,
            observations: [...(repair.observations || []), newObservationObj],
          });
        }

        toast.success(t("repair.observationAdded"));
      } catch (error) {
        console.error("Error adding observation:", error);
        toast.error(t("repair.observationAddError"));
      }
    }
  };

  const handleAddPriceModification = async () => {
    if (
      repair &&
      id &&
      newPriceModification.reason.trim() &&
      newPriceModification.amount !== 0
    ) {
      try {
        // Create a new price modification object for local state update
        const newPriceModificationObj = {
          id: crypto.randomUUID(),
          amount: newPriceModification.amount,
          reason: newPriceModification.reason.trim(),
          createdAt: new Date(),
          userId: repair.userId,
        };

        // Send the new price modification object
        await updateRepair(id, {
          priceModifications: [newPriceModificationObj] as PriceModification[],
        });

        // Clear the input immediately
        setNewPriceModification({ amount: 0, reason: "" });

        // Fetch the latest repair data to ensure we have the most up-to-date price modifications
        const updatedRepair = await getRepairById(id);
        if (updatedRepair) {
          setRepair(updatedRepair);
        } else {
          // Fallback to local update if fetch fails
          setRepair({
            ...repair,
            priceModifications: [
              ...(repair.priceModifications || []),
              newPriceModificationObj,
            ],
          });
        }

        toast.success(t("repair.priceModificationAdded"));
      } catch (error) {
        console.error("Error adding price modification:", error);
        toast.error(t("repair.priceModificationAddError"));
      }
    }
  };

  const handleDelete = async () => {
    // Get the deletion code from environment variables
    const correctCode = "123456";

    // Reset error state
    setDeleteError("");

    // Check if the entered code matches the correct code
    if (deleteCode !== correctCode) {
      setDeleteError(t("repair.invalidDeleteCode"));
      return;
    }

    if (id) {
      await deleteRepair(id);
      toast.success(t("repair.successDeleted"));
      setDeleteDialogOpen(false);
      setDeleteCode(""); // Reset the code input
      navigate("/");
    }
  };

  const handlePrintQR = () => {
    if (!qrCode) return;

    // Open a window that will adapt to the printer's capabilities
    const printWindow = window.open(
      "",
      "_blank",
      "width=400,height=400,menubar=no,toolbar=no,location=no,scrollbars=yes,status=no,resizable=yes"
    );
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>${t("repair.scanQrCode")} - ${repair?.customerName}</title>
            <style>
              @page {
                margin: 0;
                size: auto;
              }
              html, body {
                height: auto;
                overflow: visible;
              }
              body {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 0;
                margin: 0;
                font-family: Arial, sans-serif;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
              }
              .qr-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                border: 1px solid #000;
                padding: 10px;
                margin-top: 0;
                width: calc(100% - 20px);
                box-sizing: border-box;
              }
              img {
                width: 200px;
                height: 200px;
              }
              .details {
                margin-top: 10px;
                text-align: center;
              }
              h3 {
                margin: 5px 0;
              }
              p {
                margin: 2px 0;
                font-size: 14px;
              }
            </style>
          </head>
          <body>
            <div class="qr-container">
              <img src="${qrCode}" alt="QR Code" />
              <div class="details">
                <h3>${repair?.customerName}</h3>
                <p>${repair?.phoneModel}</p>
                <p>${format(
                  new Date(repair?.createdAt || new Date()),
                  "dd/MM/yyyy - HH:mm"
                )}</p>
              </div>
            </div>
            <script>
              // Print immediately without waiting for full load
              document.addEventListener('DOMContentLoaded', function() {
                // Small timeout to ensure the browser has rendered the content
                setTimeout(function() {
                  window.print();
                  window.onafterprint = function() {
                    window.close();
                  };
                }, 100);
              });

              // Fallback in case DOMContentLoaded doesn't trigger
              window.onload = function() {
                window.print();
                window.onafterprint = function() {
                  window.close();
                };
              };
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  const handlePrintBarcode = async () => {
    if (!repair) return;

    // Generate a standalone barcode (larger, with text)
    await generateBarcode(false);

    if (!barcode) return;

    // Open a window that will adapt to the printer's capabilities
    const printWindow = window.open(
      "",
      "_blank",
      "width=400,height=200,menubar=no,toolbar=no,location=no,scrollbars=yes,status=no,resizable=yes"
    );
    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <title>${t("repair.scanBarcode")} - ${repair?.customerName}</title>
            <style>
              @page {
                margin: 0;
                size: auto;
              }
              html, body {
                height: auto;
                overflow: visible;
              }
              body {
                display: flex;
                flex-direction: column;
                align-items: center;
                padding: 0;
                margin: 0;
                font-family: Arial, sans-serif;
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
              }
              .barcode-container {
                display: flex;
                flex-direction: column;
                align-items: center;
                border: 1px solid #000;
                padding: 10px;
                margin-top: 0;
                width: calc(100% - 20px);
                box-sizing: border-box;
              }
              img {
                width: 95%;
                max-width: 350px;
                height: auto;
                margin-top: 10px; /* Add some space at the top */
              }
              .details {
                margin-top: 10px;
                text-align: center;
              }
              h3 {
                margin: 5px 0;
              }
              p {
                margin: 2px 0;
                font-size: 14px;
              }
            </style>
          </head>
          <body>
            <div class="barcode-container">
              <img src="${barcode}" alt="Barcode" />
              <div class="details">
                <h3>${repair?.customerName}</h3>
                <p>${repair?.phoneModel}</p>
                <p>${format(
                  new Date(repair?.createdAt || new Date()),
                  "dd/MM/yyyy - HH:mm"
                )}</p>
              </div>
            </div>
            <script>
              // Print immediately without waiting for full load
              document.addEventListener('DOMContentLoaded', function() {
                // Small timeout to ensure the browser has rendered the content
                setTimeout(function() {
                  window.print();
                  window.onafterprint = function() {
                    window.close();
                  };
                }, 100);
              });

              // Fallback in case DOMContentLoaded doesn't trigger
              window.onload = function() {
                window.print();
                window.onafterprint = function() {
                  window.close();
                };
              };
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  // Function to print a barcode sticker for the back of devices with more information
  const handlePrintBarcodeSticker = async () => {
    if (!repair) return;

    // Generate a compact barcode specifically for stickers
    try {
      // Always use the ticket number for the barcode
      const ticketNumber = repair.ticketNumber?.toString() || "";

      if (!ticketNumber) {
        toast.error(t("repair.noTicketNumberForBarcode"));
        return;
      }

      // Generate a compact barcode for stickers
      const stickerBarcode = await generateRepairBarcode(ticketNumber, false);

      // Truncate problem description if it's too long
      const truncateProblem = (text: string, maxLength: number = 50) => {
        if (text.length <= maxLength) return text;
        return text.substring(0, maxLength) + "...";
      };

      // Open a window optimized for larger sticker printing
      const printWindow = window.open(
        "",
        "_blank",
        "width=300,height=200,menubar=no,toolbar=no,location=no,scrollbars=yes,status=no,resizable=yes"
      );

      if (printWindow) {
        printWindow.document.write(`
          <html>
            <head>
              <title>${t("repair.barcodeSticker")} - ${
          repair?.customerName
        }</title>
              <style>
                @page {
                  margin: 0;
                  size: 50mm 30mm; /* Larger sticker size */
                }
                html, body {
                  height: 100%;
                  width: 100%;
                  overflow: hidden;
                  padding: 0;
                  margin: 0;
                }
                body {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  font-family: Arial, sans-serif;
                }
                .sticker-container {
                  display: flex;
                  flex-direction: column;
                  align-items: center;
                  justify-content: center;
                  width: 100%;
                  height: 100%;
                  box-sizing: border-box;
                  padding: 2mm;
                }
                .barcode-img {
                  width: 100%;
                  max-height: 12mm;
                }
                .ticket-number {
                  font-size: 8pt;
                  font-weight: bold;
                  margin-top: 1mm;
                  text-align: center;
                }
                .device-model {
                  font-size: 7pt;
                  font-weight: bold;
                  margin-top: 0.5mm;
                  text-align: center;
                  width: 100%;
                  white-space: nowrap;
                  overflow: hidden;
                  text-overflow: ellipsis;
                }
                .problem {
                  font-size: 6pt;
                  margin-top: 0.5mm;
                  text-align: center;
                  width: 100%;
                  max-height: 8mm;
                  overflow: hidden;
                }
                .shop-name {
                  font-size: 6pt;
                  margin-top: 0.5mm;
                  text-align: center;
                }
              </style>
            </head>
            <body>
              <div class="sticker-container">
                <img src="${stickerBarcode}" alt="Barcode" class="barcode-img" />
                <div class="ticket-number">#${repair.ticketNumber}</div>
                <div class="device-model">${repair.phoneModel || ""}</div>
                <div class="problem">${truncateProblem(
                  repair.problemDescription || ""
                )}</div>
                <div class="shop-name">${t("app.title")}</div>
              </div>
              <script>
                // Print immediately without waiting for full load
                document.addEventListener('DOMContentLoaded', function() {
                  setTimeout(function() {
                    window.print();
                    window.onafterprint = function() {
                      window.close();
                    };
                  }, 100);
                });

                // Fallback in case DOMContentLoaded doesn't trigger
                window.onload = function() {
                  window.print();
                  window.onafterprint = function() {
                    window.close();
                  };
                };
              </script>
            </body>
          </html>
        `);
        printWindow.document.close();
      }
    } catch (error) {
      console.error("Failed to generate barcode sticker:", error);
      toast.error(t("repair.scanError"));
    }
  };

  const handleDownloadQR = () => {
    if (!qrCode) return;

    const link = document.createElement("a");
    link.href = qrCode;
    link.download = `qr-code-${repair?.id}.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleOpenPrintDialog = () => {
    if (!repair || !qrCode) return;
    setPrintDialogOpen(true);
  };

  const handlePrintTechnicianTicketOnly = async () => {
    if (!repair) return;

    // Generate a ticket-specific barcode (smaller) if barcode is selected
    if (codeType === "barcode") {
      await generateBarcode(true);
    }

    // Use the stored ticket number or generate one if not available
    const ticketNumber = repair.ticketNumber
      ? repair.ticketNumber.toString()
      : generateTicketNumber(repair, repairs);
    const formattedTicketNumber = formatTicketNumber(ticketNumber, "#");

    try {
      await printTechnicianTicket(formattedTicketNumber, ticketNumber);
      toast.success("Technician ticket printed!");
    } catch (error) {
      console.error("Error printing technician ticket:", error);
      toast.error("Failed to print technician ticket");
    }
  };

  const handlePrintTicket = async () => {
    if (!repair || !qrCode) return;

    // Generate a ticket-specific barcode (smaller) if barcode is selected
    if (codeType === "barcode") {
      await generateBarcode(true);
    }

    // Use the stored ticket number or generate one if not available
    const ticketNumber = repair.ticketNumber
      ? repair.ticketNumber.toString()
      : generateTicketNumber(repair, repairs);
    const formattedTicketNumber = formatTicketNumber(ticketNumber, "#");

    // Print both tickets as a single 2-page document
    try {
      console.log("Starting to print combined ticket (2 pages)...");
      await printCombinedTickets(formattedTicketNumber, ticketNumber);
      console.log("Combined ticket print initiated");
    } catch (error) {
      console.error("Error printing combined tickets:", error);
      toast.error("Failed to print tickets");
    }
  };

  const printCombinedTickets = async (
    formattedTicketNumber: string,
    ticketNumber: string
  ) => {
    console.log("printCombinedTickets called", {
      formattedTicketNumber,
      ticketNumber,
    });

    if (!repair || !qrCode) {
      console.error("Missing repair or qrCode", {
        repair: !!repair,
        qrCode: !!qrCode,
      });
      return;
    }

    // Declare variables outside try block
    let technicianBarcode = "";
    let customerBarcode = "";
    let logoUrl = "";

    try {
      // Generate barcode for technician ticket
      console.log("Generating technician barcode...");
      technicianBarcode = await generateRepairBarcode(ticketNumber, false);

      // Generate barcode for customer ticket if needed
      if (codeType === "barcode") {
        console.log("Generating customer barcode...");
        customerBarcode = await generateRepairBarcode(ticketNumber, true);
      }

      // Get the absolute URL for the logo
      console.log("Getting logo URL...");
      logoUrl = new URL(logoPrintConfig.path, window.location.origin).href;
      console.log("Logo URL:", logoUrl);
    } catch (error) {
      console.error("Error in printCombinedTickets setup:", error);
      throw error;
    }

    // Open a single window for both tickets
    const printWindow = window.open(
      "",
      "combinedTickets" + Date.now(),
      "width=800,height=600,menubar=no,toolbar=no,location=no,scrollbars=yes,status=no,resizable=yes"
    );

    if (printWindow) {
      printWindow.document.write(`
        <html>
          <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Repair Tickets - ${repair.customerName}</title>
            <style>
              @page {
                size: 80mm auto;
                margin: 0;
              }
              @media print {
                html, body {
                  margin: 0 !important;
                  padding: 0 !important;
                  overflow: hidden;
                  width: 80mm !important;
                  max-width: 80mm !important;
                }
                .ticket {
                  width: 72mm !important;
                  max-width: 72mm !important;
                  margin: 0 4mm !important;
                  padding: 0 !important;
                  box-sizing: border-box !important;
                }
                .page-break {
                  page-break-before: always;
                }
              }
              html, body {
                height: auto;
                overflow: visible;
                margin: 0;
                padding: 0;
                width: 100%;
                max-width: 100%;
              }
              body {
                font-family: 'Arial', sans-serif;
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
                font-size: 10px;
                line-height: 1.4;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                position: relative;
                box-sizing: border-box;
                left: 0;
                font-weight: 900;
              }
              .ticket {
                width: 72mm;
                max-width: 72mm;
                margin: 0 4mm;
                padding: 0;
                position: relative;
                top: 0;
                left: 0;
                box-sizing: border-box;
              }
              .header {
                text-align: center;
                margin-bottom: 5px;
                border-bottom: 1px dashed #000;
                padding-bottom: 3px;
              }
              .logo-container {
                text-align: center;
                margin-bottom: 8px;
                padding-top: 5px;
              }
              .logo-container img {
                max-width: 80px;
                max-height: 80px;
                margin: 0 auto;
                display: block;
                object-fit: contain;
              }
              .shop-name {
                font-size: 16px;
                font-weight: 900;
                text-decoration: underline;
                margin-bottom: 2px;
              }
              .title {
                font-size: 14px;
                font-weight: 900;
                margin: 3px 0;
                border-bottom: 2px solid #000;
                padding: 2px 0;
                text-decoration: underline;
                text-align: center;
              }
              .section {
                margin-bottom: 5px;
                border-bottom: 1px dashed #000;
                padding-bottom: 3px;
              }
              .info-row {
                display: block;
                margin: 1px 0;
                width: 100%;
                box-sizing: border-box;
                overflow: hidden;
              }
              .label {
                font-weight: 900;
                text-decoration: underline;
                display: inline-block;
                width: 40%;
                padding-right: 2px;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
              }
              .value {
                font-weight: 900;
                text-align: right;
                display: inline-block;
                width: 58%;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
                font-size: 16px;
              }
              .qr-code {
                text-align: center;
                margin-top: 5px;
                width: 100%;
                box-sizing: border-box;
              }
              .qr-code img {
                width: 50px;
                height: 50px;
                max-width: 100%;
              }
              .barcode {
                text-align: center;
                margin-top: 5px;
                width: 100%;
                box-sizing: border-box;
                border-top: 1px dashed #000;
                padding-top: 5px;
              }
              .barcode img {
                width: 90%;
                max-width: 90%;
                height: 70px;
                margin: 0 auto;
                display: block;
              }
              .footer {
                text-align: center;
                margin-top: 5px;
                font-size: 10px;
                font-weight: 900;
                width: 100%;
                box-sizing: border-box;
              }
              .text-center {
                text-align: center;
              }
              p {
                margin: 1px 0;
                font-weight: 900;
                width: 100%;
                box-sizing: border-box;
                word-wrap: break-word;
                white-space: normal;
                overflow-wrap: break-word;
              }
              .timestamp {
                font-size: 8px;
                font-weight: 700;
                width: 100%;
                box-sizing: border-box;
                display: block;
              }

              /* Technician ticket specific styles */
              .technician-ticket .header {
                border-bottom: 2px solid #000;
              }
              .technician-ticket .title {
                font-size: 14px;
                font-weight: 900;
                margin: 3px 0;
                text-decoration: underline;
                text-align: center;
              }
              .technician-ticket .section {
                margin-bottom: 4px;
                border-bottom: 1px dashed #000;
                padding-bottom: 2px;
              }
              .technician-ticket .value {
                font-size: 11px;
              }
              .technician-ticket .barcode img {
                height: 50px;
              }
              .problem-box {
                margin-top: 3px;
                border: 1px solid #000;
                padding: 2px;
                background: #f9f9f9;
              }
              .problem-text {
                font-weight: 800;
                font-size: 18px;
                text-align: left;
                line-height: 1.2;
              }
            </style>
          </head>
          <body>
            <!-- PAGE 1: CUSTOMER TICKET -->
            <div style="width: 80mm; margin: 0; padding: 0; box-sizing: border-box;">
              <div class="ticket">
                <div class="header">
                  <div class="logo-container">
                    <img
                      src="${logoUrl}"
                      alt="${logoConfig.alt || t("app.title")}"
                      onerror="this.style.display='none'; document.getElementById('shop-name').style.fontSize='18px';"
                    />
                  </div>
                  <div id="shop-name" class="shop-name">${t("app.title")}</div>
                  <div style="font-size: 10px; font-weight: 900;">50 190 417</div>
                  <div>${t(
                    "repair.repairTicket"
                  )} ${formattedTicketNumber}</div>
                  <div>${format(new Date(), "dd/MM/yyyy - HH:mm")}</div>
                </div>

                <div class="section">
                  <div class="title">${t("repair.customerInfo")}</div>
                  <div class="info-row">
                    <span class="label">${t("repair.customerName")}:</span>
                    <span class="value">${repair.customerName}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">${t("repair.customerPhone")}:</span>
                    <span class="value">${repair.customerPhone}</span>
                  </div>
                </div>

                <div class="section">
                  <div class="title">${t("repair.repairDetails")}</div>
                  <div class="info-row">
                    <span class="label">${t("repair.phoneModel")}:</span>
                    <span class="value">${repair.phoneModel}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">${t("repair.stat")}:</span>
                    <span class="value">${t(
                      `repair.status.${repair.status}`
                    )}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">${t("repair.createdAt")}:</span>
                    <span class="value">${format(
                      new Date(repair.createdAt),
                      "dd/MM/yyyy - HH:mm"
                    )}</span>
                  </div>
                  <div style="margin-top: 4px; border: 1px solid #000; padding: 2px;">
                    <p style="font-weight: 900; text-align: center; text-decoration: underline; font-size: 10px; margin-bottom: 2px;">${t(
                      "repair.problemDescription"
                    )}:</p>
                    <p style="font-weight: 800; font-size: 9px; text-align: left;">${
                      repair.problemDescription
                    }</p>
                  </div>

                  ${
                    repair.observations &&
                    Array.isArray(repair.observations) &&
                    repair.observations.length > 0
                      ? `
                  <div style="margin-top: 3px; border-top: 1px dashed #000; padding-top: 2px;">
                    <p style="font-weight: 900; text-decoration: underline; font-size: 9px;">${t(
                      "repair.observations"
                    )}:</p>
                    ${repair.observations
                      .map(
                        (obs, index) =>
                          `<div style="margin-top: ${
                            index > 0 ? "3px" : "1px"
                          }; padding-left: 2px;">
                            <p style="font-weight: 900; font-size: 10px;">- ${
                              obs.text
                            }</p>
                            <p style="font-size: 8px; font-weight: 700; text-align: right;">${format(
                              new Date(obs.createdAt),
                              "dd/MM/yyyy - HH:mm"
                            )}</p>
                          </div>`
                      )
                      .join("")}
                  </div>
                  `
                      : ""
                  }
                </div>

                <div class="section">
                  <div class="title">${t("repair.paymentStatus")}</div>
                  <div class="info-row">
                    <span class="label">${t("repair.initialPrice")}:</span>
                    <span class="value" style="font-weight: 800;">${new Intl.NumberFormat(
                      i18n.language,
                      {
                        style: "currency",
                        currency: "TND",
                      }
                    ).format(repair.repairPrice)}</span>
                  </div>

                  ${
                    repair.priceModifications &&
                    repair.priceModifications.length > 0
                      ? `<div style="margin-top: 2px; border-top: 1px dashed #000; padding-top: 2px;">
                    <p style="font-weight: 900; text-decoration: underline; font-size: 8px;">${t(
                      "repair.priceModifications"
                    )}:</p>
                    ${repair.priceModifications
                      .map(
                        (mod, index) =>
                          `<div style="margin-top: ${
                            index > 0 ? "2px" : "1px"
                          };">
                            <div>
                              <span style="font-weight: 900; font-size: 10px; display: inline-block; width: 65%;">- ${
                                mod.reason
                              }:</span>
                              <span style="font-weight: 900; font-size: 10px; display: inline-block; width: 35%; text-align: right;">
                                ${
                                  mod.amount > 0 ? "+" : ""
                                }${new Intl.NumberFormat(i18n.language, {
                            style: "currency",
                            currency: "TND",
                          }).format(mod.amount)}
                              </span>
                            </div>
                            <p style="font-size: 8px; font-weight: 700; text-align: right; margin: 0;">
                              ${format(
                                new Date(mod.createdAt),
                                "dd/MM/yyyy - HH:mm"
                              )}
                            </p>
                          </div>`
                      )
                      .join("")}
                    <div style="border-top: 1px dotted #000; padding-top: 2px; margin-top: 2px;">
                      <span style="font-weight: 900; display: inline-block; width: 50%;">${t(
                        "repair.totalPrice"
                      )}:</span>
                      <span style="font-weight: 900; display: inline-block; width: 50%; text-align: right; font-size: 12px;">
                        ${new Intl.NumberFormat(i18n.language, {
                          style: "currency",
                          currency: "TND",
                        }).format(
                          repair.repairPrice +
                            (repair.priceModifications?.reduce(
                              (sum, mod) => sum + mod.amount,
                              0
                            ) || 0)
                        )}
                      </span>
                    </div>
                  </div>`
                      : ""
                  }

                <div style="margin-top: 3px; border-top: 1px dashed #000; padding-top: 2px;">
                  <div style="margin-bottom: 1px;">
                    <span style="font-weight: 700; display: inline-block; width: 50%;">${t(
                      "repair.downPayment"
                    )}:</span>
                    <span style="font-weight: 700; display: inline-block; width: 50%; text-align: right;">${new Intl.NumberFormat(
                      i18n.language,
                      {
                        style: "currency",
                        currency: "TND",
                      }
                    ).format(repair.downPayment)}</span>
                  </div>
                  <div style="margin-bottom: 1px;">
                    <span style="font-weight: 700; display: inline-block; width: 50%;">${t(
                      "common.balance"
                    )}:</span>
                    <span style="font-weight: 800; display: inline-block; width: 50%; text-align: right;">${new Intl.NumberFormat(
                      i18n.language,
                      {
                        style: "currency",
                        currency: "TND",
                      }
                    ).format(
                      Math.max(
                        0,
                        repair.repairPrice +
                          (repair.priceModifications?.reduce(
                            (sum, mod) => sum + mod.amount,
                            0
                          ) || 0) -
                          repair.downPayment
                      )
                    )}</span>
                  </div>
                  <div>
                    <span style="font-weight: 700; display: inline-block; width: 50%;">${t(
                      "repair.paymentStatus"
                    )}:</span>
                    <span style="font-weight: 800; display: inline-block; width: 50%; text-align: right;">${t(
                      `repair.payment.${repair.paymentStatus}`
                    )}</span>
                  </div>
                </div>
                </div>

                ${
                  codeType === "qr"
                    ? `<div class="qr-code">
                    <img src="${qrCode}" alt="QR Code" />
                    <div style="font-size: 8px; font-weight: 900; margin-top: 2px;">${formattedTicketNumber}</div>
                  </div>`
                    : `<div class="barcode">
                    <img src="${customerBarcode}" alt="Barcode" />
                    <div style="font-size: 8px; font-weight: 900; margin-top: 2px;">${formattedTicketNumber}</div>
                  </div>`
                }

                <div class="footer">
          
                  <p>${t("repair.thankYou")}</p>
                  <div style="margin-top: 8px; padding-top: 4px; border-top: 1px dashed #000; font-size: 8px; line-height: 1.2; text-align: center;">
                    <p>${t("repair.responsibilityDisclaimer")}</p>
                  </div>
                </div>
              </div>
            </div>

            <!-- PAGE 2: TECHNICIAN TICKET -->
            <div class="page-break" style="width: 80mm; margin: 0; padding: 0; box-sizing: border-box;">
              <div class="ticket technician-ticket">
                <div class="header">
                  <div class="title">TECHNICIAN TICKET</div>
                  <div>${formattedTicketNumber}</div>
                  <div>${format(new Date(), "dd/MM/yyyy - HH:mm")}</div>
                </div>

                <div class="section">
                  <div class="info-row">
                    <span class="label">${t("repair.customerName")}:</span>
                    <span class="value">${repair.customerName}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">${t("repair.phoneModel")}:</span>
                    <span class="value">${repair.phoneModel}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">${t("repair.createdAt")}:</span>
                    <span class="value">${format(
                      new Date(repair.createdAt),
                      "dd/MM/yyyy - HH:mm"
                    )}</span>
                  </div>
                </div>

                <div class="section">
                  <div class="problem-box">
                    <p style="font-weight: 900; text-align: center; text-decoration: underline; font-size: 9px; margin-bottom: 2px;">${t(
                      "repair.problemDescription"
                    )}:</p>
                    <p class="problem-text">${repair.problemDescription}</p>
                  </div>

                  ${
                    repair.observations &&
                    Array.isArray(repair.observations) &&
                    repair.observations.length > 0
                      ? `
                  <div style="margin-top: 3px; border-top: 1px dashed #000; padding-top: 2px;">
                    <p style="font-weight: 900; text-decoration: underline; font-size: 8px;">${t(
                      "repair.observations"
                    )}:</p>
                    ${repair.observations
                      .slice(-2) // Only show last 2 observations to save space
                      .map(
                        (obs) =>
                          `<div style="margin-top: 1px; padding-left: 2px;">
                            <p style="font-weight: 900; font-size: 8px;">- ${obs.text}</p>
                          </div>`
                      )
                      .join("")}
                  </div>
                  `
                      : ""
                  }
                </div>

                <div class="barcode">
                  <img src="${technicianBarcode}" alt="Barcode" />
                  <div style="font-size: 8px; font-weight: 900; margin-top: 2px;">${formattedTicketNumber}</div>
                </div>

                <div class="footer">
                  <p>FOR TECHNICIAN USE</p>
                </div>
              </div>
            </div>

            <script>
              document.addEventListener('DOMContentLoaded', function() {
                setTimeout(function() {
                  window.print();
                  window.onafterprint = function() {
                    window.close();
                  };
                }, 300);
              });

              window.onload = function() {
                window.print();
                window.onafterprint = function() {
                  window.close();
                };
              };
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
    }
  };

  // Removed unused printClientTicket function

  const printTechnicianTicket = async (
    formattedTicketNumber: string,
    ticketNumber: string
  ) => {
    console.log("printTechnicianTicket called with:", {
      formattedTicketNumber,
      ticketNumber,
    });

    if (!repair) {
      console.log("No repair found, returning");
      return;
    }

    console.log("Generating barcode for technician ticket...");
    // Generate barcode for technician ticket
    const technicianBarcode = await generateRepairBarcode(ticketNumber, false);
    console.log("Barcode generated:", technicianBarcode ? "success" : "failed");

    // Open a smaller window for technician ticket (shorter length)
    console.log("Opening print window for technician ticket...");
    const printWindow = window.open(
      "",
      "technicianTicket" + Date.now(), // Unique window name
      "width=302,height=400,menubar=no,toolbar=no,location=no,scrollbars=yes,status=no,resizable=yes"
    );

    if (printWindow) {
      console.log("Print window opened successfully for technician ticket");
      printWindow.document.write(`
        <html>
          <head>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Technician Ticket - ${repair.customerName}</title>
            <style>
              @page {
                size: 80mm auto; /* Set width to 80mm for thermal printer */
                margin: 0;
              }
              @media print {
                html, body {
                  margin: 0 !important;
                  padding: 0 !important;
                  overflow: hidden;
                  width: 80mm !important;
                  max-width: 80mm !important;
                }
                .ticket {
                  width: 72mm !important;
                  max-width: 72mm !important;
                  margin: 0 4mm !important;
                  padding: 0 !important;
                  box-sizing: border-box !important;
                }
              }
              html, body {
                height: auto;
                overflow: visible;
                margin: 0;
                padding: 0;
                width: 100%;
                max-width: 100%;
              }
              body {
                font-family: 'Arial', sans-serif;
                width: 100%;
                max-width: 100%;
                padding: 0;
                margin: 0;
                font-size: 10px;
                line-height: 1.4;
                -webkit-print-color-adjust: exact;
                color-adjust: exact;
                position: relative;
                box-sizing: border-box;
                left: 0;
                font-weight: 900;
              }
              .ticket {
                width: 72mm;
                max-width: 72mm;
                margin: 0 4mm;
                padding: 0;
                position: relative;
                top: 0;
                left: 0;
                box-sizing: border-box;
              }
              .header {
                text-align: center;
                margin-bottom: 5px;
                border-bottom: 2px solid #000;
                padding-bottom: 3px;
              }
              .title {
                font-size: 14px;
                font-weight: 900;
                margin: 3px 0;
                text-decoration: underline;
                text-align: center;
              }
              .section {
                margin-bottom: 4px;
                border-bottom: 1px dashed #000;
                padding-bottom: 2px;
              }
              .info-row {
                display: block;
                margin: 1px 0;
                width: 100%;
                box-sizing: border-box;
                overflow: hidden;
              }
              .label {
                font-weight: 900;
                text-decoration: underline;
                display: inline-block;
                width: 40%;
                padding-right: 2px;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
              }
              .value {
                font-weight: 900;
                text-align: right;
                display: inline-block;
                width: 58%;
                overflow: hidden;
                text-overflow: ellipsis;
                vertical-align: top;
                font-size: 11px;
              }
              .barcode {
                text-align: center;
                margin-top: 5px;
                width: 100%;
                box-sizing: border-box;
                border-top: 1px dashed #000;
                padding-top: 5px;
              }
              .barcode img {
                width: 90%;
                max-width: 90%;
                height: 50px;
                margin: 0 auto;
                display: block;
              }
              .footer {
                text-align: center;
                margin-top: 5px;
                font-size: 9px;
                font-weight: 900;
                width: 100%;
                box-sizing: border-box;
              }
              p {
                margin: 1px 0;
                font-weight: 900;
                width: 100%;
                box-sizing: border-box;
                word-wrap: break-word;
                white-space: normal;
                overflow-wrap: break-word;
              }
              .problem-box {
                margin-top: 3px;
                border: 1px solid #000;
                padding: 2px;
                background: #f9f9f9;
              }
              .problem-text {
                font-weight: 800;
                font-size: 9px;
                text-align: left;
                line-height: 1.2;
              }
            </style>
          </head>
          <body>
            <div style="width: 80mm; margin: 0; padding: 0; box-sizing: border-box;">
              <div class="ticket">
                <div class="header">
                  <div class="title">TECHNICIAN TICKET</div>
                  <div>${formattedTicketNumber}</div>
                  <div>${format(new Date(), "dd/MM/yyyy - HH:mm")}</div>
                </div>

                <div class="section">
                  <div class="info-row">
                    <span class="label">${t("repair.customerName")}:</span>
                    <span class="value">${repair.customerName}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">${t("repair.phoneModel")}:</span>
                    <span class="value">${repair.phoneModel}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">${t("repair.createdAt")}:</span>
                    <span class="value">${format(
                      new Date(repair.createdAt),
                      "dd/MM/yyyy - HH:mm"
                    )}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">${t("repair.downPayment")}:</span>
                    <span class="value">${new Intl.NumberFormat(i18n.language, {
                      style: "currency",
                      currency: "TND",
                    }).format(repair.downPayment)}</span>
                  </div>
                  <div class="info-row">
                    <span class="label">${t("common.balance")}:</span>
                    <span class="value">${new Intl.NumberFormat(i18n.language, {
                      style: "currency",
                      currency: "TND",
                    }).format(
                      Math.max(
                        0,
                        repair.repairPrice +
                          (repair.priceModifications?.reduce(
                            (sum, mod) => sum + mod.amount,
                            0
                          ) || 0) -
                          repair.downPayment
                      )
                    )}</span>
                  </div>
                </div>

                <div class="section">
                  <div class="problem-box">
                    <p style="font-weight: 900; text-align: center; text-decoration: underline; font-size: 9px; margin-bottom: 2px;">${t(
                      "repair.problemDescription"
                    )}:</p>
                    <p class="problem-text">${repair.problemDescription}</p>
                  </div>

                  ${
                    repair.observations &&
                    Array.isArray(repair.observations) &&
                    repair.observations.length > 0
                      ? `
                  <div style="margin-top: 3px; border-top: 1px dashed #000; padding-top: 2px;">
                    <p style="font-weight: 900; text-decoration: underline; font-size: 8px;">${t(
                      "repair.observations"
                    )}:</p>
                    ${repair.observations
                      .slice(-2) // Only show last 2 observations to save space
                      .map(
                        (obs) =>
                          `<div style="margin-top: 1px; padding-left: 2px;">
                            <p style="font-weight: 900; font-size: 8px;">- ${obs.text}</p>
                          </div>`
                      )
                      .join("")}
                  </div>
                  `
                      : ""
                  }
                </div>

                <div class="barcode">
                  <img src="${technicianBarcode}" alt="Barcode" />
                  <div style="font-size: 8px; font-weight: 900; margin-top: 2px;">${formattedTicketNumber}</div>
                </div>

                <div class="footer">
                  <p>FOR TECHNICIAN USE</p>
                </div>
              </div>
            </div>
            <script>
              document.addEventListener('DOMContentLoaded', function() {
                setTimeout(function() {
                  window.print();
                  window.onafterprint = function() {
                    window.close();
                  };
                }, 300);
              });

              window.onload = function() {
                window.print();
                window.onafterprint = function() {
                  window.close();
                };
              };
            </script>
          </body>
        </html>
      `);
      printWindow.document.close();
      console.log("Technician ticket document written and closed");
    } else {
      console.log("Failed to open print window for technician ticket");
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat(i18n.language, {
      style: "currency",
      currency: "TND",
    }).format(amount);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-3xl mx-auto">
          <div className="flex items-center justify-center p-12">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
          </div>
        </div>
      </div>
    );
  }

  if (!repair) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="max-w-3xl mx-auto">
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>{t("common.error")}</AlertTitle>
            <AlertDescription>{t("repair.notFound")}</AlertDescription>
          </Alert>
          <Button
            variant="outline"
            className="mt-4"
            onClick={() => navigate("/")}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("common.back")} {t("common.dashboard")}
          </Button>
        </div>
      </div>
    );
  }

  const statusColors = {
    pending: "bg-yellow-100 text-yellow-800 border-yellow-200",
    inProgress: "bg-blue-100 text-blue-800 border-blue-200",
    completed: "bg-green-100 text-green-800 border-green-200",
    cancelled: "bg-red-100 text-red-800 border-red-200",
    returned: "bg-purple-100 text-purple-800 border-purple-200",
  };

  const paymentStatusColors = {
    paid: "bg-green-100 text-green-800 border-green-200",
    partial: "bg-blue-100 text-blue-800 border-blue-200",
    unpaid: "bg-red-100 text-red-800 border-red-200",
  };

  return (
    <div className="container mx-auto px-4 py-6">
      <div className="max-w-3xl mx-auto">
        <div className="mb-6">
          <Button
            variant="ghost"
            onClick={() => navigate("/")}
            className="mb-4"
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            {t("common.back")} {t("common.dashboard")}
          </Button>

          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div>
              <h1 className="text-2xl font-bold">
                {t("repair.repairDetails")}
              </h1>
              {repair.ticketNumber && (
                <div className="text-sm font-medium text-gray-500 mt-1">
                  {t("repair.ticketNumber")}: #{repair.ticketNumber}
                </div>
              )}
            </div>

            <div className="flex gap-2 flex-wrap">
              <Button
                variant="outline"
                onClick={handlePrintQR}
                className="flex items-center gap-2"
              >
                <Printer className="h-4 w-4" />
                {t("common.print")} QR
              </Button>

              <Button
                variant="outline"
                onClick={handlePrintBarcode}
                className="flex items-center gap-2"
              >
                <Printer className="h-4 w-4" />
                {t("common.print")} {t("scanner.barcode")}
              </Button>

              <Button
                variant="outline"
                onClick={handlePrintBarcodeSticker}
                className="flex items-center gap-2"
              >
                <Printer className="h-4 w-4" />
                {t("repair.barcodeSticker")}
              </Button>

              <Button
                variant="outline"
                onClick={handleDownloadQR}
                className="flex items-center gap-2"
              >
                <Download className="h-4 w-4" />
                {t("common.download")} QR
              </Button>

              <Button
                variant="default"
                onClick={handleOpenPrintDialog}
                className="flex items-center gap-2"
              >
                <Ticket className="h-4 w-4" />
                {t("repair.printTicket")}
              </Button>

              <Button
                variant="outline"
                onClick={handlePrintTechnicianTicketOnly}
                className="flex items-center gap-2"
              >
                <Wrench className="h-4 w-4" />
                Technician Ticket
              </Button>

              <Dialog open={printDialogOpen} onOpenChange={setPrintDialogOpen}>
                <DialogContent className="sm:max-w-md">
                  <DialogHeader>
                    <DialogTitle>{t("repair.selectCodeType")}</DialogTitle>
                    <DialogDescription>
                      {t("repair.selectCodeTypeDescription")}
                    </DialogDescription>
                  </DialogHeader>
                  <div className="flex flex-col space-y-4 py-4">
                    <RadioGroup
                      value={codeType}
                      onValueChange={(value) =>
                        setCodeType(value as "qr" | "barcode")
                      }
                      className="flex flex-col space-y-2"
                    >
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="qr" id="qr" />
                        <Label htmlFor="qr" className="cursor-pointer">
                          {t("repair.qrCode")}
                        </Label>
                      </div>
                      <div className="flex items-center space-x-2">
                        <RadioGroupItem value="barcode" id="barcode" />
                        <Label htmlFor="barcode" className="cursor-pointer">
                          {t("scanner.barcode")}
                        </Label>
                      </div>
                    </RadioGroup>
                  </div>
                  <DialogFooter>
                    <Button
                      variant="outline"
                      onClick={() => setPrintDialogOpen(false)}
                    >
                      {t("common.cancel")}
                    </Button>
                    <Button
                      onClick={() => {
                        setPrintDialogOpen(false);
                        handlePrintTicket();
                      }}
                    >
                      {t("common.print")}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="md:col-span-2 space-y-6">
            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 pb-2 flex flex-row justify-between items-center">
                <CardTitle className="flex items-center">
                  <User className="h-5 w-5 mr-2 text-blue-500" />
                  {t("repair.customerInfo")}
                </CardTitle>
                <Badge className={statusColors[repair.status]}>
                  {t(`repair.status.${repair.status}`)}
                </Badge>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="space-y-4">
                  {/* Customer Name - Larger and more prominent */}
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-10 h-10 rounded-full bg-blue-100 flex items-center justify-center mr-3">
                      <User className="h-5 w-5 text-blue-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 font-medium">
                        {t("repair.customerName")}
                      </p>
                      <p className="text-lg font-semibold">
                        {repair.customerName}
                      </p>
                    </div>
                    <div className="ml-auto">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-blue-600"
                        onClick={() => {
                          navigator.clipboard.writeText(repair.customerName);
                          toast.success(t("common.copied"));
                        }}
                        title={t("common.copy")}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Phone Number with Call Button */}
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                      <Phone className="h-5 w-5 text-green-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 font-medium">
                        {t("repair.customerPhone")}
                      </p>
                      <p className="text-base font-medium">
                        {repair.customerPhone}
                      </p>
                    </div>
                    <div className="ml-auto flex">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-green-600"
                        onClick={() => {
                          navigator.clipboard.writeText(repair.customerPhone);
                          toast.success(t("common.copied"));
                        }}
                        title={t("common.copy")}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-green-600"
                        onClick={() =>
                          window.open(`tel:${repair.customerPhone}`)
                        }
                        title={t("common.call")}
                      >
                        <PhoneCall className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Device Model */}
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-10 h-10 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                      <Smartphone className="h-5 w-5 text-purple-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 font-medium">
                        {t("repair.phoneModel")}
                      </p>
                      <p className="text-base font-medium">
                        {repair.phoneModel}
                      </p>
                    </div>
                    <div className="ml-auto">
                      <Button
                        variant="ghost"
                        size="icon"
                        className="h-8 w-8 text-gray-500 hover:text-purple-600"
                        onClick={() => {
                          navigator.clipboard.writeText(repair.phoneModel);
                          toast.success(t("common.copied"));
                        }}
                        title={t("common.copy")}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Ticket Number */}
                  {repair.ticketNumber && (
                    <div className="flex items-center">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-amber-100 flex items-center justify-center mr-3">
                        <Ticket className="h-5 w-5 text-amber-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 font-medium">
                          {t("repair.ticketNumber")}
                        </p>
                        <p className="text-base font-medium">
                          #{repair.ticketNumber}
                        </p>
                      </div>
                      <div className="ml-auto">
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-8 w-8 text-gray-500 hover:text-amber-600"
                          onClick={() => {
                            navigator.clipboard.writeText(
                              repair.ticketNumber?.toString() || ""
                            );
                            toast.success(t("common.copied"));
                          }}
                          title={t("common.copy")}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  )}

                  {/* Creation Date */}
                  <div className="flex items-center">
                    <div className="flex-shrink-0 w-10 h-10 rounded-full bg-gray-100 flex items-center justify-center mr-3">
                      <Calendar className="h-5 w-5 text-gray-600" />
                    </div>
                    <div>
                      <p className="text-sm text-gray-500 font-medium">
                        {t("repair.createdAt")}
                      </p>
                      <p className="text-base font-medium">
                        {format(
                          new Date(repair.createdAt),
                          "dd/MM/yyyy - HH:mm"
                        )}
                      </p>
                    </div>
                  </div>

                  {/* Completion Date (if available) */}
                  {repair.completedAt && (
                    <div className="flex items-center">
                      <div className="flex-shrink-0 w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                        <CheckCircle2 className="h-5 w-5 text-green-600" />
                      </div>
                      <div>
                        <p className="text-sm text-gray-500 font-medium">
                          {t("repair.completedAt")}
                        </p>
                        <p className="text-base font-medium">
                          {format(
                            new Date(repair.completedAt),
                            "dd/MM/yyyy - HH:mm"
                          )}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-red-50 to-orange-50 pb-2">
                <CardTitle className="flex items-center">
                  <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
                  {t("repair.problemDescription")}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <div className="flex items-start p-4 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0 w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-3">
                    <FileText className="h-5 w-5 text-red-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-gray-700 whitespace-pre-wrap">
                      {repair.problemDescription}
                    </p>
                  </div>
                  <div className="ml-2">
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8 text-gray-500 hover:text-red-600"
                      onClick={() => {
                        navigator.clipboard.writeText(
                          repair.problemDescription
                        );
                        toast.success(t("common.copied"));
                      }}
                      title={t("common.copy")}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-blue-50 to-cyan-50 pb-2">
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2 text-blue-500" />
                  {t("repair.repairTimeline")}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4">
                <RepairTimeline repair={repair} />
              </CardContent>
            </Card>

            {/* Observations Card */}
            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-purple-50 to-indigo-50 pb-2">
                <CardTitle className="flex items-center">
                  <ClipboardList className="h-5 w-5 mr-2 text-purple-500" />
                  {t("repair.observations")}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4 space-y-4">
                {/* Add new observation */}
                <div className="flex gap-2 p-3 bg-purple-50 rounded-lg">
                  <Input
                    placeholder={t("repair.addObservation") + "..."}
                    value={newObservation}
                    onChange={(e) => setNewObservation(e.target.value)}
                    className="flex-1 border-purple-200 focus-visible:ring-purple-500"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    onClick={handleAddObservation}
                    className="border-purple-200 hover:bg-purple-100 hover:text-purple-700"
                  >
                    <Plus className="h-4 w-4" />
                  </Button>
                </div>

                {/* List of observations */}
                <div className="space-y-3">
                  {repair.observations &&
                  Array.isArray(repair.observations) &&
                  repair.observations.length > 0 ? (
                    repair.observations.map((observation) => (
                      <div
                        key={observation.id}
                        className="flex items-start p-4 bg-gray-50 rounded-lg border border-gray-100 hover:border-purple-200 transition-colors"
                      >
                        <div className="flex-shrink-0 w-8 h-8 rounded-full bg-purple-100 flex items-center justify-center mr-3">
                          <ClipboardList className="h-4 w-4 text-purple-600" />
                        </div>
                        <div className="flex-1">
                          <p className="text-sm font-medium">
                            {observation.text}
                          </p>
                          <p className="text-xs text-gray-500 mt-1 flex items-center">
                            <Calendar className="h-3 w-3 mr-1 inline" />
                            {format(
                              new Date(observation.createdAt),
                              "dd/MM/yyyy - HH:mm"
                            )}
                          </p>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="flex flex-col items-center justify-center py-8 px-4 bg-gray-50 rounded-lg">
                      <ClipboardList className="h-10 w-10 text-gray-300 mb-2" />
                      <p className="text-sm text-gray-500 text-center">
                        {t("repair.noObservations")}
                      </p>
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-green-50 to-emerald-50 pb-2">
                <CardTitle className="flex items-center">
                  <Receipt className="h-5 w-5 mr-2 text-green-600" />
                  {t("repair.paymentStatus")}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4 space-y-6">
                {/* Initial Price (Read-only) */}
                <div className="flex items-center p-3 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0 w-10 h-10 rounded-full bg-green-100 flex items-center justify-center mr-3">
                    <Receipt className="h-5 w-5 text-green-600" />
                  </div>
                  <div className="flex-1">
                    <p className="text-sm text-gray-500 font-medium">
                      {t("repair.initialPrice")}
                    </p>
                    <p className="text-lg font-semibold">
                      {formatCurrency(repair.repairPrice)}
                    </p>
                  </div>
                </div>

                {/* Price Modifications */}
                {repair.priceModifications &&
                  repair.priceModifications.length > 0 && (
                    <div className="space-y-3">
                      <h3 className="text-sm font-medium text-gray-500 flex items-center">
                        <AlertTriangle className="h-4 w-4 mr-1 text-amber-500" />
                        {t("repair.priceModifications")}
                      </h3>
                      <div className="space-y-2">
                        {repair.priceModifications.map((mod) => (
                          <div
                            key={mod.id}
                            className="flex items-start p-3 bg-gray-50 rounded-lg border border-gray-100"
                          >
                            <div className="flex-shrink-0 w-8 h-8 rounded-full bg-amber-100 flex items-center justify-center mr-3">
                              {mod.amount > 0 ? (
                                <ArrowLeft className="h-4 w-4 text-green-600 rotate-[135deg]" />
                              ) : (
                                <ArrowLeft className="h-4 w-4 text-red-600 rotate-[315deg]" />
                              )}
                            </div>
                            <div className="flex-1">
                              <div className="flex justify-between">
                                <span className="text-sm font-medium">
                                  {mod.reason}
                                </span>
                                <span
                                  className={`text-sm font-bold ${
                                    mod.amount > 0
                                      ? "text-green-600"
                                      : "text-red-600"
                                  }`}
                                >
                                  {mod.amount > 0 ? "+" : ""}
                                  {formatCurrency(mod.amount)}
                                </span>
                              </div>
                              <p className="text-xs text-gray-500 mt-1 flex items-center">
                                <Calendar className="h-3 w-3 mr-1 inline" />
                                {format(
                                  new Date(mod.createdAt),
                                  "dd/MM/yyyy - HH:mm"
                                )}
                              </p>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                {/* Add Price Modification */}
                <div className="space-y-3">
                  <h3 className="text-sm font-medium text-gray-500 flex items-center">
                    <Plus className="h-4 w-4 mr-1 text-blue-500" />
                    {t("repair.addPriceModification")}
                  </h3>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="flex flex-col gap-3">
                      <Input
                        type="number"
                        placeholder={t("repair.amount")}
                        value={
                          newPriceModification.amount === 0
                            ? ""
                            : newPriceModification.amount
                        }
                        onChange={(e) =>
                          setNewPriceModification({
                            ...newPriceModification,
                            amount: parseFloat(e.target.value) || 0,
                          })
                        }
                        className="flex-1 border-blue-200 focus-visible:ring-blue-500"
                      />
                      <div className="flex gap-2">
                        <Input
                          placeholder={t("repair.reason")}
                          value={newPriceModification.reason}
                          onChange={(e) =>
                            setNewPriceModification({
                              ...newPriceModification,
                              reason: e.target.value,
                            })
                          }
                          className="flex-1 border-blue-200 focus-visible:ring-blue-500"
                        />
                        <Button
                          type="button"
                          variant="outline"
                          size="icon"
                          onClick={handleAddPriceModification}
                          disabled={
                            !newPriceModification.reason.trim() ||
                            newPriceModification.amount === 0
                          }
                          className="border-blue-200 hover:bg-blue-100 hover:text-blue-700"
                        >
                          <Plus className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Summary Section */}
                <div className="p-4 bg-gray-50 rounded-lg space-y-3 border border-gray-200">
                  {/* Total Price (with modifications) */}
                  <div className="flex items-center justify-between">
                    <span className="text-sm font-medium text-gray-500">
                      {t("repair.totalPrice")}
                    </span>
                    <span className="text-lg font-bold">
                      {formatCurrency(
                        repair.repairPrice +
                          (repair.priceModifications?.reduce(
                            (sum, mod) => sum + mod.amount,
                            0
                          ) || 0)
                      )}
                    </span>
                  </div>

                  {/* Down Payment */}
                  <div className="flex items-center justify-between pt-2 border-t border-gray-200">
                    <span className="text-sm font-medium text-gray-500">
                      {t("repair.downPayment")}
                    </span>
                    <span className="text-base font-medium">
                      {formatCurrency(repair.downPayment)}
                    </span>
                  </div>

                  {/* Balance */}
                  <div className="flex items-center justify-between pt-2 border-t border-gray-200">
                    <span className="text-sm font-medium text-gray-500">
                      {t("common.balance")}
                    </span>
                    <span className="text-lg font-bold">
                      {formatCurrency(
                        Math.max(
                          0,
                          repair.repairPrice +
                            (repair.priceModifications?.reduce(
                              (sum, mod) => sum + mod.amount,
                              0
                            ) || 0) -
                            repair.downPayment
                        )
                      )}
                    </span>
                  </div>

                  {/* Payment Status */}
                  <div className="flex items-center justify-between pt-3 border-t border-gray-200">
                    <span className="text-sm font-medium text-gray-500">
                      {t("repair.paymentStatus")}
                    </span>
                    <Badge
                      className={`${
                        paymentStatusColors[repair.paymentStatus]
                      } px-3 py-1 text-sm`}
                    >
                      {t(`repair.payment.${repair.paymentStatus}`)}
                    </Badge>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="space-y-6">
            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-indigo-50 to-violet-50 pb-2">
                <CardTitle className="flex items-center">
                  <QrCode className="h-5 w-5 mr-2 text-indigo-500" />
                  {t("repair.scanQrCode")}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4 flex flex-col items-center">
                {qrCode ? (
                  <div className="p-4 bg-white rounded-lg border border-indigo-100 shadow-sm">
                    <img
                      src={qrCode}
                      alt="QR Code"
                      className="w-48 h-48 object-contain"
                    />
                  </div>
                ) : (
                  <div className="w-48 h-48 bg-gray-100 flex items-center justify-center rounded-lg border border-gray-200">
                    <div className="animate-pulse flex flex-col items-center">
                      <div className="h-10 w-10 bg-gray-200 rounded-full mb-2"></div>
                      <div className="h-4 w-24 bg-gray-200 rounded"></div>
                    </div>
                  </div>
                )}
                <div className="mt-4 flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700"
                    onClick={handlePrintQR}
                  >
                    <Printer className="h-4 w-4" />
                    {t("common.print")}
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="flex items-center gap-1 border-indigo-200 hover:bg-indigo-50 hover:text-indigo-700"
                    onClick={handleDownloadQR}
                  >
                    <Download className="h-4 w-4" />
                    {t("common.download")}
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="overflow-hidden">
              <CardHeader className="bg-gradient-to-r from-amber-50 to-yellow-50 pb-2">
                <CardTitle className="flex items-center">
                  <Wrench className="h-5 w-5 mr-2 text-amber-500" />
                  {t("repair.updateStatus")}
                </CardTitle>
              </CardHeader>
              <CardContent className="pt-4 space-y-4">
                {/* Repair Status */}
                <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                  <div className="text-sm font-medium text-gray-500 mb-2 flex items-center">
                    <Clock className="h-4 w-4 mr-1 text-amber-500" />
                    {t("repair.stat")}
                  </div>
                  <Select
                    value={repair.status}
                    onValueChange={handleStatusChange}
                  >
                    <SelectTrigger className="border-amber-200 focus:ring-amber-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-yellow-500 mr-2"></div>
                          {t("repair.status.pending")}
                        </div>
                      </SelectItem>
                      <SelectItem value="inProgress">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                          {t("repair.status.inProgress")}
                        </div>
                      </SelectItem>
                      <SelectItem value="completed">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                          {t("repair.status.completed")}
                        </div>
                      </SelectItem>
                      <SelectItem value="returned">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-purple-500 mr-2"></div>
                          {t("repair.status.returned")}
                        </div>
                      </SelectItem>
                      <SelectItem value="cancelled">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                          {t("repair.status.cancelled")}
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Payment Status */}
                <div className="p-3 bg-gray-50 rounded-lg border border-gray-100">
                  <div className="text-sm font-medium text-gray-500 mb-2 flex items-center">
                    <Receipt className="h-4 w-4 mr-1 text-green-500" />
                    {t("repair.paymentStatus")}
                  </div>
                  <Select
                    value={repair.paymentStatus}
                    onValueChange={handlePaymentStatusChange}
                  >
                    <SelectTrigger className="border-green-200 focus:ring-green-500">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="paid">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                          {t("repair.payment.paid")}
                        </div>
                      </SelectItem>
                      <SelectItem value="partial">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-blue-500 mr-2"></div>
                          {t("repair.payment.partial")}
                        </div>
                      </SelectItem>
                      <SelectItem value="unpaid">
                        <div className="flex items-center">
                          <div className="w-2 h-2 rounded-full bg-red-500 mr-2"></div>
                          {t("repair.payment.unpaid")}
                        </div>
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Print Ticket Button */}
                <Button
                  variant="default"
                  onClick={handleOpenPrintDialog}
                  className="w-full flex items-center justify-center gap-2 bg-gradient-to-r from-amber-500 to-yellow-500 hover:from-amber-600 hover:to-yellow-600"
                >
                  <Printer className="h-4 w-4" />
                  {t("repair.printTicket")}
                </Button>
              </CardContent>
              <CardFooter className="pt-0">
                <Dialog
                  open={deleteDialogOpen}
                  onOpenChange={setDeleteDialogOpen}
                >
                  <DialogTrigger asChild>
                    <Button
                      variant="outline"
                      className="w-full text-red-500 hover:bg-red-50 border-red-200 mt-2"
                    >
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      {t("repair.deleteRepair")}
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle className="flex items-center text-red-500">
                        <AlertTriangle className="h-5 w-5 mr-2" />
                        {t("common.areYouSure")}
                      </DialogTitle>
                      <DialogDescription>
                        {t("repair.deleteConfirmation")}
                      </DialogDescription>
                    </DialogHeader>
                    <div className="py-4">
                      <Label
                        htmlFor="delete-code"
                        className="mb-2 block font-medium"
                      >
                        {t("repair.enterDeleteCode")}:
                      </Label>
                      <Input
                        id="delete-code"
                        type="password"
                        value={deleteCode}
                        onChange={(e) => setDeleteCode(e.target.value)}
                        placeholder="******"
                        className="mb-2 border-red-200 focus-visible:ring-red-500"
                      />
                      {deleteError && (
                        <p className="text-sm text-red-500 mt-1 flex items-center">
                          <X className="h-4 w-4 mr-1" />
                          {deleteError}
                        </p>
                      )}
                    </div>
                    <DialogFooter>
                      <Button
                        variant="outline"
                        onClick={() => {
                          setDeleteDialogOpen(false);
                          setDeleteCode(""); // Reset code when canceling
                          setDeleteError(""); // Reset error when canceling
                        }}
                      >
                        {t("common.cancel")}
                      </Button>
                      <Button
                        variant="destructive"
                        onClick={handleDelete}
                        disabled={!deleteCode} // Disable if no code entered
                        className="bg-red-500 hover:bg-red-600"
                      >
                        {t("common.delete")}
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </CardFooter>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RepairDetail;
