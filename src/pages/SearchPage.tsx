import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useRepairContext } from "@/context/RepairContext";
import RepairCard from "@/components/RepairCard";
import UniversalScanner from "@/components/UniversalScanner";
import { SimplePagination } from "@/components/SimplePagination";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Search, SlidersHorizontal, X, Grid3X3, List } from "lucide-react";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { t } from "i18next";

const SearchPage: React.FC = () => {
  const {
    repairs,
    loading,
    currentPage,
    totalPages,
    getRepairByTicketNumber,
    goToPage,
  } = useRepairContext();
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string | null>(null);
  const [paymentFilter, setPaymentFilter] = useState<string | null>(null);
  const [dateFromFilter, setDateFromFilter] = useState<string>("");
  const [dateToFilter, setDateToFilter] = useState<string>("");
  const [viewMode, setViewMode] = useState<"grid" | "list">("list");

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    // Filtering is done below
  };

  const handleQRScan = async (data: string) => {
    // Check if the scanned data is a numeric ticket number (including EAN-13)
    const isNumeric = /^[0-9]+$/.test(data);

    if (isNumeric) {
      // If it's a numeric ticket number, query the database directly
      try {
        console.log("Scanned numeric data:", data);

        // The getRepairByTicketNumber function now handles EAN-13 format internally
        // Use cached data first for faster response
        const repair = await getRepairByTicketNumber(data, false);

        if (repair) {
          console.log("Found repair:", repair.id);
          navigate(`/repair/${repair.id}`);
        } else {
          // If no repair is found with that ticket number, show an error or alert
          console.error(`No repair found with ticket number: ${data}`);
          alert(t("repair.noRepairFoundWithTicket"));
        }
      } catch (error) {
        console.error("Error finding repair by ticket number:", error);
        alert(t("repair.scanError"));
      }
    } else {
      // If it's not a numeric ticket number, assume it's a UUID and navigate directly
      console.log("Scanned UUID:", data);
      navigate(`/repair/${data}`);
    }
  };

  const handleClearFilters = () => {
    setStatusFilter(null);
    setPaymentFilter(null);
    setDateFromFilter("");
    setDateToFilter("");
  };

  // Filter repairs based on search term and filters
  const filteredRepairs = repairs.filter((repair) => {
    // Text search
    if (searchTerm) {
      const searchLower = searchTerm.toLowerCase();
      const matchesSearch =
        repair.customerName.toLowerCase().includes(searchLower) ||
        repair.customerPhone.includes(searchLower) ||
        repair.phoneModel.toLowerCase().includes(searchLower);

      if (!matchesSearch) return false;
    }

    // Status filter
    if (statusFilter && repair.status !== statusFilter) {
      return false;
    }

    // Payment filter
    if (paymentFilter && repair.paymentStatus !== paymentFilter) {
      return false;
    }

    // Date filters
    if (dateFromFilter) {
      const repairDate = new Date(repair.createdAt);
      const fromDate = new Date(dateFromFilter);
      if (repairDate < fromDate) return false;
    }

    if (dateToFilter) {
      const repairDate = new Date(repair.createdAt);
      const toDate = new Date(dateToFilter + "T23:59:59");
      if (repairDate > toDate) return false;
    }

    return true;
  });

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center p-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  // Return the component JSX as it was before
  return (
    <div className="container mx-auto px-4 py-6">
      <h1 className="text-2xl font-bold mb-6">{t("searchRepairs.title")}</h1>

      <div className="flex flex-col md:flex-row gap-4 mb-8">
        <form onSubmit={handleSearch} className="flex-1 relative">
          <div className="relative">
            <Input
              placeholder="Search by customer, phone number or model..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pr-10 w-full" /* Increased right padding for icon */
            />
            <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
              <Search className="h-4 w-4 text-gray-400" />
            </div>
          </div>
        </form>

        <div className="flex gap-2">
          <div className="flex border rounded-md">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("grid")}
              className="rounded-r-none"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("list")}
              className="rounded-l-none"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>

          <UniversalScanner onScan={handleQRScan} />

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" className="flex items-center gap-2">
                <SlidersHorizontal className="h-4 w-4" />
                <span>{t("filters.title")}</span>
                {(statusFilter ||
                  paymentFilter ||
                  dateFromFilter ||
                  dateToFilter) && (
                  <span className="bg-primary rounded-full w-5 h-5 text-xs flex items-center justify-center text-primary-foreground">
                    {(statusFilter ? 1 : 0) +
                      (paymentFilter ? 1 : 0) +
                      (dateFromFilter ? 1 : 0) +
                      (dateToFilter ? 1 : 0)}
                  </span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-80">
              <div className="space-y-4">
                <h3 className="font-medium">{t("filters.slogan")}</h3>

                <div className="space-y-2">
                  <Label htmlFor="status-filter">{t("filters.status")}</Label>
                  <Select
                    value={statusFilter || ""}
                    onValueChange={(val) => setStatusFilter(val || null)}
                  >
                    <SelectTrigger id="status-filter">
                      <SelectValue
                        placeholder={t("filters.anyStatus") || "Any status"}
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="pending">
                        {t("repair.status.pending")}
                      </SelectItem>
                      <SelectItem value="inProgress">
                        {t("repair.status.inProgress")}
                      </SelectItem>
                      <SelectItem value="completed">
                        {t("repair.status.completed")}
                      </SelectItem>
                      <SelectItem value="returned">
                        {t("repair.status.returned")}
                      </SelectItem>
                      <SelectItem value="cancelled">
                        {t("repair.status.cancelled")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="payment-filter">{t("filters.payment")}</Label>
                  <Select
                    value={paymentFilter || ""}
                    onValueChange={(val) => setPaymentFilter(val || null)}
                  >
                    <SelectTrigger id="payment-filter">
                      <SelectValue
                        placeholder={
                          t("filters.anyPaymentStatus") || "Any payment status"
                        }
                      />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="paid">
                        {t("repair.payment.paid")}
                      </SelectItem>
                      <SelectItem value="partial">
                        {t("repair.payment.partial")}
                      </SelectItem>
                      <SelectItem value="unpaid">
                        {t("repair.payment.unpaid")}
                      </SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid grid-cols-2 gap-2">
                  <div className="space-y-2">
                    <Label htmlFor="date-from">{t("filters.dateFrom")}</Label>
                    <Input
                      id="date-from"
                      type="date"
                      value={dateFromFilter}
                      onChange={(e) => setDateFromFilter(e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="date-to">{t("filters.dateTo")}</Label>
                    <Input
                      id="date-to"
                      type="date"
                      value={dateToFilter}
                      onChange={(e) => setDateToFilter(e.target.value)}
                    />
                  </div>
                </div>

                {(statusFilter ||
                  paymentFilter ||
                  dateFromFilter ||
                  dateToFilter) && (
                  <Button
                    variant="ghost"
                    className="w-full flex items-center justify-center gap-1 mt-2"
                    onClick={handleClearFilters}
                  >
                    <X className="h-3 w-3" />
                    {t("filters.clear") || "Clear filters"}
                  </Button>
                )}
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div>
        {filteredRepairs.length > 0 ? (
          viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredRepairs.map((repair) => (
                <RepairCard key={repair.id} repair={repair} />
              ))}
            </div>
          ) : (
            <div className="bg-white dark:bg-gray-800 rounded-lg border dark:border-gray-700 overflow-hidden">
              <table className="w-full">
                <thead className="bg-gray-50 dark:bg-gray-700">
                  <tr>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">
                      {t("repair.ticketNumber")}
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">
                      {t("repair.customerName")}
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">
                      {t("repair.phoneModel")}
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">
                      {t("repair.stat")}
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">
                      {t("repair.paymentStatus")}
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">
                      {t("repair.repairPrice")}
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">
                      {t("repair.createdAt")}
                    </th>
                    <th className="px-4 py-3 text-left text-sm font-medium text-gray-900 dark:text-gray-100">
                      {t("common.actions")}
                    </th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200 dark:divide-gray-600">
                  {filteredRepairs.map((repair) => {
                    // Check if repair is overdue (more than 7 days for pending, 14 days for in progress)
                    const daysSinceCreated = Math.floor(
                      (new Date().getTime() -
                        new Date(repair.createdAt).getTime()) /
                        (1000 * 60 * 60 * 24)
                    );
                    const isOverdue =
                      (repair.status === "pending" && daysSinceCreated > 7) ||
                      (repair.status === "inProgress" && daysSinceCreated > 14);

                    const statusColors = {
                      pending: isOverdue
                        ? "bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200 border-l-4 border-orange-500 animate-pulse"
                        : "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200 border-l-4 border-yellow-500",
                      inProgress: isOverdue
                        ? "bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200 border-l-4 border-orange-500 animate-pulse"
                        : "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-200 border-l-4 border-blue-500",
                      completed:
                        "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 border-l-4 border-green-500",
                      cancelled:
                        "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200 border-l-4 border-red-500",
                      returned:
                        "bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-200 border-l-4 border-purple-500",
                    };

                    const paymentColors = {
                      paid: "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200",
                      partial:
                        "bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-200",
                      unpaid:
                        "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-200",
                    };

                    return (
                      <tr
                        key={repair.id}
                        className={`hover:bg-gray-50 dark:hover:bg-gray-700/50 cursor-pointer ${
                          statusColors[repair.status]
                        }`}
                        onClick={() => navigate(`/repair/${repair.id}`)}
                      >
                        <td className="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                          #{repair.ticketNumber}
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">
                          <div>
                            <div className="font-medium">
                              {repair.customerName}
                            </div>
                            <div className="text-gray-500 dark:text-gray-300">
                              {repair.customerPhone}
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-900 dark:text-white">
                          {repair.phoneModel}
                        </td>
                        <td className="px-4 py-3">
                          <div className="flex items-center gap-2">
                            <span
                              className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                                statusColors[repair.status]
                              }`}
                            >
                              {t(`repair.status.${repair.status}`)}
                            </span>
                            {isOverdue && (
                              <span className="inline-flex items-center px-1.5 py-0.5 text-xs font-medium bg-orange-100 dark:bg-orange-900/30 text-orange-800 dark:text-orange-200 rounded-full animate-pulse">
                                ⚠️ {daysSinceCreated}d
                              </span>
                            )}
                          </div>
                        </td>
                        <td className="px-4 py-3">
                          <span
                            className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                              paymentColors[repair.paymentStatus]
                            }`}
                          >
                            {t(`repair.payment.${repair.paymentStatus}`)}
                          </span>
                        </td>
                        <td className="px-4 py-3 text-sm font-medium text-gray-900 dark:text-white">
                          {repair.repairPrice.toFixed(3)} TND
                        </td>
                        <td className="px-4 py-3 text-sm text-gray-500 dark:text-gray-300">
                          {new Date(repair.createdAt).toLocaleDateString(
                            "en-GB"
                          )}
                        </td>
                        <td className="px-4 py-3">
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(`/repair/${repair.id}`);
                            }}
                          >
                            {t("common.viewDetails")}
                          </Button>
                        </td>
                      </tr>
                    );
                  })}
                </tbody>
              </table>
            </div>
          )
        ) : (
          <div className="text-center py-12 bg-gray-50 rounded-lg">
            <h3 className="text-lg font-medium text-gray-900 mb-1">
              {t("searchRepairs.noResults") || "No repairs found"}
            </h3>
            <p className="text-gray-500 mb-4">
              {t("searchRepairs.adjustFilters") ||
                "Try adjusting your search or filters"}
            </p>
            {(searchTerm ||
              statusFilter ||
              paymentFilter ||
              dateFromFilter ||
              dateToFilter) && (
              <Button variant="outline" onClick={handleClearFilters}>
                {t("filters.clearAll") || "Clear all filters"}
              </Button>
            )}
          </div>
        )}
      </div>

      {/* Simple Pagination */}
      <SimplePagination
        currentPage={currentPage}
        totalPages={totalPages}
        onPageChange={goToPage}
        loading={loading}
      />
    </div>
  );
};

export default SearchPage;
