import React, { useState, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useRepairContext } from "@/context/RepairContext";
import { RepairItem } from "@/types";
import DateRangePicker from "@/components/lottery/DateRangePicker";
import LotteryWheel from "@/components/lottery/LotteryWheel";
import { Card } from "@/components/ui/card";
import { toast } from "sonner";
import { Trophy, Sparkles } from "lucide-react";

const LotteryPage: React.FC = () => {
  const { t, i18n } = useTranslation();
  const { repairs, loading } = useRepairContext();
  const isRTL = i18n.dir() === "rtl";

  const [startDate, setStartDate] = useState("");
  const [endDate, setEndDate] = useState("");
  const [filteredRepairs, setFilteredRepairs] = useState<RepairItem[]>([]);
  const [showWheel, setShowWheel] = useState(false);
  const [winner, setWinner] = useState<RepairItem | null>(null);

  // Set default date range to current month
  useEffect(() => {
    const now = new Date();
    const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
    const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);

    setStartDate(firstDay.toISOString().split("T")[0]);
    setEndDate(lastDay.toISOString().split("T")[0]);
  }, []);

  // Filter repairs based on date range and completed status
  useEffect(() => {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      end.setHours(23, 59, 59, 999); // Include the entire end date

      const filtered = repairs.filter((repair) => {
        const repairDate = new Date(repair.createdAt);
        return (
          repairDate >= start &&
          repairDate <= end &&
          repair.ticketNumber &&
          repair.status === "completed"
        );
      });

      setFilteredRepairs(filtered);
    } else {
      setFilteredRepairs([]);
    }
  }, [startDate, endDate, repairs]);

  const handleRunLottery = () => {
    if (!startDate || !endDate) {
      toast.error(t("lottery.selectDatesFirst"));
      return;
    }

    if (new Date(startDate) > new Date(endDate)) {
      toast.error(t("lottery.invalidDateRange"));
      return;
    }

    if (filteredRepairs.length === 0) {
      toast.error(t("lottery.noRepairsInRange"));
      return;
    }

    setShowWheel(true);
    setWinner(null);
  };

  const handleWinnerSelected = (selectedWinner: RepairItem) => {
    setWinner(selectedWinner);
    toast.success(
      `${t("lottery.congratulations")} ${selectedWinner.customerName}! ${t(
        "lottery.ticketNumber"
      )}${selectedWinner.ticketNumber}`
    );
  };

  const handleReset = () => {
    setShowWheel(false);
    setWinner(null);
  };

  if (loading) {
    return (
      <div className="container mx-auto px-4 py-6">
        <div className="flex items-center justify-center p-12">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
        </div>
      </div>
    );
  }

  return (
    <div
      className="container mx-auto px-4 py-6"
      style={{ direction: isRTL ? "rtl" : "ltr" }}
    >
      {/* Header */}
      <div className="text-center mb-8">
        <div className="flex items-center justify-center mb-4">
          <Sparkles className="h-8 w-8 mr-3 text-yellow-500" />
          <h1 className="text-3xl font-bold text-gray-800 dark:text-gray-100">
            {t("lottery.title")}
          </h1>
          <Trophy className="h-8 w-8 ml-3 text-yellow-500" />
        </div>
        <p className="text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
          {t("lottery.selectDateRange")} - {t("lottery.totalEntries")}:{" "}
          {filteredRepairs.length}
        </p>
        <p className="text-sm text-blue-600 dark:text-blue-400 max-w-2xl mx-auto mt-2">
          {t("lottery.eligibilityNote")}
        </p>
      </div>

      <div className="max-w-4xl mx-auto">
        {!showWheel ? (
          /* Date Range Selection */
          <div className="space-y-6">
            <DateRangePicker
              startDate={startDate}
              endDate={endDate}
              onStartDateChange={setStartDate}
              onEndDateChange={setEndDate}
              onRunLottery={handleRunLottery}
              isLoading={loading}
              totalRepairs={filteredRepairs.length}
            />

            {/* Preview of repairs */}
            {filteredRepairs.length > 0 && (
              <Card className="p-6">
                <h3 className="text-lg font-semibold mb-4">
                  {t("lottery.entries")} ({filteredRepairs.length})
                </h3>
                <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-2 max-h-40 overflow-y-auto">
                  {filteredRepairs.map((repair) => (
                    <div
                      key={repair.id}
                      className="bg-green-50 dark:bg-green-900/20 p-2 rounded text-center text-sm border border-green-200 dark:border-green-800"
                    >
                      <div className="font-semibold text-green-700 dark:text-green-300">
                        #{repair.ticketNumber}
                      </div>
                      <div className="text-xs text-gray-600 dark:text-gray-400 truncate">
                        {repair.customerName}
                      </div>
                      <div className="text-xs text-green-600 dark:text-green-400 mt-1">
                        ✓ {t("repair.completed")}
                      </div>
                    </div>
                  ))}
                </div>
              </Card>
            )}
          </div>
        ) : (
          /* Lottery Wheel */
          <div className="text-center">
            <LotteryWheel
              repairs={filteredRepairs}
              onWinnerSelected={handleWinnerSelected}
              onReset={handleReset}
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default LotteryPage;
