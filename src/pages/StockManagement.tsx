import React, { useState } from "react";
import { useTranslation } from "react-i18next";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Package,
  ShoppingCart,
  BarChart3,
  Grid3X3,
  List,
  Plus,
  Search,
  Monitor,
  Smartphone,
} from "lucide-react";
import POSInterface from "@/components/stock/POSInterface";
import ProductManagement from "@/components/stock/ProductManagement";
import CategoryManagement from "@/components/stock/CategoryManagement";
import SalesHistory from "@/components/stock/SalesHistory";
import InventoryOverview from "@/components/stock/InventoryOverview";
import { useStockInitialization } from "@/hooks/useStockInitialization";

const StockManagement: React.FC = () => {
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";
  const [activeTab, setActiveTab] = useState("pos");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [touchMode, setTouchMode] = useState(false);

  // Initialize stock data when this page loads
  useStockInitialization();

  // Detect if device is likely a touch device
  React.useEffect(() => {
    const isTouchDevice =
      "ontouchstart" in window || navigator.maxTouchPoints > 0;
    setTouchMode(isTouchDevice);
  }, []);

  const tabs = [
    {
      id: "pos",
      label: t("stock.pos"),
      icon: ShoppingCart,
      component: POSInterface,
    },
    {
      id: "products",
      label: t("stock.products"),
      icon: Package,
      component: ProductManagement,
    },
    {
      id: "categories",
      label: t("stock.categories"),
      icon: Grid3X3,
      component: CategoryManagement,
    },
    {
      id: "inventory",
      label: t("stock.inventory"),
      icon: BarChart3,
      component: InventoryOverview,
    },
    {
      id: "sales",
      label: t("stock.sales"),
      icon: List,
      component: SalesHistory,
    },
  ];

  return (
    <div
      className="container mx-auto px-4 py-6 max-w-7xl"
      style={{ direction: isRTL ? "rtl" : "ltr" }}
    >
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
            <Package className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100">
              {t("stock.title")}
            </h1>
            <p className="text-sm text-gray-600 dark:text-gray-400">
              Manage inventory, process sales, and track performance
            </p>
          </div>
        </div>

        {/* View Controls */}
        <div className="flex items-center gap-2">
          {/* Touch Mode Indicator */}
          <div className="flex items-center gap-2 px-3 py-1 bg-gray-100 dark:bg-gray-800 rounded-lg">
            {touchMode ? (
              <Smartphone className="h-4 w-4 text-green-600" />
            ) : (
              <Monitor className="h-4 w-4 text-blue-600" />
            )}
            <span className="text-xs font-medium">
              {touchMode ? t("stock.touchMode") : "Desktop"}
            </span>
          </div>

          {/* View Mode Toggle */}
          <div className="flex bg-gray-100 dark:bg-gray-800 rounded-lg p-1">
            <Button
              variant={viewMode === "grid" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("grid")}
              className="h-8 w-8 p-0"
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "ghost"}
              size="sm"
              onClick={() => setViewMode("list")}
              className="h-8 w-8 p-0"
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        {/* Tab Navigation */}
        <TabsList
          className={`grid w-full ${touchMode ? "h-16" : "h-12"} mb-6`}
          style={{ gridTemplateColumns: `repeat(${tabs.length}, 1fr)` }}
        >
          {tabs.map((tab) => {
            const IconComponent = tab.icon;
            return (
              <TabsTrigger
                key={tab.id}
                value={tab.id}
                className={`flex flex-col gap-1 ${
                  touchMode ? "py-3 px-4" : "py-2 px-3"
                }`}
              >
                <IconComponent
                  className={`${touchMode ? "h-5 w-5" : "h-4 w-4"}`}
                />
                <span
                  className={`${touchMode ? "text-xs" : "text-xs"} font-medium`}
                >
                  {tab.label}
                </span>
              </TabsTrigger>
            );
          })}
        </TabsList>

        {/* Tab Content */}
        {tabs.map((tab) => {
          const ComponentToRender = tab.component;
          return (
            <TabsContent key={tab.id} value={tab.id} className="mt-0">
              <ComponentToRender
                viewMode={viewMode}
                touchMode={touchMode}
                onViewModeChange={setViewMode}
              />
            </TabsContent>
          );
        })}
      </Tabs>
    </div>
  );
};

export default StockManagement;
