import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useTranslation } from "react-i18next";
import RepairForm from "@/components/RepairForm";
import { useRepairContext } from "@/context/RepairContext";
import { RepairFormData } from "@/types";
import { toast } from "sonner";

const NewRepair: React.FC = () => {
  const { addRepair } = useRepairContext();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const { t, i18n } = useTranslation();
  const isRTL = i18n.dir() === "rtl";

  const handleSubmit = async (data: RepairFormData) => {
    setIsLoading(true);
    try {
      const newRepair = await addRepair(data);
      toast.success(t("repair.successCreated"));
      navigate(`/repair/${newRepair.id}`);
    } catch (error) {
      console.error("Error creating repair:", error);
      toast.error(t("repair.failedCreated"));
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <div
        className="max-w-3xl mx-auto"
        style={{ direction: isRTL ? "rtl" : "ltr" }}
      >
        <div className="flex items-center mb-8">
          <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-100">
            {t("repair.createNew")}
          </h1>
        </div>

        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md border border-gray-200 dark:border-gray-700 p-6">
          <RepairForm
            onSubmit={handleSubmit}
            isLoading={isLoading}
            isRTL={isRTL}
          />
        </div>
      </div>
    </div>
  );
};

export default NewRepair;
