import React, { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

const rolesList = ["administrator", "technician", "receptionist", "cashier"];

interface Shop {
  id: string;
  name: string;
}

interface UserWithRole {
  id: string;
  user_id: string;
  role: string;
  repair_shop_id: string;
  users: {
    email: string;
  };
  user_emails?: {
    email: string;
  };
}

export default function UserManagement() {
  const { user, loading, hasPermission, logUserAction } = useAuth();
  const { t } = useTranslation();
  const [users, setUsers] = useState<UserWithRole[]>([]);
  const [shops, setShops] = useState<Shop[]>([]);
  const [selectedShop, setSelectedShop] = useState<string | null>(null);
  const [refresh, setRefresh] = useState(false);
  const [inviteDialogOpen, setInviteDialogOpen] = useState(false);
  const [newUserEmail, setNewUserEmail] = useState("");
  const [newUserPassword, setNewUserPassword] = useState("");
  const [newUserFullName, setNewUserFullName] = useState("");
  const [newUserRole, setNewUserRole] = useState("technician");
  const [actionLogs, setActionLogs] = useState<any[]>([]);
  const [activeTab, setActiveTab] = useState("users");

  // Only allow administrators
  const isAdmin = hasPermission("manage_users");

  useEffect(() => {
    if (!isAdmin) return;

    // Get all shops the user has access to
    const fetchShops = async () => {
      const { data, error } = await supabase
        .from("repair_shops")
        .select("id, name")
        .order("name");

      if (!error && data) {
        setShops(data);
        if (!selectedShop && data.length > 0) {
          setSelectedShop(data[0].id);
        }
      }
    };

    fetchShops();
  }, [isAdmin]);

  useEffect(() => {
    if (!isAdmin || !selectedShop) return;

    // Get all users and their roles for the current shop
    const fetchUsers = async () => {
      console.log("Fetching users for shop:", selectedShop);
      try {
        const { data, error } = await supabase.rpc("get_shop_users", {
          shop_id: selectedShop,
        });

        console.log("RPC response:", { data, error });

        if (error) {
          console.error("Error fetching users:", error);
          toast.error("Failed to fetch users: " + error.message);
          return;
        }

        // Transform data to match expected format
        const transformedUsers =
          data?.map((user) => ({
            id: user.id,
            user_id: user.user_id,
            role: user.role,
            repair_shop_id: user.repair_shop_id,
            full_name: user.full_name,
            users: { email: user.email },
          })) || [];

        console.log("Transformed users:", transformedUsers);
        setUsers(transformedUsers as UserWithRole[]);
      } catch (error) {
        console.error("Error in fetchUsers:", error);
        toast.error("Failed to fetch users");
      }
    };

    fetchUsers();
  }, [isAdmin, selectedShop, refresh]);

  useEffect(() => {
    if (!isAdmin || activeTab !== "activity" || !selectedShop) return;

    // Fetch action logs
    const fetchActionLogs = async () => {
      const { data, error } = await supabase
        .from("user_action_logs")
        .select(
          `
          id, 
          action, 
          details, 
          created_at, 
          users: user_id(email)
        `
        )
        .order("created_at", { ascending: false })
        .limit(100);

      if (!error && data) {
        setActionLogs(data);
      }
    };

    fetchActionLogs();
  }, [isAdmin, activeTab, selectedShop, refresh]);

  const handleRoleChange = async (userId: string, newRole: string) => {
    try {
      console.log("Updating role for user ID:", userId, "to role:", newRole);

      const { data, error } = await supabase.rpc("update_user_role_in_shop", {
        shop_id: selectedShop,
        target_user_id: userId,
        new_role: newRole,
      });

      if (error) {
        console.error("Role update error:", error);
        throw error;
      }

      if (data && data.success) {
        await logUserAction("change_user_role", {
          user_id: userId,
          new_role: newRole,
        });

        toast.success(data.message || "User role updated successfully");
        setRefresh((r) => !r);
      } else {
        throw new Error(data?.error || "Failed to update role");
      }
    } catch (error: any) {
      toast.error(
        "Failed to update user role: " + (error.message || "Unknown error")
      );
      console.error(error);
    }
  };

  const handleCreateUser = async () => {
    if (
      !newUserEmail ||
      !newUserPassword ||
      !newUserFullName ||
      !selectedShop
    ) {
      toast.error("Please fill in all required fields");
      return;
    }

    if (newUserPassword.length < 6) {
      toast.error("Password must be at least 6 characters");
      return;
    }

    try {
      // Call the server function to create user and assign role
      const { data, error } = await supabase.rpc("create_user_with_role", {
        shop_id: selectedShop,
        user_email: newUserEmail,
        user_full_name: newUserFullName,
        user_password: newUserPassword,
        user_role: newUserRole,
      });

      if (error) throw error;

      if (data.success) {
        await logUserAction("create_user", {
          email: newUserEmail,
          role: newUserRole,
          shop_id: selectedShop,
          user_id: data.user_id,
        });

        toast.success("User created successfully. They can login immediately.");
        setNewUserEmail("");
        setNewUserPassword("");
        setNewUserFullName("");
        setInviteDialogOpen(false);
        setRefresh((r) => !r);
      } else {
        throw new Error(data.error || "Failed to create user");
      }
    } catch (error: any) {
      toast.error(error.message || "Failed to create user");
      console.error(error);
    }
  };

  const handleRemoveUser = async (userId: string, email: string) => {
    if (!confirm(`Are you sure you want to remove ${email} from this shop?`)) {
      return;
    }

    try {
      const { data, error } = await supabase.rpc("remove_user_from_shop", {
        shop_id: selectedShop,
        target_user_id: userId,
      });

      if (error) throw error;

      if (data && !data.success) {
        throw new Error(data.error || "Failed to remove user");
      }

      await logUserAction("remove_user", { user_id: userId, email: email });

      toast.success(data?.message || "User removed successfully");
      setRefresh((r) => !r);
    } catch (error: any) {
      toast.error(error.message || "Failed to remove user");
      console.error(error);
    }
  };

  if (loading)
    return (
      <div className="flex items-center justify-center h-screen">
        Loading...
      </div>
    );
  if (!isAdmin)
    return (
      <div className="flex items-center justify-center h-screen">
        Access denied. You need administrator privileges.
      </div>
    );

  return (
    <div className="container mx-auto py-6">
      <h1 className="text-3xl font-bold mb-6">{t("userManagement.title")}</h1>

      <div className="flex items-center space-x-4 mb-6">
        <div className="flex-1">
          <Label htmlFor="shop-select">Select Shop</Label>
          <Select
            value={selectedShop || ""}
            onValueChange={(value) => setSelectedShop(value)}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select a shop" />
            </SelectTrigger>
            <SelectContent>
              {shops.map((shop) => (
                <SelectItem key={shop.id} value={shop.id}>
                  {shop.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="pt-6">
          <Dialog open={inviteDialogOpen} onOpenChange={setInviteDialogOpen}>
            <DialogTrigger asChild>
              <Button>{t("userManagement.createUser")}</Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create New User</DialogTitle>
                <DialogDescription>
                  Create a new user account and assign them to your repair shop.
                </DialogDescription>
              </DialogHeader>

              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="fullName" className="text-right">
                    {t("userManagement.fullName")} *
                  </Label>
                  <Input
                    id="fullName"
                    type="text"
                    value={newUserFullName}
                    onChange={(e) => setNewUserFullName(e.target.value)}
                    className="col-span-3"
                    placeholder="John Doe"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="email" className="text-right">
                    {t("userManagement.email")} *
                  </Label>
                  <Input
                    id="email"
                    type="email"
                    value={newUserEmail}
                    onChange={(e) => setNewUserEmail(e.target.value)}
                    className="col-span-3"
                    placeholder="<EMAIL>"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="password" className="text-right">
                    Password *
                  </Label>
                  <Input
                    id="password"
                    type="password"
                    value={newUserPassword}
                    onChange={(e) => setNewUserPassword(e.target.value)}
                    className="col-span-3"
                    placeholder="Minimum 6 characters"
                  />
                </div>
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="role" className="text-right">
                    Role *
                  </Label>
                  <Select value={newUserRole} onValueChange={setNewUserRole}>
                    <SelectTrigger className="col-span-3">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {rolesList.map((role) => (
                        <SelectItem key={role} value={role}>
                          {role.charAt(0).toUpperCase() + role.slice(1)}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              </div>

              <DialogFooter>
                <Button
                  variant="outline"
                  onClick={() => setInviteDialogOpen(false)}
                >
                  Cancel
                </Button>
                <Button onClick={handleCreateUser}>Create User</Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="mb-4">
          <TabsTrigger value="users">Users</TabsTrigger>
          <TabsTrigger value="activity">Activity Log</TabsTrigger>
        </TabsList>

        <TabsContent value="users">
          <Card>
            <CardHeader>
              <CardTitle>{t("userManagement.title")}</CardTitle>
              <CardDescription>{t("userManagement.title")}</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="p-2 text-left font-medium">
                        {t("userManagement.fullName")}
                      </th>
                      <th className="p-2 text-left font-medium">
                        {t("userManagement.email")}
                      </th>
                      <th className="p-2 text-left font-medium">
                        {t("userManagement.role")}
                      </th>
                      <th className="p-2 text-left font-medium">
                        {t("common.actions")}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {users.length === 0 ? (
                      <tr>
                        <td
                          colSpan={4}
                          className="p-4 text-center text-muted-foreground"
                        >
                          {t("userManagement.noUsers")}
                        </td>
                      </tr>
                    ) : (
                      users.map((u) => (
                        <tr key={u.id} className="border-b">
                          <td className="p-2">{u.full_name || "No name"}</td>
                          <td className="p-2">{u.email || "No email"}</td>
                          <td className="p-2">
                            <Select
                              value={u.role}
                              onValueChange={(value) =>
                                handleRoleChange(u.user_id, value)
                              }
                            >
                              <SelectTrigger className="w-[180px]">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {rolesList.map((role) => (
                                  <SelectItem key={role} value={role}>
                                    {role.charAt(0).toUpperCase() +
                                      role.slice(1)}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                          </td>
                          <td className="p-2">
                            <Button
                              variant="destructive"
                              size="sm"
                              onClick={() =>
                                handleRemoveUser(
                                  u.user_id,
                                  u.email || "Unknown"
                                )
                              }
                            >
                              {t("userManagement.removeUser")}
                            </Button>
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="activity">
          <Card>
            <CardHeader>
              <CardTitle>{t("userManagement.activityLog")}</CardTitle>
              <CardDescription>
                {t("userManagement.activityLog")}
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="rounded-md border">
                <table className="w-full">
                  <thead>
                    <tr className="border-b bg-muted/50">
                      <th className="p-2 text-left font-medium">
                        {t("common.user")}
                      </th>
                      <th className="p-2 text-left font-medium">
                        {t("common.actions")}
                      </th>
                      <th className="p-2 text-left font-medium">
                        {t("common.details")}
                      </th>
                      <th className="p-2 text-left font-medium">
                        {t("advancedStock.dateTime")}
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {actionLogs.length === 0 ? (
                      <tr>
                        <td
                          colSpan={4}
                          className="p-4 text-center text-muted-foreground"
                        >
                          {t("userManagement.noActivity")}
                        </td>
                      </tr>
                    ) : (
                      actionLogs.map((log) => (
                        <tr key={log.id} className="border-b">
                          <td className="p-2">
                            {log.users?.email || "Unknown"}
                          </td>
                          <td className="p-2">{log.action}</td>
                          <td className="p-2">
                            {log.details ? (
                              <pre className="text-xs overflow-auto max-w-xs">
                                {JSON.stringify(log.details, null, 2)}
                              </pre>
                            ) : (
                              "N/A"
                            )}
                          </td>
                          <td className="p-2">
                            {new Date(log.created_at).toLocaleString()}
                          </td>
                        </tr>
                      ))
                    )}
                  </tbody>
                </table>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
