import React, { useEffect, useState } from "react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";

const rolesList = ["administrator", "technician", "receptionist", "cashier"];

export default function UserManagement() {
  const { user, roles, loading } = useAuth();
  const [users, setUsers] = useState<any[]>([]);
  const [selectedShop, setSelectedShop] = useState<string | null>(null);
  const [refresh, setRefresh] = useState(false);

  // Only allow administrators
  const isAdmin = roles.some((r) => r.role === "administrator");

  useEffect(() => {
    if (!isAdmin) return;
    // Get all users and their roles for the current shop(s)
    const fetchUsers = async () => {
      let shopId = selectedShop || (roles[0] && roles[0].repair_shop_id);
      if (!shopId) return;
      const { data, error } = await supabase
        .from("user_repair_shops")
        .select("id, user_id, role, repair_shop_id, users: user_id(email)")
        .eq("repair_shop_id", shopId);
      if (!error) setUsers(data || []);
    };
    fetchUsers();
  }, [isAdmin, selectedShop, refresh, roles]);

  const handleRoleChange = async (id: string, newRole: string) => {
    await supabase
      .from("user_repair_shops")
      .update({ role: newRole })
      .eq("id", id);
    setRefresh((r) => !r);
  };

  if (loading) return <div>Loading...</div>;
  if (!isAdmin) return <div>Access denied.</div>;

  return (
    <div className="p-4 max-w-2xl mx-auto">
      <h2 className="text-2xl font-bold mb-4">User Management</h2>
      <table className="w-full border">
        <thead>
          <tr>
            <th className="border p-2">Email</th>
            <th className="border p-2">Role</th>
            <th className="border p-2">Actions</th>
          </tr>
        </thead>
        <tbody>
          {users.map((u) => (
            <tr key={u.id}>
              <td className="border p-2">{u.users?.email || u.user_id}</td>
              <td className="border p-2">
                <select
                  value={u.role}
                  onChange={(e) => handleRoleChange(u.id, e.target.value)}
                  className="border rounded px-2 py-1"
                >
                  {rolesList.map((role) => (
                    <option key={role} value={role}>
                      {role.charAt(0).toUpperCase() + role.slice(1)}
                    </option>
                  ))}
                </select>
              </td>
              <td className="border p-2">
                {/* Future: Add remove user, invite user, etc. */}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
}
