import { useAuth } from "@/context/AuthContext";
import { useCallback } from "react";

/**
 * Hook to ensure session is valid before making API calls
 * Automatically refreshes session if needed
 */
export const useSessionGuard = () => {
  const { checkSessionHealth, user } = useAuth();

  const withSessionGuard = useCallback(
    async <T>(apiCall: () => Promise<T>): Promise<T | null> => {
      if (!user) {
        console.warn("No user session available");
        return null;
      }

      // Check session health before making the API call
      const isHealthy = await checkSessionHealth();

      if (!isHealthy) {
        console.warn("Session is not healthy, skipping API call");
        return null;
      }

      try {
        return await apiCall();
      } catch (error) {
        console.error("API call failed:", error);

        // If it's an auth error, check session health again
        if (error && typeof error === "object" && "message" in error) {
          const errorMessage = (error as any).message?.toLowerCase() || "";
          if (
            errorMessage.includes("jwt") ||
            errorMessage.includes("token") ||
            errorMessage.includes("unauthorized")
          ) {
            await checkSessionHealth();
          }
        }

        throw error;
      }
    },
    [checkSessionHealth, user]
  );

  return { withSessionGuard };
};
