import { useCallback, useRef } from "react";

export const useLotterySounds = () => {
  const audioContextRef = useRef<AudioContext | null>(null);
  const spinSoundRef = useRef<HTMLAudioElement | null>(null);

  // Initialize audio context
  const initAudioContext = useCallback(() => {
    if (!audioContextRef.current) {
      audioContextRef.current = new (window.AudioContext ||
        (window as any).webkitAudioContext)();
    }
    return audioContextRef.current;
  }, []);

  // Create a beep sound using Web Audio API
  const createBeep = useCallback(
    (frequency: number, duration: number, volume: number = 0.3) => {
      const audioContext = initAudioContext();

      const oscillator = audioContext.createOscillator();
      const gainNode = audioContext.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(audioContext.destination);

      oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime);
      oscillator.type = "sine";

      gainNode.gain.setValueAtTime(0, audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(
        volume,
        audioContext.currentTime + 0.01
      );
      gainNode.gain.exponentialRampToValueAtTime(
        0.001,
        audioContext.currentTime + duration
      );

      oscillator.start(audioContext.currentTime);
      oscillator.stop(audioContext.currentTime + duration);
    },
    [initAudioContext]
  );

  // Play spinning sound (continuous ticking with acceleration)
  const playSpinSound = useCallback(() => {
    let tickCount = 0;
    const maxTicks = 30;

    const tick = () => {
      if (tickCount >= maxTicks) return;

      // Accelerating tick sound - starts slow, gets faster
      const frequency = 600 + tickCount * 10; // Rising pitch
      const interval = Math.max(50, 150 - tickCount * 4); // Decreasing interval

      createBeep(frequency, 0.03, 0.15);
      tickCount++;

      setTimeout(tick, interval);
    };

    tick();
  }, [createBeep]);

  // Play winner celebration sound
  const playWinnerSound = useCallback(() => {
    // Play a triumphant fanfare melody
    const melody = [
      { freq: 523, time: 0, duration: 0.2 }, // C5
      { freq: 659, time: 0.15, duration: 0.2 }, // E5
      { freq: 784, time: 0.3, duration: 0.2 }, // G5
      { freq: 1047, time: 0.5, duration: 0.4 }, // C6 (longer)
      { freq: 784, time: 0.8, duration: 0.15 }, // G5
      { freq: 1047, time: 0.95, duration: 0.15 }, // C6
      { freq: 1319, time: 1.1, duration: 0.6 }, // E6 (finale)
    ];

    melody.forEach((note) => {
      setTimeout(() => {
        createBeep(note.freq, note.duration, 0.4);
      }, note.time * 1000);
    });

    // Add sparkle effects
    for (let i = 0; i < 5; i++) {
      setTimeout(() => {
        createBeep(1500 + Math.random() * 500, 0.1, 0.2);
      }, 1800 + i * 100);
    }
  }, [createBeep]);

  // Play button click sound
  const playClickSound = useCallback(() => {
    createBeep(1000, 0.1, 0.2);
  }, [createBeep]);

  // Play existing beep sound (from scanner)
  const playBeepSound = useCallback(() => {
    try {
      const audio = new Audio("/beep.mp3");
      audio.volume = 0.5;
      audio.play().catch(console.warn);
    } catch (error) {
      console.warn("Could not play beep sound:", error);
      // Fallback to generated beep
      createBeep(800, 0.2, 0.3);
    }
  }, [createBeep]);

  // Drum roll effect for building suspense
  const playDrumRoll = useCallback(() => {
    const audioContext = initAudioContext();
    let startTime = audioContext.currentTime;

    // Create drum roll effect with noise
    for (let i = 0; i < 30; i++) {
      setTimeout(() => {
        // Create a short burst of noise for drum effect
        const bufferSize = audioContext.sampleRate * 0.05; // 50ms
        const buffer = audioContext.createBuffer(
          1,
          bufferSize,
          audioContext.sampleRate
        );
        const output = buffer.getChannelData(0);

        for (let j = 0; j < bufferSize; j++) {
          output[j] = (Math.random() * 2 - 1) * 0.1; // Low volume noise
        }

        const noise = audioContext.createBufferSource();
        const gainNode = audioContext.createGain();

        noise.buffer = buffer;
        noise.connect(gainNode);
        gainNode.connect(audioContext.destination);

        gainNode.gain.setValueAtTime(0.1, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(
          0.001,
          audioContext.currentTime + 0.05
        );

        noise.start();
      }, i * 100); // Accelerating drum roll
    }
  }, [initAudioContext]);

  return {
    playSpinSound,
    playWinnerSound,
    playClickSound,
    playBeepSound,
    playDrumRoll,
  };
};
