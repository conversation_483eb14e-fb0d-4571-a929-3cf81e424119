import { useEffect } from 'react';
import { useStock } from '@/context/StockContext';

/**
 * Hook to lazily initialize stock data only when needed
 * This prevents loading stock data on app startup if not required
 */
export const useStockInitialization = () => {
  const { initializeStockData } = useStock();

  useEffect(() => {
    // Initialize stock data when this hook is used
    initializeStockData();
  }, [initializeStockData]);
};
