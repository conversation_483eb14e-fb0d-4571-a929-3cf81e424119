import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";
import { useTranslation } from "react-i18next";

// Define shortcut types
export type ShortcutAction =
  | "goToDashboard"
  | "newRepair"
  | "printCurrentRepair"
  | "help";

// Define shortcut configuration
export interface Shortcut {
  key: string;
  ctrlKey?: boolean;
  altKey?: boolean;
  shiftKey?: boolean;
  action: ShortcutAction;
  description: string;
}

// Default shortcuts configuration - using Alt key to avoid browser conflicts
const defaultShortcuts: Shortcut[] = [
  {
    key: "h",
    altKey: true,
    action: "goToDashboard",
    description: "shortcuts.goToDashboard",
  },
  {
    key: "n",
    altKey: true,
    action: "newRepair",
    description: "shortcuts.newRepair",
  },
  {
    key: "p",
    altKey: true,
    action: "printCurrentRepair",
    description: "shortcuts.printCurrentRepair",
  },
  { key: "?", action: "help", description: "shortcuts.help" }, // Keep ? as is since it doesn't conflict
];

export interface UseKeyboardShortcutsProps {
  onPrint?: () => void;
}

export const useKeyboardShortcuts = ({
  onPrint,
}: UseKeyboardShortcutsProps = {}) => {
  const navigate = useNavigate();
  const { t } = useTranslation();
  const [shortcuts, setShortcuts] = useState<Shortcut[]>(defaultShortcuts);
  const [showHelp, setShowHelp] = useState(false);

  // Load shortcuts from localStorage if available
  useEffect(() => {
    const savedShortcuts = localStorage.getItem("keyboardShortcuts");
    if (savedShortcuts) {
      try {
        setShortcuts(JSON.parse(savedShortcuts));
      } catch (error) {
        console.error("Failed to parse saved shortcuts:", error);
      }
    }
  }, []);

  // Save shortcuts to localStorage when they change
  useEffect(() => {
    localStorage.setItem("keyboardShortcuts", JSON.stringify(shortcuts));
  }, [shortcuts]);

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      // Skip if user is typing in an input, textarea, or select
      if (
        event.target instanceof HTMLInputElement ||
        event.target instanceof HTMLTextAreaElement ||
        event.target instanceof HTMLSelectElement
      ) {
        return;
      }

      // Skip if user is using browser's default shortcuts (Ctrl+A, Ctrl+C, Ctrl+V, etc.)
      // Allow only our specific combinations
      if (event.ctrlKey && !event.altKey) {
        // Let browser handle all Ctrl+key combinations
        return;
      }

      // Find matching shortcut
      const matchingShortcut = shortcuts.find(
        (shortcut) =>
          shortcut.key.toLowerCase() === event.key.toLowerCase() &&
          !!shortcut.ctrlKey === event.ctrlKey &&
          !!shortcut.altKey === event.altKey &&
          !!shortcut.shiftKey === event.shiftKey
      );

      if (matchingShortcut) {
        // Only prevent default for our custom shortcuts
        event.preventDefault();
        executeAction(matchingShortcut.action);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => {
      window.removeEventListener("keydown", handleKeyDown);
    };
  }, [shortcuts, navigate, onPrint]);

  // Execute the action associated with a shortcut
  const executeAction = (action: ShortcutAction) => {
    switch (action) {
      case "goToDashboard":
        navigate("/");
        toast.info(t("shortcuts.navigatedToDashboard"));
        break;
      case "newRepair":
        navigate("/new-repair");
        toast.info(t("shortcuts.creatingNewRepair"));
        break;
      case "printCurrentRepair":
        if (onPrint) {
          onPrint();
          toast.info(t("shortcuts.printing"));
        }
        break;
      case "help":
        setShowHelp(true);
        break;
      default:
        console.warn(`No handler for shortcut action: ${action}`);
    }
  };

  // Update a shortcut
  const updateShortcut = (index: number, updatedShortcut: Shortcut) => {
    const newShortcuts = [...shortcuts];
    newShortcuts[index] = updatedShortcut;
    setShortcuts(newShortcuts);
  };

  // Reset shortcuts to default
  const resetShortcuts = () => {
    setShortcuts(defaultShortcuts);
    localStorage.removeItem("keyboardShortcuts");
  };

  return {
    shortcuts,
    updateShortcut,
    resetShortcuts,
    showHelp,
    setShowHelp,
  };
};
