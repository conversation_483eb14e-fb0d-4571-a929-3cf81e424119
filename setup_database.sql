/*
 * REPAIR QR NINJA - COMPLETE DATABASE SETUP SCRIPT
 *
 * This script sets up the complete database structure for the Repair QR Ninja application.
 * It creates all necessary tables, relationships, functions, triggers, and security policies.
 *
 * Use this script when setting up a new database instance for the application.
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- EXTENSIONS AND CONFIGURATION
-- =============================================

-- Enable the uuid-ossp extension for UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- TABLE DEFINITIONS
-- =============================================

-- Create the repair_shops table
CREATE TABLE IF NOT EXISTS public.repair_shops (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    address TEXT,
    phone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create the repairs table
CREATE TABLE IF NOT EXISTS public.repairs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_name TEXT NOT NULL,
    customer_phone TEXT NOT NULL,
    phone_model TEXT NOT NULL,
    problem_description TEXT NOT NULL,
    repair_price NUMERIC NOT NULL,
    payment_status TEXT NOT NULL CHECK (payment_status IN ('paid', 'partial', 'unpaid')),
    down_payment NUMERIC NOT NULL,
    status TEXT NOT NULL CHECK (status IN ('pending', 'inProgress', 'completed', 'cancelled')),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_shop_id UUID REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE
);

-- Create the user_repair_shops junction table
CREATE TABLE IF NOT EXISTS public.user_repair_shops (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('owner', 'admin', 'technician')),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(user_id, repair_shop_id)
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_repairs_user_id ON public.repairs(user_id);
CREATE INDEX IF NOT EXISTS idx_repairs_repair_shop_id ON public.repairs(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_repairs_status ON public.repairs(status);
CREATE INDEX IF NOT EXISTS idx_repairs_payment_status ON public.repairs(payment_status);
CREATE INDEX IF NOT EXISTS idx_user_repair_shops_user_id ON public.user_repair_shops(user_id);
CREATE INDEX IF NOT EXISTS idx_user_repair_shops_repair_shop_id ON public.user_repair_shops(repair_shop_id);

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update the updated_at column
DROP TRIGGER IF EXISTS update_repair_shops_updated_at ON public.repair_shops;
CREATE TRIGGER update_repair_shops_updated_at
    BEFORE UPDATE ON public.repair_shops
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_repairs_updated_at ON public.repairs;
CREATE TRIGGER update_repairs_updated_at
    BEFORE UPDATE ON public.repairs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_repair_shops_updated_at ON public.user_repair_shops;
CREATE TRIGGER update_user_repair_shops_updated_at
    BEFORE UPDATE ON public.user_repair_shops
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Function to ensure all new repairs have a repair_shop_id
CREATE OR REPLACE FUNCTION ensure_repair_has_shop() RETURNS TRIGGER AS $$
DECLARE
    default_shop_id UUID;
BEGIN
    -- If repair_shop_id is NULL, assign the default shop
    IF NEW.repair_shop_id IS NULL THEN
        -- Get the ID of the default repair shop
        SELECT id INTO default_shop_id FROM public.repair_shops LIMIT 1;

        -- Set the repair_shop_id
        NEW.repair_shop_id := default_shop_id;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger for ensuring repairs have a shop
DROP TRIGGER IF EXISTS ensure_repair_has_shop_trigger ON public.repairs;
CREATE TRIGGER ensure_repair_has_shop_trigger
    BEFORE INSERT ON public.repairs
    FOR EACH ROW
    EXECUTE FUNCTION ensure_repair_has_shop();

-- Function to associate existing users with the default repair shop
CREATE OR REPLACE FUNCTION associate_users_with_shop() RETURNS void AS $$
DECLARE
    default_shop_id UUID;
    user_record RECORD;
BEGIN
    -- Get the ID of the default repair shop
    SELECT id INTO default_shop_id FROM public.repair_shops LIMIT 1;

    -- For each user in auth.users
    FOR user_record IN SELECT id FROM auth.users LOOP
        -- Check if the user is already associated with a repair shop
        IF NOT EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_id = user_record.id
        ) THEN
            -- Associate the user with the default repair shop as a technician
            INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role)
            VALUES (user_record.id, default_shop_id, 'technician');
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically associate new users with the default repair shop
CREATE OR REPLACE FUNCTION associate_new_user_with_shop()
RETURNS TRIGGER AS $$
DECLARE
    default_shop_id UUID;
BEGIN
    -- Get the ID of the default repair shop
    SELECT id INTO default_shop_id FROM public.repair_shops LIMIT 1;

    -- Associate the new user with the default repair shop
    INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role)
    VALUES (NEW.id, default_shop_id, 'technician');

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger for new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION associate_new_user_with_shop();

-- =============================================
-- INITIAL DATA
-- =============================================

-- Create a default repair shop (replace with your actual shop details)
INSERT INTO public.repair_shops (name, address, phone)
VALUES ('Your Repair Shop', 'Your Address', 'Your Phone Number')
ON CONFLICT DO NOTHING;

-- Associate existing users with the default repair shop
SELECT associate_users_with_shop();

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================

-- Enable RLS on all tables
ALTER TABLE public.repair_shops ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_repair_shops ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.repairs ENABLE ROW LEVEL SECURITY;

-- Repair Shops table policies
CREATE POLICY "Users can view repair shops they belong to" ON public.repair_shops
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repair_shops.id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

-- User-Repair Shops junction table policies
CREATE POLICY "Users can view their own repair shop associations" ON public.user_repair_shops
    FOR SELECT
    USING (user_id = auth.uid());

-- Repairs table policies
CREATE POLICY "Users can view repairs from their repair shops" ON public.repairs
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repairs.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert repairs for their repair shops" ON public.repairs
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repairs.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update repairs from their repair shops" ON public.repairs
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repairs.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete repairs from their repair shops" ON public.repairs
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repairs.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

-- =============================================
-- REAL-TIME FUNCTIONALITY
-- =============================================

-- Enable real-time functionality for the tables
-- This creates a publication that tracks all changes to the specified tables
DROP PUBLICATION IF EXISTS supabase_realtime;
CREATE PUBLICATION supabase_realtime FOR TABLE repairs, repair_shops, user_repair_shops;

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;
