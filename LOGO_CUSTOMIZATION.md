# Logo Customization Guide

This application supports custom logos that can be easily changed without modifying the code.

## How to Change the Logo

1. Prepare your logo image file:
   - Supported formats: JPG, PNG, or WebP
   - Recommended size: 120x120 pixels (square) for best results
   - Transparent background is recommended for PNG and WebP formats

2. Place your logo file in the `public` directory:
   - Name it `logo.png` (or `logo.jpg` or `logo.webp`)
   - Alternatively, you can use a different filename and update the configuration

3. If you used a different filename or want to customize dimensions, update the configuration:
   - Open `src/config/appConfig.ts`
   - Modify the `logoConfig` object:
     ```typescript
     export const logoConfig = {
       path: '/your-logo-filename.png', // Path relative to public directory
       alt: 'Your Company Logo',         // Alt text for accessibility
       width: 120,                       // Width in pixels
       height: 120,                      // Height in pixels
     };
     ```

## Logo Behavior

- If no logo file is found or if the image fails to load, the application will display a default wrench icon.
- The logo is displayed in the login page and in the navigation bar.
- The logo size is automatically adjusted based on where it appears in the application.

## Troubleshooting

If your logo doesn't appear:

1. Make sure the file is in the correct location (public directory)
2. Check that the path in `appConfig.ts` matches your file name
3. Verify that the file format is supported (JPG, PNG, or WebP)
4. Try clearing your browser cache or using incognito mode to test
