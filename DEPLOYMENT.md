# Deployment Guide for Repair QR Ninja

This guide explains how to deploy the Repair QR Ninja application to a VM using Docker and your existing Nginx setup.

## Prerequisites

- A Virtual Machine (VM) with:
  - Ubuntu 20.04 or newer
  - Docker and Docker Compose installed
  - Nginx installed directly on the host (not in Docker)
  - Certbot installed for SSL certificate management
- A domain or subdomain pointing to your VM's IP address

## Deployment Steps

### 1. Clone the Repository

```bash
git clone https://github.com/yourusername/repair-qr-ninja.git
cd repair-qr-ninja
```

### 2. Configure Environment Variables

Create a `.env` file with your Supabase credentials:

```bash
cp .env.example .env
nano .env
```

Update the following variables:

- `SUPABASE_URL`: Your Supabase project URL
- `SUPABASE_ANON_KEY`: Your Supabase anonymous key

### 3. Set Up the Database

Run the SQL script in your Supabase project to set up the database schema:

1. Log in to your Supabase dashboard
2. Go to the SQL Editor
3. Copy and paste the contents of `setup_database.sql`
4. Execute the script

### 4. Deploy the Application Container

You can deploy the application using either docker-compose or the provided script:

**Option 1: Using docker-compose**

```bash
docker-compose up -d --build
```

**Option 2: Using the build script (recommended)**

```bash
./build-and-run.sh
```

This will:

- Build the application container
- Start the container on port 3001 (host) mapped to port 3000 (container)
- Make the application available at http://localhost:3001

### 5. Configure Nginx

1. Create an Nginx configuration file for your domain:

```bash
sudo nano /etc/nginx/sites-available/repair-qr-ninja
```

2. Copy the contents of the `nginx-host-config.conf` file into this file. The configuration is already set up for `Multitech.talifouni.com`.

3. Enable the site:

```bash
sudo ln -s /etc/nginx/sites-available/repair-qr-ninja /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

### 6. Set Up SSL Certificate

If you don't already have an SSL certificate for your domain, obtain one using Certbot:

```bash
sudo certbot --nginx -d Multitech.talifouni.com
```

Follow the prompts to complete the certificate setup.

## Maintenance

### Updating the Application

To update the application with new code:

1. Pull the latest changes:

```bash
git pull
```

2. Rebuild and restart the container:

```bash
docker-compose up -d --build
```

### Viewing Logs

```bash
# View logs
docker-compose logs

# Follow logs in real-time
docker-compose logs -f
```

### Stopping the Application

```bash
docker-compose down
```

## Troubleshooting

### Application Container Issues

If the application container fails to start:

```bash
# Check container status
docker-compose ps

# View detailed logs
docker-compose logs
```

### Nginx Configuration Issues

If you encounter issues with the Nginx configuration:

```bash
# Test Nginx configuration
sudo nginx -t

# Check Nginx error logs
sudo tail -f /var/log/nginx/error.log
```

### SSL Certificate Issues

If you encounter issues with SSL certificates:

```bash
# Check certificate status
sudo certbot certificates

# Renew certificates manually
sudo certbot renew --dry-run
sudo certbot renew
```

## Security Considerations

- The application uses HTTPS with modern SSL configuration
- Helmet.js is configured for security headers in the Express server
- Nginx is configured with security best practices

For additional security:

1. Set up a firewall:

```bash
sudo ufw allow ssh
sudo ufw allow http
sudo ufw allow https
sudo ufw enable
```

2. Consider setting up fail2ban to protect against brute force attacks:

```bash
sudo apt install fail2ban -y
```
