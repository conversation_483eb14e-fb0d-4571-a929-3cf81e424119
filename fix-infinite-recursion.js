#!/usr/bin/env node

/**
 * REPAIR QR NINJA - FIX INFINITE RECURSION IN RLS POLICIES
 * 
 * This script fixes the infinite recursion error in Row Level Security policies
 * for the user_repair_shops table and related functions.
 * 
 * Run this script with: node fix-infinite-recursion.js
 */

import { createClient } from '@supabase/supabase-js';

const SUPABASE_URL = "https://thcapwaszlsoqhskkxsc.supabase.co";
const SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_KEY;

if (!SUPABASE_SERVICE_KEY) {
  console.error('❌ SUPABASE_SERVICE_KEY environment variable is required');
  console.log('Please set it with your Supabase service role key (not the anon key)');
  console.log('You can find this in your Supabase dashboard under Settings > API');
  process.exit(1);
}

// Create admin client with service key
const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_KEY, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

const migrationSQL = `
-- =============================================
-- STEP 1: DROP ALL PROBLEMATIC RLS POLICIES
-- =============================================

-- Drop all existing policies that might cause recursion
DROP POLICY IF EXISTS "Users can view repair shops they belong to" ON public.repair_shops;
DROP POLICY IF EXISTS "Administrators can manage repair shops" ON public.repair_shops;
DROP POLICY IF EXISTS "All authenticated users can view repair shops" ON public.repair_shops;

DROP POLICY IF EXISTS "Users can view their own repair shop associations" ON public.user_repair_shops;
DROP POLICY IF EXISTS "Administrators can view repair shop associations" ON public.user_repair_shops;
DROP POLICY IF EXISTS "Administrators can manage user-repair shop associations" ON public.user_repair_shops;
DROP POLICY IF EXISTS "Admins can manage user-repair shop associations" ON public.user_repair_shops;
DROP POLICY IF EXISTS "Users can insert their own repair shop associations" ON public.user_repair_shops;
DROP POLICY IF EXISTS "Users can update their own repair shop associations" ON public.user_repair_shops;

-- =============================================
-- STEP 2: CREATE SIMPLE, NON-RECURSIVE POLICIES
-- =============================================

-- REPAIR SHOPS: Allow all authenticated users to view and manage
CREATE POLICY "Authenticated users can access repair shops" ON public.repair_shops
    FOR ALL USING (auth.uid() IS NOT NULL);

-- USER_REPAIR_SHOPS: Simple policies without recursion
-- Users can only see their own associations
CREATE POLICY "Users can view own associations" ON public.user_repair_shops
    FOR SELECT USING (user_id = auth.uid());

-- Users can insert their own associations
CREATE POLICY "Users can insert own associations" ON public.user_repair_shops
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Users can update their own associations
CREATE POLICY "Users can update own associations" ON public.user_repair_shops
    FOR UPDATE USING (user_id = auth.uid());

-- Users can delete their own associations
CREATE POLICY "Users can delete own associations" ON public.user_repair_shops
    FOR DELETE USING (user_id = auth.uid());
`;

const functionSQL = `
-- =============================================
-- STEP 3: FIX FUNCTIONS WITH PROPER TYPE CASTING
-- =============================================

-- Drop and recreate the function with proper type casting
DROP FUNCTION IF EXISTS public.get_shop_users(UUID);

-- Function to get users for a specific repair shop (with proper type casting)
CREATE OR REPLACE FUNCTION public.get_shop_users(shop_id UUID)
RETURNS TABLE (
    user_id UUID,
    email TEXT,
    full_name TEXT,
    role TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        urs.user_id,
        au.email::TEXT,  -- Cast varchar to TEXT to match return type
        COALESCE(urs.full_name, '')::TEXT,  -- Ensure full_name is TEXT and handle nulls
        urs.role::TEXT,  -- Cast to TEXT to be safe
        urs.created_at,
        urs.updated_at
    FROM public.user_repair_shops urs
    JOIN auth.users au ON urs.user_id = au.id
    WHERE urs.repair_shop_id = shop_id
    ORDER BY urs.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Also fix the get_user_repair_shop function if it has similar issues
DROP FUNCTION IF EXISTS public.get_user_repair_shop();

CREATE OR REPLACE FUNCTION public.get_user_repair_shop()
RETURNS TABLE (
    repair_shop_id UUID,
    repair_shop_name TEXT,
    user_role TEXT,
    full_name TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        urs.repair_shop_id,
        rs.name::TEXT as repair_shop_name,  -- Cast to TEXT
        urs.role::TEXT as user_role,        -- Cast to TEXT
        COALESCE(urs.full_name, '')::TEXT   -- Cast to TEXT and handle nulls
    FROM public.user_repair_shops urs
    JOIN public.repair_shops rs ON urs.repair_shop_id = rs.id
    WHERE urs.user_id = auth.uid()
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
`;

async function runMigration() {
  console.log('🔧 Starting infinite recursion fix...');
  
  try {
    // Step 1: Fix RLS policies
    console.log('📝 Fixing RLS policies...');
    const { error: policyError } = await supabase.rpc('exec_sql', { 
      sql: migrationSQL 
    });
    
    if (policyError) {
      console.error('❌ Error fixing policies:', policyError);
      // Try direct SQL execution
      const { error: directError } = await supabase
        .from('_supabase_admin')
        .select('*')
        .limit(0); // This will fail but we can try the SQL directly
        
      // If the above doesn't work, we'll need to execute each statement separately
      console.log('🔄 Trying to execute SQL statements individually...');
      
      // Execute each policy drop/create individually
      const statements = migrationSQL.split(';').filter(stmt => stmt.trim());
      
      for (const statement of statements) {
        if (statement.trim()) {
          try {
            const { error } = await supabase.rpc('exec_sql', { 
              sql: statement.trim() + ';' 
            });
            if (error) {
              console.warn(`⚠️  Warning executing statement: ${error.message}`);
            }
          } catch (e) {
            console.warn(`⚠️  Warning executing statement: ${e.message}`);
          }
        }
      }
    } else {
      console.log('✅ RLS policies fixed successfully');
    }
    
    // Step 2: Fix functions
    console.log('🔧 Fixing functions...');
    const { error: functionError } = await supabase.rpc('exec_sql', { 
      sql: functionSQL 
    });
    
    if (functionError) {
      console.error('❌ Error fixing functions:', functionError);
      
      // Try executing function statements individually
      const functionStatements = functionSQL.split(';').filter(stmt => stmt.trim());
      
      for (const statement of functionStatements) {
        if (statement.trim()) {
          try {
            const { error } = await supabase.rpc('exec_sql', { 
              sql: statement.trim() + ';' 
            });
            if (error) {
              console.warn(`⚠️  Warning executing function statement: ${error.message}`);
            }
          } catch (e) {
            console.warn(`⚠️  Warning executing function statement: ${e.message}`);
          }
        }
      }
    } else {
      console.log('✅ Functions fixed successfully');
    }
    
    // Step 3: Verify the fix
    console.log('🔍 Verifying the fix...');
    const { data: policies, error: verifyError } = await supabase
      .from('pg_policies')
      .select('schemaname, tablename, policyname, cmd')
      .in('tablename', ['repair_shops', 'user_repair_shops']);
    
    if (!verifyError && policies) {
      console.log('📋 Current policies:');
      policies.forEach(policy => {
        console.log(`  - ${policy.tablename}: ${policy.policyname} (${policy.cmd})`);
      });
    }
    
    console.log('✅ Migration completed successfully!');
    console.log('🔄 Please refresh your application and try again.');
    
  } catch (error) {
    console.error('❌ Migration failed:', error);
    console.log('\n📋 Manual steps to fix the issue:');
    console.log('1. Go to your Supabase dashboard');
    console.log('2. Navigate to SQL Editor');
    console.log('3. Run the SQL from migrations/fix_get_shop_users_function.sql');
    console.log('4. Or copy the SQL statements from this script and run them manually');
  }
}

// Run the migration
runMigration();
