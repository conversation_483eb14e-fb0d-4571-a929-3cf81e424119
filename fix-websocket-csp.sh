#!/bin/bash

# This script fixes the WebSocket CSP issues by directly updating the Nginx configuration

echo "=== Fixing WebSocket CSP Issues ==="
echo "This script will update your Nginx configuration to properly allow WebSocket connections"

# Update Nginx configuration
echo "Updating Nginx configuration..."
echo "Please enter your sudo password when prompted."

# Assuming the Nginx config is in /etc/nginx/sites-available/
NGINX_CONFIG_PATH="/etc/nginx/sites-available/Multitech.talifouni.com"

# Check if the file exists
if [ -f "$NGINX_CONFIG_PATH" ]; then
    # Backup the existing config
    sudo cp "$NGINX_CONFIG_PATH" "${NGINX_CONFIG_PATH}.bak.$(date +%Y%m%d%H%M%S)"
    
    # Copy the new config
    sudo cp nginx-host-config-fixed.conf "$NGINX_CONFIG_PATH"
    
    # Test Nginx configuration
    echo "Testing Nginx configuration..."
    sudo nginx -t
    
    if [ $? -eq 0 ]; then
        # Reload Nginx if the test was successful
        echo "Reloading Nginx..."
        sudo systemctl reload nginx
        echo "Nginx configuration updated and reloaded successfully."
    else
        echo "Nginx configuration test failed. Reverting to backup..."
        sudo cp "${NGINX_CONFIG_PATH}.bak.$(ls -t ${NGINX_CONFIG_PATH}.bak.* | head -1)" "$NGINX_CONFIG_PATH"
        sudo systemctl reload nginx
        echo "Reverted to previous configuration."
        exit 1
    fi
else
    echo "Nginx configuration file not found at $NGINX_CONFIG_PATH"
    echo "Please manually update your Nginx configuration with the content from nginx-host-config-fixed.conf"
    echo "Then reload Nginx with: sudo systemctl reload nginx"
fi

echo ""
echo "=== Fix Completed ==="
echo "The Nginx configuration has been updated with the correct Content Security Policy settings."
echo "Please check your application at https://Multitech.talifouni.com to verify the issues are resolved."
echo ""
echo "If you still encounter issues, you may need to:"
echo "1. Clear your browser cache or use incognito mode"
echo "2. Check the Nginx error logs: sudo tail -f /var/log/nginx/error.log"
echo "3. Manually edit the Nginx configuration if needed"
