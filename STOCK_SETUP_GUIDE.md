# Stock Management System - Database Setup Guide

## 🚀 Quick Setup Instructions

### Step 1: Run Database Schema
1. Open your **Supabase Dashboard**
2. Go to **SQL Editor**
3. Copy and paste the content from `setup_stock_database.sql`
4. Click **Run** to create all tables, policies, and triggers

### Step 2: Get Your Repair Shop ID
Run this query in the SQL Editor to get your repair shop ID:
```sql
SELECT id, name FROM public.repair_shops LIMIT 1;
```
Copy the `id` value - you'll need it for the next step.

### Step 3: Add Sample Data (Optional)
1. Open `sample_stock_data.sql`
2. Replace **ALL** instances of `'your-repair-shop-id'` with your actual repair shop ID
3. Replace **ALL** instances of category placeholder IDs with actual category IDs (see instructions below)
4. Run the modified script in SQL Editor

### Step 4: Update Category IDs in Sample Data
After running the categories insert, get the actual category IDs:
```sql
SELECT id, name FROM public.product_categories WHERE repair_shop_id = 'your-actual-repair-shop-id';
```

Then replace these placeholders in the sample data:
- `'mobile-phones-category-id'` → actual Mobile Phones category ID
- `'accessories-category-id'` → actual Phone Accessories category ID  
- `'screen-protectors-category-id'` → actual Screen Protectors category ID
- `'repair-tools-category-id'` → actual Repair Tools category ID
- `'audio-category-id'` → actual Audio Accessories category ID

## 🎯 What You'll Get

### ✅ Database Tables Created:
- **product_categories** - Organize products into categories
- **products** - Complete product catalog with pricing and stock
- **sales** - Transaction records with automatic numbering
- **sale_items** - Individual items in each sale
- **stock_movements** - Complete inventory audit trail

### ✅ Sample Data Includes:
- **5 Product Categories**: Mobile Phones, Accessories, Screen Protectors, Repair Tools, Audio
- **20+ Sample Products**: iPhones, Samsung phones, cases, cables, tools, etc.
- **Stock Levels**: Realistic inventory quantities with low stock examples
- **Pricing**: Sample costs and selling prices

### ✅ Automatic Features:
- **Sale Numbers**: Auto-generated (SALE-0001, SALE-0002, etc.)
- **Stock Updates**: Automatic inventory adjustments on sales
- **Stock Tracking**: Complete audit trail of all stock movements
- **RLS Security**: Multi-tenant data isolation
- **Real-time Updates**: Live synchronization across users

## 🔧 Testing the System

### 1. Add Categories
- Go to Stock & POS → Categories tab
- Click "Add Category" 
- Fill in name, description, choose icon and color
- Save and see it appear in the list

### 2. Add Products  
- Go to Products tab
- Click "Add Product"
- Fill in all required fields (name, SKU, category, price, cost)
- Set stock quantities and minimum levels
- Save and see it in the product grid

### 3. Process a Sale
- Go to POS tab
- Search and click products to add to cart
- Adjust quantities as needed
- Enter customer information (optional)
- Select payment method
- Enter amount paid
- Click "Process Sale"
- Watch stock levels update automatically

### 4. Monitor Inventory
- Go to Inventory tab
- See stock statistics and alerts
- Check low stock and out of stock items
- View total inventory value

### 5. Review Sales
- Go to Sales tab  
- See all processed transactions
- View sale details and customer information
- Check daily revenue totals

## 🎨 UI Features

### Touch-Friendly Design
- **Auto-Detection**: Automatically detects touch devices
- **Large Buttons**: 44px+ touch targets for easy interaction
- **Responsive Layout**: Adapts to tablets and phones
- **Grid/List Views**: Flexible display options

### Multi-Language Support
- **English**: Complete interface
- **French**: "Stock & PDV" (Point de Vente)
- **Arabic**: "المخزون ونقطة البيع" with RTL support

### Dark Mode Support
- **Automatic**: Follows system theme preference
- **Manual Toggle**: Switch between light and dark modes
- **Consistent**: All components support both themes

## 🔐 Security Features

### Row Level Security (RLS)
- Users can only access their repair shop's data
- Automatic data isolation between repair shops
- Secure multi-tenant architecture

### Data Validation
- Server-side validation for all operations
- Prevents invalid data entry
- Maintains data integrity

### Audit Trail
- Complete tracking of all stock movements
- User attribution for all changes
- Timestamp tracking for all operations

## 📊 Performance Features

### Database Optimization
- Indexes on all frequently queried columns
- Efficient joins and relationships
- Optimized for fast searches and filtering

### Real-time Updates
- Live inventory synchronization
- Instant sales notifications
- Multi-user collaboration support

### Caching
- Context-based state management
- Efficient data loading
- Minimal API calls

## 🚨 Troubleshooting

### Common Issues:

1. **"No categories found"**
   - Make sure you've run the database schema setup
   - Check that categories were inserted with correct repair shop ID

2. **"Cannot add products"**
   - Ensure at least one category exists
   - Verify all required fields are filled

3. **"Stock not updating"**
   - Check that triggers were created properly
   - Verify RLS policies are active

4. **"Permission denied"**
   - Confirm user is associated with a repair shop
   - Check RLS policies are correctly configured

### Getting Help:
- Check browser console for error messages
- Verify database connection in Supabase dashboard
- Ensure all SQL scripts ran without errors

## 🎉 You're Ready!

Your stock management system is now fully functional with:
- ✅ Complete product catalog management
- ✅ Touch-friendly POS interface  
- ✅ Real-time inventory tracking
- ✅ Comprehensive sales reporting
- ✅ Multi-user collaboration
- ✅ Mobile and desktop support

Start by adding your product categories, then your inventory, and begin processing sales! 🚀📱💰
