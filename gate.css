/* gate.css */
body {
  margin: 0;
  padding: 0;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  background: linear-gradient(145deg, #f0f4f8, #d9e2ec);
  height: 100vh;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  color: #1f2937;
  position: relative;
}

.brand {
  position: absolute;
  top: 16px;
  font-size: 18px;
  font-weight: bold;
  color: #3b82f6;
}

h1 {
  font-size: 24px;
  margin-bottom: 16px;
  color: #1a202c;
  text-align: center;
}

form {
  background: white;
  padding: 24px 32px;
  border-radius: 12px;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.08);
  width: 100%;
  max-width: 400px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

label {
  font-weight: 600;
  margin-bottom: 4px;
}

.input-with-icon {
  position: relative;
  display: flex;
  align-items: center;
}

.input-with-icon svg {
  position: absolute;
  left: 12px;
  width: 20px;
  height: 20px;
  fill: #94a3b8;
  pointer-events: none;
}

.input-with-icon input {
  width: 100%;
  padding: 10px 12px 10px 40px; /* left padding increased for icon */
  border-radius: 8px;
  border: 1px solid #cbd5e0;
  font-size: 16px;
  outline: none;
  box-sizing: border-box; /* important to prevent overflow */
  transition: 0.2s border-color;
}

.input-with-icon input:focus {
  border-color: #3b82f6;
}

input[type="text"] {
  width: 100%;
  padding: 10px 14px 10px 40px;
  border-radius: 8px;
  border: 1px solid #cbd5e0;
  font-size: 16px;
  outline: none;
  transition: 0.2s border-color;
}

input[type="text"]:focus {
  border-color: #3b82f6;
}

button {
  width: 100%;
  padding: 10px;
  background-color: #3b82f6;
  color: white;
  font-weight: 600;
  font-size: 16px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: background 0.3s ease;
}

button:hover {
  background-color: #2563eb;
}
