/* index.css */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

body {
  height: 100vh;
  background: #f2f2f2;
  display: flex;
  align-items: center;
  justify-content: center;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
  color: #000;
}

form#license-gate {
  background: #fff;
  padding: 2rem;
  border: 1px solid #ccc;
  box-shadow: 0 0 20px rgba(0, 0, 0, 0.05);
  width: 100%;
  max-width: 400px;
  text-align: center;
}

form#license-gate label {
  display: block;
  font-size: 1rem;
  margin-bottom: 1rem;
  color: #000;
}

form#license-gate input[type="text"] {
  width: 100%;
  padding: 0.75rem;
  margin-bottom: 1.5rem;
  border: 1px solid #000;
  font-size: 1rem;
  background: #fff;
  color: #000;
}

form#license-gate input[type="text"]:focus {
  outline: none;
  border-color: #000;
}

form#license-gate button {
  background: #000;
  color: #fff;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  border: none;
  cursor: pointer;
  transition: opacity 0.3s ease;
}

form#license-gate button:hover {
  opacity: 0.85;
}
