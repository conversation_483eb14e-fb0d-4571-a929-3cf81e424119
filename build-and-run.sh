#!/bin/bash

# Stop and remove any existing containers
echo "Stopping and removing existing containers..."
docker stop $(docker ps -a -q --filter "name=repair-qr-ninja") 2>/dev/null || true
docker rm $(docker ps -a -q --filter "name=repair-qr-ninja") 2>/dev/null || true

# Build the Docker image
echo "Building Docker image..."
docker build -t repair-qr-ninja .

# Run the container
echo "Starting container..."
docker run -d --name repair-qr-ninja -p 3001:3000 \
  -e NODE_ENV=production \
  -e VITE_SUPABASE_URL=${SUPABASE_URL} \
  -e VITE_SUPABASE_ANON_KEY=${SUPABASE_ANON_KEY} \
  -v $(pwd)/public:/app/public \
  repair-qr-ninja

echo "Container started. Check logs with: docker logs repair-qr-ninja"
