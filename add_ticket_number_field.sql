-- Add ticket_number field to repairs table
ALTER TABLE public.repairs ADD COLUMN IF NOT EXISTS ticket_number INTEGER;

-- Create a sequence for ticket numbers starting from 10001
CREATE SEQUENCE IF NOT EXISTS repair_ticket_number_seq START WITH 10001;

-- Update existing repairs with sequential ticket numbers
DO $$
DECLARE
    repair_record RECORD;
BEGIN
    -- For each repair without a ticket number, assign one from the sequence
    FOR repair_record IN 
        SELECT id FROM public.repairs 
        WHERE ticket_number IS NULL
        ORDER BY created_at ASC
    LOOP
        UPDATE public.repairs 
        SET ticket_number = nextval('repair_ticket_number_seq')
        WHERE id = repair_record.id;
    END LOOP;
END $$;

-- Create a trigger to automatically assign ticket numbers to new repairs
CREATE OR REPLACE FUNCTION assign_ticket_number()
RETURNS TRIGGER AS $$
BEGIN
    -- If ticket_number is NULL, assign a new one from the sequence
    IF NEW.ticket_number IS NULL THEN
        NEW.ticket_number := nextval('repair_ticket_number_seq');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger for new repairs
DROP TRIGGER IF EXISTS assign_ticket_number_trigger ON public.repairs;
CREATE TRIGGER assign_ticket_number_trigger
    BEFORE INSERT ON public.repairs
    FOR EACH ROW
    EXECUTE FUNCTION assign_ticket_number();

-- Update the supabase_realtime publication to include the new field
ALTER PUBLICATION supabase_realtime REFRESH TABLE repairs;
