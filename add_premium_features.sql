-- Premium Plan Features Migration
-- This script adds tables and functionality for the premium plan features

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- TECH<PERSON>CIAN TIME TRACKING
-- =============================================

-- Create the technician_time_entries table
CREATE TABLE IF NOT EXISTS public.technician_time_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_id UUID REFERENCES public.repairs(id) ON DELETE SET NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_time_entries_user_id ON public.technician_time_entries(user_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_repair_id ON public.technician_time_entries(repair_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_repair_shop_id ON public.technician_time_entries(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_start_time ON public.technician_time_entries(start_time);

-- Enable Row Level Security
ALTER TABLE public.technician_time_entries ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for technician_time_entries
CREATE POLICY "Users can view time entries from their repair shop" ON public.technician_time_entries
    FOR SELECT USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own time entries" ON public.technician_time_entries
    FOR INSERT WITH CHECK (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
        AND user_id = auth.uid()
    );

CREATE POLICY "Users can update their own time entries" ON public.technician_time_entries
    FOR UPDATE USING (
        user_id = auth.uid()
    );

CREATE POLICY "Administrators can update any time entries in their shop" ON public.technician_time_entries
    FOR UPDATE USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
            AND role = 'administrator'
        )
    );

-- =============================================
-- CUSTOMIZABLE FORMS
-- =============================================

-- Create the form_templates table
CREATE TABLE IF NOT EXISTS public.form_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    device_type TEXT NOT NULL,
    fields JSONB NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create the form_responses table
CREATE TABLE IF NOT EXISTS public.form_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repair_id UUID NOT NULL REFERENCES public.repairs(id) ON DELETE CASCADE,
    template_id UUID NOT NULL REFERENCES public.form_templates(id) ON DELETE CASCADE,
    responses JSONB NOT NULL,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_form_templates_repair_shop_id ON public.form_templates(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_form_templates_device_type ON public.form_templates(device_type);
CREATE INDEX IF NOT EXISTS idx_form_responses_repair_id ON public.form_responses(repair_id);
CREATE INDEX IF NOT EXISTS idx_form_responses_template_id ON public.form_responses(template_id);
CREATE INDEX IF NOT EXISTS idx_form_responses_repair_shop_id ON public.form_responses(repair_shop_id);

-- Create function for updated_at timestamp
CREATE OR REPLACE FUNCTION update_form_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
CREATE TRIGGER trigger_update_form_templates_updated_at
    BEFORE UPDATE ON public.form_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_form_updated_at_column();

CREATE TRIGGER trigger_update_form_responses_updated_at
    BEFORE UPDATE ON public.form_responses
    FOR EACH ROW
    EXECUTE FUNCTION update_form_updated_at_column();

-- Enable Row Level Security
ALTER TABLE public.form_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.form_responses ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for form_templates
CREATE POLICY "Users can view form templates from their repair shop" ON public.form_templates
    FOR SELECT USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Administrators can manage form templates" ON public.form_templates
    FOR ALL USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
            AND role = 'administrator'
        )
    );

-- Create RLS policies for form_responses
CREATE POLICY "Users can view form responses from their repair shop" ON public.form_responses
    FOR SELECT USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert form responses for their repair shop" ON public.form_responses
    FOR INSERT WITH CHECK (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

-- =============================================
-- USER ACTION LOGGING
-- =============================================

-- Create an action log table for traceability
CREATE TABLE IF NOT EXISTS public.user_action_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_user_action_logs_user_id ON public.user_action_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_action_logs_created_at ON public.user_action_logs(created_at);

-- Enable Row Level Security
ALTER TABLE public.user_action_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_action_logs
CREATE POLICY "Users can insert their own action logs" ON public.user_action_logs
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Administrators can view action logs from their repair shop" ON public.user_action_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.user_id = auth.uid()
            AND user_repair_shops.role = 'administrator'
        )
    );

-- =============================================
-- USER ACTION LOGGING
-- =============================================

-- Create an action log table for traceability
CREATE TABLE IF NOT EXISTS public.user_action_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_user_action_logs_user_id ON public.user_action_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_action_logs_created_at ON public.user_action_logs(created_at);

-- Enable Row Level Security
ALTER TABLE public.user_action_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_action_logs
CREATE POLICY "Users can insert their own action logs" ON public.user_action_logs
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Administrators can view action logs from their repair shop" ON public.user_action_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.user_id = auth.uid()
            AND user_repair_shops.role = 'administrator'
        )
    );

-- =============================================
-- ENABLE REAL-TIME FOR NEW TABLES
-- =============================================

ALTER PUBLICATION supabase_realtime ADD TABLE public.technician_time_entries;
ALTER PUBLICATION supabase_realtime ADD TABLE public.form_templates;
ALTER PUBLICATION supabase_realtime ADD TABLE public.form_responses;

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;