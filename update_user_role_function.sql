-- Create function to update user role (bypasses RLS)
CREATE OR <PERSON><PERSON><PERSON>CE FUNCTION update_user_role(
  user_role_id UUID,
  new_role VARCHAR
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  result JSON;
BEGIN
  -- Update the user role
  UPDATE public.user_repair_shops 
  SET role = new_role, updated_at = NOW()
  WHERE id = user_role_id;
  
  -- Check if update was successful
  IF FOUND THEN
    result := json_build_object(
      'success', true,
      'message', 'Role updated successfully'
    );
  ELSE
    result := json_build_object(
      'success', false,
      'error', 'User role record not found'
    );
  END IF;
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  result := json_build_object(
    'success', false,
    'error', SQLERRM
  );
  RETURN result;
END;
$$;