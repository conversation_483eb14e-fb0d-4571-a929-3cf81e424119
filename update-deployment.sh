#!/bin/bash

# This script updates the deployment with the new CSP settings

# Pull the latest changes
echo "Pulling latest changes..."
git pull

# Rebuild the Docker container
echo "Rebuilding Docker container..."
./build-and-run.sh

# Update Nginx configuration
echo "Updating Nginx configuration..."
echo "Please enter your sudo password when prompted to update the Nginx configuration."

# Assuming the Nginx config is in /etc/nginx/sites-available/
NGINX_CONFIG_PATH="/etc/nginx/sites-available/zonetech.talifouni.com"

# Check if the file exists
if [ -f "$NGINX_CONFIG_PATH" ]; then
    # Backup the existing config
    sudo cp "$NGINX_CONFIG_PATH" "${NGINX_CONFIG_PATH}.bak"

    # Copy the new config
    sudo cp nginx-host-config.conf "$NGINX_CONFIG_PATH"

    # Test Nginx configuration
    echo "Testing Nginx configuration..."
    sudo nginx -t

    if [ $? -eq 0 ]; then
        # Reload Nginx if the test was successful
        echo "Reloading Nginx..."
        sudo systemctl reload nginx
        echo "Nginx configuration updated and reloaded successfully."
    else
        echo "Nginx configuration test failed. Reverting to backup..."
        sudo cp "${NGINX_CONFIG_PATH}.bak" "$NGINX_CONFIG_PATH"
        sudo systemctl reload nginx
        echo "Reverted to previous configuration."
    fi
else
    echo "Nginx configuration file not found at $NGINX_CONFIG_PATH"
    echo "Please manually update your Nginx configuration with the content from nginx-host-config.conf"
fi

echo "Deployment update completed."
echo "Check your application at your domain to verify the CSP issues are resolved."
