-- Migration: Add RBAC roles and action log for premium plan
-- 1. Drop policies that reference the role column
DROP POLICY IF EXISTS "Admins can manage user-repair shop associations" ON public.user_repair_shops;
DROP POLICY IF EXISTS "Users can view their own user-repair shop associations" ON public.user_repair_shops;

-- Drop policies on repair_shops that reference user_repair_shops.role
DROP POLICY IF EXISTS "Admins can manage repair shops" ON public.repair_shops;

-- Drop policies on repairs that reference user_repair_shops.role
DROP POLICY IF EXISTS "Technicians and above can view repairs" ON public.repairs;
DROP POLICY IF EXISTS "Technicians and above can insert repairs" ON public.repairs;
DROP POLICY IF EXISTS "Technicians and above can update repairs" ON public.repairs;
DROP POLICY IF EXISTS "Only administrators can delete repairs" ON public.repairs;

-- 2. Alter the column type
ALTER TABLE public.user_repair_shops
    ALTER COLUMN role TYPE VARCHAR(32);

-- 3. Add the check constraint for allowed roles
ALTER TABLE public.user_repair_shops
    DROP CONSTRAINT IF EXISTS user_repair_shops_role_check;
ALTER TABLE public.user_repair_shops
    ADD CONSTRAINT user_repair_shops_role_check
    CHECK (role IN ('administrator', 'technician', 'receptionist', 'cashier'));

-- 4. Recreate the policies
CREATE POLICY "Admins can manage user-repair shop associations" ON public.user_repair_shops
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops AS urs
            WHERE urs.user_id = auth.uid()
            AND urs.repair_shop_id = user_repair_shops.repair_shop_id
            AND urs.role = 'administrator'
        )
    );

CREATE POLICY "Users can view their own user-repair shop associations" ON public.user_repair_shops
    FOR SELECT
    USING (user_id = auth.uid());

DROP POLICY IF EXISTS "Users can view repair shops they belong to" ON public.repair_shops;
CREATE POLICY "Users can view repair shops they belong to" ON public.repair_shops
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repair_shops.id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

DROP POLICY IF EXISTS "Users can view repairs from their repair shops" ON public.repairs;
CREATE POLICY "Users can view repairs from their repair shops" ON public.repairs
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repairs.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

-- 5. Create an action log table for traceability
CREATE TABLE IF NOT EXISTS public.user_action_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- 6. (Optional) Index for faster queries
CREATE INDEX IF NOT EXISTS idx_user_action_logs_user_id ON public.user_action_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_action_logs_created_at ON public.user_action_logs(created_at);

-- 7. (Optional) Insert sample roles for existing users (update as needed)
-- UPDATE public.user_repair_shops SET role = 'technician' WHERE role IS NULL;
