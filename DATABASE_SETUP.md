# Database Setup Guide for Repair QR Ninja

This guide explains how to set up the database for the Repair QR Ninja application using the provided SQL script.

## Overview

The `setup_database.sql` script creates a complete database structure for the application, including:

- Tables for repair shops, repairs, and user-repair shop relationships
- Indexes for performance optimization
- Functions and triggers for data integrity and automation
- Row-level security policies for data protection
- Real-time functionality for live updates

## Prerequisites

- A Supabase project with access to the SQL editor
- Admin access to the Supabase project

## Setup Instructions

### 1. Customize the Default Repair Shop

Before running the script, you may want to customize the default repair shop information. Open the `setup_database.sql` file and locate the following section (around line 185):

```sql
-- Create a default repair shop (replace with your actual shop details)
INSERT INTO public.repair_shops (name, address, phone)
VALUES ('Your Repair Shop', 'Your Address', 'Your Phone Number')
ON CONFLICT DO NOTHING;
```

Replace `'Your Repair Shop'`, `'Your Address'`, and `'Your Phone Number'` with your actual shop details.

### 2. Run the SQL Script

1. Log in to your Supabase dashboard
2. Navigate to the SQL Editor
3. Copy and paste the entire contents of the `setup_database.sql` file
4. Click "Run" to execute the script

The script is wrapped in a transaction, so either all operations will succeed or none will be applied, ensuring database consistency.

### 3. Verify the Setup

After running the script, you can verify the setup by:

1. Checking the Tables section in the Supabase dashboard to ensure all tables were created
2. Checking the Policies section to ensure all security policies were applied
3. Checking the Database Functions section to ensure all functions were created

## Database Structure

### Tables

1. **repair_shops**: Stores information about repair shops
   - `id`: Unique identifier (UUID)
   - `name`: Shop name
   - `address`: Shop address (optional)
   - `phone`: Shop phone number (optional)
   - `created_at`: Timestamp when the record was created
   - `updated_at`: Timestamp when the record was last updated

2. **repairs**: Stores information about repair jobs
   - `id`: Unique identifier (UUID)
   - `customer_name`: Name of the customer
   - `customer_phone`: Phone number of the customer
   - `phone_model`: Model of the phone being repaired
   - `problem_description`: Description of the problem
   - `repair_price`: Price of the repair
   - `payment_status`: Status of payment (paid, partial, unpaid)
   - `down_payment`: Amount paid as down payment
   - `status`: Status of the repair (pending, inProgress, completed, cancelled)
   - `user_id`: ID of the user who created the repair
   - `repair_shop_id`: ID of the repair shop
   - `created_at`: Timestamp when the record was created
   - `updated_at`: Timestamp when the record was last updated
   - `completed_at`: Timestamp when the repair was completed (optional)

3. **user_repair_shops**: Junction table linking users to repair shops
   - `id`: Unique identifier (UUID)
   - `user_id`: ID of the user
   - `repair_shop_id`: ID of the repair shop
   - `role`: Role of the user in the repair shop (owner, admin, technician)
   - `created_at`: Timestamp when the record was created
   - `updated_at`: Timestamp when the record was last updated

### Security Policies

The script sets up Row Level Security (RLS) policies to ensure that:

- Users can only view repair shops they belong to
- Users can only view their own repair shop associations
- Users can only view, insert, update, and delete repairs from repair shops they belong to

### Automation

The script includes several automated features:

- Automatic updating of `updated_at` timestamps when records are modified
- Automatic assignment of repairs to the default repair shop if no shop is specified
- Automatic association of new users with the default repair shop
- Association of existing users with the default repair shop

## Troubleshooting

If you encounter any issues while running the script:

1. **Syntax Errors**: Make sure you're using the latest version of the script and that no modifications have introduced syntax errors.

2. **Permission Issues**: Ensure you have admin access to the Supabase project.

3. **Existing Tables**: If you're running the script on a database that already has some of these tables, you might see errors. The script uses `IF NOT EXISTS` clauses to avoid conflicts, but if the existing tables have different structures, you might need to drop them first.

4. **Missing Extensions**: If the `uuid-ossp` extension is not available, contact your Supabase administrator.

## Resetting the Database

If you need to reset the database and start fresh:

1. Navigate to the SQL Editor in your Supabase dashboard
2. Run the following SQL to drop all tables:

```sql
DROP TABLE IF EXISTS public.repairs CASCADE;
DROP TABLE IF EXISTS public.user_repair_shops CASCADE;
DROP TABLE IF EXISTS public.repair_shops CASCADE;
```

3. Then run the `setup_database.sql` script again to recreate everything

## Updating the Database

If you need to update the database structure in the future:

1. Create a new SQL script with only the changes you need to make
2. Run this script in the SQL Editor

Avoid running the full `setup_database.sql` script on an existing database unless you're prepared to reset everything.
