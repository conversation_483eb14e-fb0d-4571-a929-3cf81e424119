-- Create time tracking tables and triggers
BEGIN;

-- Add assigned_technician column to repairs table
ALTER TABLE public.repairs 
ADD COLUMN IF NOT EXISTS assigned_technician UUID REFERENCES auth.users(id);

-- Create time_tracking table
CREATE TABLE IF NOT EXISTS public.time_tracking (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repair_id UUID NOT NULL REFERENCES public.repairs(id) ON DELETE CASCADE,
    technician_id UUID NOT NULL REFERENCES auth.users(id),
    status_from VARCHAR(20),
    status_to VARCHAR(20) NOT NULL,
    started_at TIMESTAMP WITH TIME ZONE NOT NULL,
    ended_at TIMESTAMP WITH TIME ZONE,
    duration_minutes INTEGER GENERATED ALWAYS AS (
        CASE 
            WHEN ended_at IS NOT NULL THEN 
                EXTRACT(EPOCH FROM (ended_at - started_at)) / 60
            ELSE NULL
        END
    ) STORED,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- <PERSON><PERSON> indexes
CREATE INDEX IF NOT EXISTS idx_time_tracking_repair_id ON public.time_tracking(repair_id);
CREATE INDEX IF NOT EXISTS idx_time_tracking_technician_id ON public.time_tracking(technician_id);
CREATE INDEX IF NOT EXISTS idx_time_tracking_repair_shop_id ON public.time_tracking(repair_shop_id);

-- Enable RLS
ALTER TABLE public.time_tracking ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view time tracking from their repair shops" ON public.time_tracking
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = time_tracking.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage time tracking from their repair shops" ON public.time_tracking
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = time_tracking.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

-- Create function to handle status changes and time tracking
CREATE OR REPLACE FUNCTION handle_repair_status_change()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    current_tracking_id UUID;
BEGIN
    -- Only track time if there's an assigned technician
    IF NEW.assigned_technician IS NOT NULL THEN
        
        -- End any current active tracking for this repair
        UPDATE public.time_tracking 
        SET ended_at = NOW()
        WHERE repair_id = NEW.id 
        AND ended_at IS NULL;
        
        -- Start new tracking if status is not completed, cancelled, or returned
        IF NEW.status NOT IN ('completed', 'cancelled', 'returned') THEN
            INSERT INTO public.time_tracking (
                repair_id,
                technician_id,
                status_from,
                status_to,
                started_at,
                repair_shop_id
            ) VALUES (
                NEW.id,
                NEW.assigned_technician,
                OLD.status,
                NEW.status,
                NOW(),
                NEW.repair_shop_id
            );
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$;

-- Create trigger for status changes
DROP TRIGGER IF EXISTS repair_status_change_trigger ON public.repairs;
CREATE TRIGGER repair_status_change_trigger
    AFTER UPDATE OF status ON public.repairs
    FOR EACH ROW
    WHEN (OLD.status IS DISTINCT FROM NEW.status)
    EXECUTE FUNCTION handle_repair_status_change();

-- Create function to start initial tracking when technician is assigned
CREATE OR REPLACE FUNCTION handle_technician_assignment()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    -- If technician is being assigned for the first time
    IF OLD.assigned_technician IS NULL AND NEW.assigned_technician IS NOT NULL THEN
        INSERT INTO public.time_tracking (
            repair_id,
            technician_id,
            status_from,
            status_to,
            started_at,
            repair_shop_id
        ) VALUES (
            NEW.id,
            NEW.assigned_technician,
            NULL,
            NEW.status,
            NOW(),
            NEW.repair_shop_id
        );
    END IF;
    
    RETURN NEW;
END;
$$;

-- Create trigger for technician assignment
DROP TRIGGER IF EXISTS technician_assignment_trigger ON public.repairs;
CREATE TRIGGER technician_assignment_trigger
    AFTER UPDATE OF assigned_technician ON public.repairs
    FOR EACH ROW
    WHEN (OLD.assigned_technician IS DISTINCT FROM NEW.assigned_technician)
    EXECUTE FUNCTION handle_technician_assignment();

COMMIT;