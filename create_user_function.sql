-- Create function to create users and assign roles (bypasses RLS)
CREATE OR <PERSON><PERSON>LACE FUNCTION create_user_with_role(
  user_email TEXT,
  user_password TEXT,
  user_role TEXT,
  shop_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id UUID;
  existing_user_id UUID;
  result JSON;
BEGIN
  -- Check if user already exists
  SELECT id INTO existing_user_id FROM auth.users WHERE email = user_email;
  
  IF existing_user_id IS NOT NULL THEN
    -- User exists, just add to shop if not already there
    INSERT INTO public.user_repair_shops (
      user_id,
      repair_shop_id,
      role
    ) VALUES (
      existing_user_id,
      shop_id,
      user_role
    )
    ON CONFLICT (user_id, repair_shop_id) DO UPDATE SET
      role = EXCLUDED.role;
    
    result := json_build_object(
      'success', true,
      'user_id', existing_user_id,
      'email', user_email,
      'message', 'User already existed, added to shop'
    );
    
    RETURN result;
  END IF;
  -- Create the user in auth.users
  INSERT INTO auth.users (
    instance_id,
    id,
    aud,
    role,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
  ) VALUES (
    '00000000-0000-0000-0000-000000000000',
    gen_random_uuid(),
    'authenticated',
    'authenticated',
    user_email,
    crypt(user_password, gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
  )
  RETURNING id INTO new_user_id;

  -- Create identity record
  INSERT INTO auth.identities (
    id,
    user_id,
    identity_data,
    provider,
    provider_id,
    created_at,
    updated_at
  ) VALUES (
    gen_random_uuid(),
    new_user_id,
    format('{"sub":"%s","email":"%s"}', new_user_id::text, user_email)::jsonb,
    'email',
    new_user_id::text,
    NOW(),
    NOW()
  );

  -- Create user-shop association (bypasses RLS)
  INSERT INTO public.user_repair_shops (
    user_id,
    repair_shop_id,
    role
  ) VALUES (
    new_user_id,
    shop_id,
    user_role
  )
  ON CONFLICT (user_id, repair_shop_id) DO UPDATE SET
    role = EXCLUDED.role;

  -- Return success with user ID
  result := json_build_object(
    'success', true,
    'user_id', new_user_id,
    'email', user_email
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  -- Return error
  result := json_build_object(
    'success', false,
    'error', SQLERRM
  );
  RETURN result;
END;
$$;