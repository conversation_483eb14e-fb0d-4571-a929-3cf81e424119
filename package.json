{"name": "fix-it-qr", "productName": "<PERSON><PERSON><PERSON><PERSON>", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:dev": "vite build --mode development", "lint": "eslint .", "preview": "vite preview", "serve": "node serve.js", "electron:dev": "concurrently \"cross-env BROWSER=none npm run dev\" \"wait-on http://localhost:8081 && cross-env ELECTRON_START_URL=http://localhost:8081 electron .\"", "electron:build": "npm run build && electron-builder", "electron:build:linux": "npm run build && electron-builder --linux", "electron:build:win": "npm run build && electron-builder --win", "electron:build:mac": "npm run build && electron-builder --mac", "electron:build:all": "npm run build && electron-builder -mwl", "electron:preview": "electron ."}, "author": "<PERSON><PERSON> <<EMAIL>> (https://github.com/kbbahaPro)", "main": "electron.js", "build": {"appId": "com.bahakaabaoui.phonerepair", "productName": "<PERSON><PERSON><PERSON><PERSON>", "icon": "public/icon", "files": ["dist/**/*", "electron.js", "gate.html", "gate-renderer.js", "preload.js", "gate.css", "index.css", "node_modules/**/*"], "directories": {"buildResources": "assets", "output": "release"}, "linux": {"target": ["AppImage", "deb"], "icon": "public/icon.png"}, "win": {"target": "nsis", "signAndEditExecutable": true, "requestedExecutionLevel": "requireAdministrator"}, "mac": {"target": ["dmg"]}}, "dependencies": {"@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.1", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.4", "@supabase/supabase-js": "^2.49.4", "@tanstack/react-query": "^5.56.2", "@types/file-saver": "^2.0.7", "@types/papaparse": "^5.3.15", "@types/uuid": "^10.0.0", "@yudiel/react-qr-scanner": "^2.2.1", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.0.0", "compression": "^1.7.4", "crypto": "^1.0.1", "date-fns": "^3.6.0", "electron-is-dev": "^3.0.1", "embla-carousel-react": "^8.3.0", "express": "^4.18.2", "helmet": "^7.1.0", "html5-qrcode": "^2.3.8", "i18next": "^22.5.1", "i18next-browser-languagedetector": "^8.0.4", "input-otp": "^1.2.4", "jsbarcode": "^3.11.6", "lucide-react": "^0.462.0", "next-themes": "^0.3.0", "node-fetch": "^2.7.0", "node-machine-id": "^1.1.12", "papaparse": "^5.5.2", "qrcode": "^1.5.4", "react": "^18.3.1", "react-day-picker": "^8.10.1", "react-dom": "^18.3.1", "react-grid-layout": "^1.5.1", "react-hook-form": "^7.53.0", "react-i18next": "^12.3.1", "react-resizable-panels": "^2.1.3", "react-router-dom": "^6.26.2", "react-to-print": "^3.1.0", "recharts": "^2.15.3", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "uuid": "^11.1.0", "vaul": "^0.9.3", "zod": "^3.23.8"}, "devDependencies": {"@eslint/js": "^9.9.0", "@tailwindcss/typography": "^0.5.15", "@types/node": "^22.5.5", "@types/react": "^18.3.3", "@types/react-dom": "^18.3.0", "@vitejs/plugin-react-swc": "^3.5.0", "autoprefixer": "^10.4.20", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "electron": "^35.1.2", "electron-builder": "^26.0.12", "eslint": "^9.9.0", "eslint-plugin-react-hooks": "^5.1.0-rc.0", "eslint-plugin-react-refresh": "^0.4.9", "globals": "^15.9.0", "lovable-tagger": "^1.1.7", "postcss": "^8.4.47", "tailwindcss": "^3.4.11", "typescript": "^5.5.3", "typescript-eslint": "^8.0.1", "vite": "^5.4.1", "wait-on": "^8.0.3"}}