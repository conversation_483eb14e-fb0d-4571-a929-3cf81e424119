# Premium Plan Features

This document outlines the premium features available in the Repair QR Ninja application.

## 1. Role-Based Access Control (RBAC)

Assign appropriate access levels to each team member:

- **Available Roles**: Administrator, Technician, Receptionist, Cashier
- **Data Visibility Restrictions**: Each role has specific permissions
- **Action Logging**: All user actions are tracked in the system
- **Simple User Management Interface**: Easy to add, remove, and modify user roles

### Role Permissions

| Permission | Administrator | Technician | Receptionist | Cashier |
|------------|--------------|------------|--------------|---------|
| Manage Users | ✅ | ❌ | ❌ | ❌ |
| Manage Repairs | ✅ | ✅ | ✅ | ❌ |
| Manage Stock | ✅ | ❌ | ❌ | ❌ |
| View Stock | ✅ | ✅ | ✅ | ✅ |
| View Reports | ✅ | ❌ | ✅ | ✅ |
| Process Payments | ✅ | ❌ | ❌ | ✅ |
| Track Time | ✅ | ✅ | ❌ | ❌ |
| Manage Settings | ✅ | ❌ | ❌ | ❌ |

## 2. Technician Time Tracking

Measure and improve your technical team's efficiency:

- **Repair Time Recording**: Track time spent on each repair
- **Technician Clock In/Out**: Record when technicians start and end their work
- **Productivity Dashboard**: View performance metrics
- **Report Export**: Export time reports for analysis

## 3. Advanced Stock Management

Track your spare parts with precision:

- **Stock Cards**: Detailed information including reference, price, quantity
- **Automatic Parts-Repair Association**: Link parts to specific repairs
- **Low Stock Alerts**: Get notified when inventory is running low
- **Simplified Reordering**: Track and manage restock orders

## 4. Customizable Intake Forms

Collect exactly the information you need:

- **Custom Field Creation**: Create text fields, checkboxes, dropdown menus
- **Device-Specific Forms**: Different forms for different device types
- **Response Storage**: All responses are saved with the customer ticket
- **Data Export**: Export form data on demand

## Setup Instructions

### Database Setup

Run the following SQL script to set up the premium features:

```sql
-- Run the premium features migration script
psql -U your_username -d your_database -f add_premium_features.sql
```

### Accessing Premium Features

1. Log in as an administrator
2. Click on the "Premium" dropdown in the navigation bar
3. Select the feature you want to access

## User Management

To manage users and their roles:

1. Go to "Premium" > "User Management"
2. Add new users by clicking "Invite User"
3. Assign appropriate roles to each user
4. Monitor user activity in the "Activity Log" tab

## Time Tracking

To track technician time:

1. Go to "Premium" > "Time Tracking"
2. Start tracking time by selecting a repair and clicking "Start Working"
3. End tracking by clicking "Stop Working"
4. Generate reports by selecting a date range and clicking "Generate Report"

## Advanced Stock Management

To use advanced stock features:

1. Go to "Premium" > "Advanced Stock"
2. Add products and categories
3. Record stock movements (in/out/adjustments)
4. Monitor low stock alerts

## Custom Forms

To create and manage custom forms:

1. Go to "Premium" > "Custom Forms"
2. Create form templates for different device types
3. Add custom fields with various types (text, checkbox, dropdown, etc.)
4. View and export form responses