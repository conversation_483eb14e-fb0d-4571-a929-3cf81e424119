/*
 * REPAIR QR NINJA - SIMPLIFIED CREATE_USER_WITH_ROLE FUNCTION
 * 
 * This creates a simplified version that works with the existing frontend code.
 * It handles user creation through a more direct approach.
 */

-- Start transaction
BEGIN;

-- Drop existing functions
DROP FUNCTION IF EXISTS public.create_user_with_role(UUID, TEXT, TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS public.invite_user_to_shop(UUID, TEXT, TEXT, TEXT);
DROP FUNCTION IF EXISTS public.complete_user_creation(UUID, UUID, TEXT, TEXT);

-- Create the simplified function that matches your frontend expectations
CREATE OR REPLACE FUNCTION public.create_user_with_role(
    shop_id UUID,
    user_email TEXT,
    user_full_name TEXT,
    user_password TEXT,
    user_role TEXT DEFAULT 'technician'
)
RETURNS JSON AS $$
DECLARE
    current_user_role TEXT;
    new_user_id UUID;
    auth_user_data JSON;
    result JSON;
BEGIN
    -- Check if current user is administrator in the specified shop
    SELECT role INTO current_user_role
    FROM public.user_repair_shops
    WHERE user_id = auth.uid() 
    AND repair_shop_id = shop_id;

    IF current_user_role != 'administrator' THEN
        RETURN json_build_object(
            'success', false, 
            'error', 'Only administrators can create users'
        );
    END IF;

    -- Validate role
    IF user_role NOT IN ('administrator', 'technician', 'receptionist', 'cashier') THEN
        RETURN json_build_object(
            'success', false, 
            'error', 'Invalid role specified'
        );
    END IF;

    -- Check if user already exists
    SELECT id INTO new_user_id FROM auth.users WHERE email = user_email;
    
    IF new_user_id IS NOT NULL THEN
        -- User exists, check if already in shop
        IF EXISTS (
            SELECT 1 FROM public.user_repair_shops 
            WHERE user_id = new_user_id AND repair_shop_id = shop_id
        ) THEN
            RETURN json_build_object(
                'success', false,
                'error', 'User with this email already exists in this repair shop'
            );
        ELSE
            -- Add existing user to shop
            INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role, full_name)
            VALUES (new_user_id, shop_id, user_role, user_full_name);
            
            RETURN json_build_object(
                'success', true,
                'message', 'Existing user added to repair shop successfully',
                'user_id', new_user_id
            );
        END IF;
    END IF;

    -- For new users, we'll use a different approach
    -- Create a temporary record that the frontend can use to create the auth user
    -- Then call a completion function
    
    -- Generate a UUID for the new user
    new_user_id := gen_random_uuid();
    
    -- Return success with instructions for frontend
    RETURN json_build_object(
        'success', true,
        'message', 'User creation initiated',
        'user_id', new_user_id,
        'action', 'create_auth_user',
        'user_data', json_build_object(
            'email', user_email,
            'password', user_password,
            'full_name', user_full_name,
            'role', user_role,
            'shop_id', shop_id,
            'temp_user_id', new_user_id
        )
    );

EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'An error occurred: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to complete user creation after auth user is created
CREATE OR REPLACE FUNCTION public.complete_user_creation(
    auth_user_id UUID,
    shop_id UUID,
    user_full_name TEXT,
    user_role TEXT
)
RETURNS JSON AS $$
BEGIN
    -- Add user to repair shop
    INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role, full_name)
    VALUES (auth_user_id, shop_id, user_role, user_full_name)
    ON CONFLICT (user_id, repair_shop_id) DO UPDATE SET
        role = EXCLUDED.role,
        full_name = EXCLUDED.full_name,
        updated_at = NOW();

    RETURN json_build_object(
        'success', true,
        'message', 'User successfully added to repair shop',
        'user_id', auth_user_id
    );

EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'An error occurred: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Alternative: Create a function that actually creates the user using auth.users
-- This is a more direct approach but requires proper permissions
CREATE OR REPLACE FUNCTION public.create_user_with_auth(
    shop_id UUID,
    user_email TEXT,
    user_full_name TEXT,
    user_password TEXT,
    user_role TEXT DEFAULT 'technician'
)
RETURNS JSON AS $$
DECLARE
    current_user_role TEXT;
    new_user_id UUID;
    result JSON;
BEGIN
    -- Check if current user is administrator
    SELECT role INTO current_user_role
    FROM public.user_repair_shops
    WHERE user_id = auth.uid() 
    AND repair_shop_id = shop_id;

    IF current_user_role != 'administrator' THEN
        RETURN json_build_object(
            'success', false, 
            'error', 'Only administrators can create users'
        );
    END IF;

    -- Validate role
    IF user_role NOT IN ('administrator', 'technician', 'receptionist', 'cashier') THEN
        RETURN json_build_object(
            'success', false, 
            'error', 'Invalid role specified'
        );
    END IF;

    -- Check if user already exists
    SELECT id INTO new_user_id FROM auth.users WHERE email = user_email;
    
    IF new_user_id IS NOT NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User with this email already exists'
        );
    END IF;

    -- Generate new user ID
    new_user_id := gen_random_uuid();

    -- Insert into auth.users (this requires proper RLS policies)
    INSERT INTO auth.users (
        id,
        email,
        encrypted_password,
        email_confirmed_at,
        created_at,
        updated_at,
        confirmation_token,
        email_change_token_new,
        recovery_token
    ) VALUES (
        new_user_id,
        user_email,
        crypt(user_password, gen_salt('bf')),
        NOW(),
        NOW(),
        NOW(),
        '',
        '',
        ''
    );

    -- Add user to repair shop
    INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role, full_name)
    VALUES (new_user_id, shop_id, user_role, user_full_name);

    RETURN json_build_object(
        'success', true,
        'message', 'User created successfully',
        'user_id', new_user_id
    );

EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'An error occurred: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Commit the changes
COMMIT;

SELECT 'Simplified user creation functions added!' as status;
