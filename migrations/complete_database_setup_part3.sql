/*
 * REPAIR QR NINJA - COMPLETE DATABASE SETUP (PART 3)
 * 
 * This is the final part of the consolidated migration file.
 * Run this after complete_database_setup_part2.sql
 * 
 * This part includes:
 * - User Management Functions
 * - Sample Data Population
 * - Real-time Configuration
 * - Final Setup and Optimization
 */

-- Continue transaction from previous parts
-- Note: If running separately, uncomment the BEGIN statement
-- BEGIN;

-- =============================================
-- USER MANAGEMENT FUNCTIONS
-- =============================================

-- Function to associate existing users with the default repair shop
CREATE OR REPLACE FUNCTION associate_users_with_shop() RETURNS void AS $$
DECLARE
    default_shop_id UUID;
    user_record RECORD;
BEGIN
    SELECT id INTO default_shop_id FROM public.repair_shops LIMIT 1;
    FOR user_record IN SELECT id FROM auth.users LOOP
        IF NOT EXISTS (
            SELECT 1 FROM public.user_repair_shops WHERE user_id = user_record.id
        ) THEN
            INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role)
            VALUES (user_record.id, default_shop_id, 'technician');
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically associate new users with the default repair shop
CREATE OR REPLACE FUNCTION associate_new_user_with_shop()
RETURNS TRIGGER AS $$
DECLARE
    default_shop_id UUID;
BEGIN
    SELECT id INTO default_shop_id FROM public.repair_shops LIMIT 1;
    INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role)
    VALUES (NEW.id, default_shop_id, 'technician');
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to check and fix user roles (maintenance function)
CREATE OR REPLACE FUNCTION check_and_fix_user_role()
RETURNS JSON AS $$
DECLARE
    user_count INTEGER;
    admin_count INTEGER;
    default_shop_id UUID;
    result JSON;
BEGIN
    SELECT id INTO default_shop_id FROM public.repair_shops LIMIT 1;
    
    SELECT COUNT(*) INTO user_count
    FROM public.user_repair_shops WHERE repair_shop_id = default_shop_id;
    
    SELECT COUNT(*) INTO admin_count
    FROM public.user_repair_shops
    WHERE repair_shop_id = default_shop_id AND role = 'administrator';
    
    IF admin_count = 0 AND user_count > 0 THEN
        UPDATE public.user_repair_shops
        SET role = 'administrator', updated_at = NOW()
        WHERE repair_shop_id = default_shop_id
        AND user_id = (
            SELECT user_id FROM public.user_repair_shops 
            WHERE repair_shop_id = default_shop_id 
            ORDER BY created_at ASC LIMIT 1
        );
        
        result := json_build_object(
            'success', true,
            'message', 'Promoted first user to administrator',
            'total_users', user_count,
            'admin_count', 1
        );
    ELSE
        result := json_build_object(
            'success', true,
            'message', 'User roles are properly configured',
            'total_users', user_count,
            'admin_count', admin_count
        );
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get technician time tracking data
CREATE OR REPLACE FUNCTION get_technician_time_tracking(
    p_user_id UUID,
    p_repair_shop_id UUID,
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    repair_id UUID,
    customer_name TEXT,
    phone_model TEXT,
    total_time_minutes INTEGER,
    in_progress_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        r.id as repair_id,
        r.customer_name,
        r.phone_model,
        CASE 
            WHEN r.completed_at IS NOT NULL AND r.in_progress_at IS NOT NULL THEN
                EXTRACT(EPOCH FROM (r.completed_at - r.in_progress_at))::INTEGER / 60
            ELSE NULL
        END as total_time_minutes,
        r.in_progress_at,
        r.completed_at
    FROM public.repairs r
    WHERE r.assigned_technician = p_user_id
        AND r.repair_shop_id = p_repair_shop_id
        AND (p_start_date IS NULL OR DATE(r.created_at) >= p_start_date)
        AND (p_end_date IS NULL OR DATE(r.created_at) <= p_end_date)
        AND r.in_progress_at IS NOT NULL
    ORDER BY r.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- Function to clean up old action logs (keep last 90 days)
CREATE OR REPLACE FUNCTION cleanup_old_action_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.user_action_logs
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- CREATE TRIGGERS FOR USER MANAGEMENT
-- =============================================

-- Create the trigger for new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION associate_new_user_with_shop();

-- =============================================
-- INITIAL DATA SETUP
-- =============================================

-- Create a default repair shop
INSERT INTO public.repair_shops (name, address, phone)
VALUES ('Your Repair Shop', 'Your Address', 'Your Phone Number')
ON CONFLICT DO NOTHING;

-- Associate existing users with the default repair shop
SELECT associate_users_with_shop();

-- =============================================
-- SAMPLE DATA POPULATION
-- =============================================

-- Insert sample product categories
INSERT INTO public.product_categories (name, description, icon, color, repair_shop_id)
SELECT 
    'Phone Screens', 
    'Replacement screens for various phone models', 
    'smartphone', 
    '#3B82F6',
    rs.id
FROM public.repair_shops rs
WHERE NOT EXISTS (
    SELECT 1 FROM public.product_categories 
    WHERE name = 'Phone Screens' AND repair_shop_id = rs.id
);

INSERT INTO public.product_categories (name, description, icon, color, repair_shop_id)
SELECT 
    'Batteries', 
    'Replacement batteries for phones and tablets', 
    'battery', 
    '#10B981',
    rs.id
FROM public.repair_shops rs
WHERE NOT EXISTS (
    SELECT 1 FROM public.product_categories 
    WHERE name = 'Batteries' AND repair_shop_id = rs.id
);

INSERT INTO public.product_categories (name, description, icon, color, repair_shop_id)
SELECT 
    'Accessories', 
    'Phone cases, chargers, and other accessories', 
    'cable', 
    '#F59E0B',
    rs.id
FROM public.repair_shops rs
WHERE NOT EXISTS (
    SELECT 1 FROM public.product_categories 
    WHERE name = 'Accessories' AND repair_shop_id = rs.id
);

-- Insert sample products
INSERT INTO public.products (name, description, sku, category_id, price, cost, stock_quantity, min_stock_level, repair_shop_id)
SELECT 
    'iPhone 12 Screen', 
    'Original quality replacement screen for iPhone 12', 
    'IP12-SCR-001',
    pc.id,
    150.00,
    80.00,
    10,
    5,
    rs.id
FROM public.repair_shops rs
JOIN public.product_categories pc ON pc.repair_shop_id = rs.id
WHERE pc.name = 'Phone Screens'
AND NOT EXISTS (
    SELECT 1 FROM public.products 
    WHERE sku = 'IP12-SCR-001' AND repair_shop_id = rs.id
);

INSERT INTO public.products (name, description, sku, category_id, price, cost, stock_quantity, min_stock_level, repair_shop_id)
SELECT 
    'Samsung Galaxy S21 Battery', 
    'High capacity replacement battery for Galaxy S21', 
    'SGS21-BAT-001',
    pc.id,
    45.00,
    25.00,
    15,
    8,
    rs.id
FROM public.repair_shops rs
JOIN public.product_categories pc ON pc.repair_shop_id = rs.id
WHERE pc.name = 'Batteries'
AND NOT EXISTS (
    SELECT 1 FROM public.products 
    WHERE sku = 'SGS21-BAT-001' AND repair_shop_id = rs.id
);

-- Insert sample form template for phone intake
INSERT INTO public.form_templates (name, description, device_type, fields, repair_shop_id)
SELECT 
    'Phone Intake Form',
    'Standard intake form for phone repairs',
    'phone',
    '[
        {
            "id": "device_condition",
            "type": "select",
            "label": "Device Condition",
            "required": true,
            "options": ["Excellent", "Good", "Fair", "Poor", "Damaged"]
        },
        {
            "id": "accessories_included",
            "type": "checkbox",
            "label": "Accessories Included",
            "options": ["Charger", "Case", "Screen Protector", "Earphones"]
        },
        {
            "id": "passcode_provided",
            "type": "radio",
            "label": "Passcode Provided",
            "required": true,
            "options": ["Yes", "No"]
        },
        {
            "id": "backup_status",
            "type": "radio",
            "label": "Data Backup Status",
            "required": true,
            "options": ["Backed up", "Not backed up", "Customer declined"]
        },
        {
            "id": "special_instructions",
            "type": "textarea",
            "label": "Special Instructions",
            "placeholder": "Any special handling instructions..."
        }
    ]'::jsonb,
    rs.id
FROM public.repair_shops rs
WHERE NOT EXISTS (
    SELECT 1 FROM public.form_templates 
    WHERE name = 'Phone Intake Form' AND repair_shop_id = rs.id
);

-- =============================================
-- REAL-TIME FUNCTIONALITY
-- =============================================

-- Drop existing publication if it exists
DROP PUBLICATION IF EXISTS supabase_realtime;

-- Create publication that tracks all changes to all tables
CREATE PUBLICATION supabase_realtime FOR TABLE 
    public.repairs,
    public.repair_shops,
    public.user_repair_shops,
    public.product_categories,
    public.products,
    public.sales,
    public.sale_items,
    public.stock_movements,
    public.one_time_items,
    public.repair_stock_items,
    public.technician_time_entries,
    public.form_templates,
    public.form_responses,
    public.user_action_logs,
    public.invoices,
    public.invoice_items;

-- =============================================
-- FINAL SYSTEM SETUP
-- =============================================

-- Ensure all existing repairs have ticket numbers
DO $$
DECLARE
    repair_record RECORD;
BEGIN
    FOR repair_record IN 
        SELECT id FROM public.repairs 
        WHERE ticket_number IS NULL
        ORDER BY created_at ASC
    LOOP
        UPDATE public.repairs 
        SET ticket_number = nextval('repair_ticket_number_seq')
        WHERE id = repair_record.id;
    END LOOP;
END $$;

-- Run user role check and fix
SELECT check_and_fix_user_role();

-- Create initial action log entry
INSERT INTO public.user_action_logs (user_id, action, details)
VALUES (
    NULL,
    'system_initialized',
    json_build_object(
        'timestamp', NOW(),
        'migration_version', 'complete_setup',
        'message', 'Database fully initialized with all premium features'
    )
);

-- =============================================
-- PERFORMANCE OPTIMIZATION
-- =============================================

-- Analyze all tables for better query planning
ANALYZE public.repairs;
ANALYZE public.repair_shops;
ANALYZE public.user_repair_shops;
ANALYZE public.products;
ANALYZE public.product_categories;
ANALYZE public.sales;
ANALYZE public.sale_items;
ANALYZE public.stock_movements;
ANALYZE public.technician_time_entries;
ANALYZE public.form_templates;
ANALYZE public.form_responses;
ANALYZE public.user_action_logs;
ANALYZE public.invoices;
ANALYZE public.invoice_items;

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;

-- =============================================
-- SETUP COMPLETE MESSAGE
-- =============================================

/*
 * 🎉 REPAIR QR NINJA DATABASE SETUP COMPLETE! 🎉
 * 
 * Your premium repair shop management system is now ready with:
 * 
 * ✅ Complete repair management system
 * ✅ Multi-user support with role-based access control
 * ✅ Advanced stock management and POS system
 * ✅ Technician time tracking
 * ✅ Customizable intake forms
 * ✅ Professional invoice generation
 * ✅ Complete audit trail and action logging
 * ✅ Real-time data synchronization
 * ✅ Sample data and templates
 * ✅ Optimized performance and security
 * 
 * Next Steps:
 * 1. Update the default repair shop information with your details
 * 2. Create your first administrator user through Supabase Auth
 * 3. Configure your product catalog and categories
 * 4. Set up email templates for customer notifications
 * 5. Configure your application environment variables
 * 
 * Your repair shop management system is ready to serve customers! 🔧📱
 */
