/*
 * REPAIR QR NINJA - ROW LEVEL SECURITY SETUP
 * Migration 002: Security Policies and Access Control
 * 
 * This migration sets up Row Level Security (RLS) policies to ensure proper data access control.
 * Users can only access data from repair shops they belong to.
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

-- Enable RLS on all tables
ALTER TABLE public.repair_shops ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_repair_shops ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.repairs ENABLE ROW LEVEL SECURITY;

-- =============================================
-- REPAIR SHOPS TABLE POLICIES
-- =============================================

-- Users can view repair shops they belong to
CREATE POLICY "Users can view repair shops they belong to" ON public.repair_shops
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repair_shops.id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

-- Administrators can manage repair shops
CREATE POLICY "Administrators can manage repair shops" ON public.repair_shops
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repair_shops.id
            AND user_repair_shops.user_id = auth.uid()
            AND user_repair_shops.role = 'administrator'
        )
    );

-- =============================================
-- USER-REPAIR SHOPS JUNCTION TABLE POLICIES
-- =============================================

-- Users can view their own repair shop associations
CREATE POLICY "Users can view their own repair shop associations" ON public.user_repair_shops
    FOR SELECT
    USING (user_id = auth.uid());

-- Administrators can view all associations in their repair shops
CREATE POLICY "Administrators can view repair shop associations" ON public.user_repair_shops
    FOR SELECT
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
            AND role = 'administrator'
        )
    );

-- Administrators can manage user-repair shop associations
CREATE POLICY "Administrators can manage user-repair shop associations" ON public.user_repair_shops
    FOR ALL
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
            AND role = 'administrator'
        )
    );

-- =============================================
-- REPAIRS TABLE POLICIES
-- =============================================

-- Users can view repairs from their repair shops
CREATE POLICY "Users can view repairs from their repair shops" ON public.repairs
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repairs.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

-- Users can insert repairs for their repair shops
CREATE POLICY "Users can insert repairs for their repair shops" ON public.repairs
    FOR INSERT
    WITH CHECK (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repairs.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

-- Users can update repairs from their repair shops
CREATE POLICY "Users can update repairs from their repair shops" ON public.repairs
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repairs.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

-- Users can delete repairs from their repair shops
CREATE POLICY "Users can delete repairs from their repair shops" ON public.repairs
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repairs.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

-- =============================================
-- USER MANAGEMENT FUNCTIONS
-- =============================================

-- Function to associate existing users with the default repair shop
CREATE OR REPLACE FUNCTION associate_users_with_shop() RETURNS void AS $$
DECLARE
    default_shop_id UUID;
    user_record RECORD;
BEGIN
    -- Get the ID of the default repair shop
    SELECT id INTO default_shop_id FROM public.repair_shops LIMIT 1;

    -- For each user in auth.users
    FOR user_record IN SELECT id FROM auth.users LOOP
        -- Check if the user is already associated with a repair shop
        IF NOT EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_id = user_record.id
        ) THEN
            -- Associate the user with the default repair shop as a technician
            INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role)
            VALUES (user_record.id, default_shop_id, 'technician');
        END IF;
    END LOOP;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically associate new users with the default repair shop
CREATE OR REPLACE FUNCTION associate_new_user_with_shop()
RETURNS TRIGGER AS $$
DECLARE
    default_shop_id UUID;
BEGIN
    -- Get the ID of the default repair shop
    SELECT id INTO default_shop_id FROM public.repair_shops LIMIT 1;

    -- Associate the new user with the default repair shop
    INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role)
    VALUES (NEW.id, default_shop_id, 'technician');

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create the trigger for new users
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
    AFTER INSERT ON auth.users
    FOR EACH ROW
    EXECUTE FUNCTION associate_new_user_with_shop();

-- Associate existing users with the default repair shop
SELECT associate_users_with_shop();

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;
