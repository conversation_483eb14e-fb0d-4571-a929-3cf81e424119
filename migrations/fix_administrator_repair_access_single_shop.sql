/*
 * REPAIR QR NINJA - FIX ADMINISTRATOR REPAIR ACCESS (SINGLE SHOP)
 * 
 * This migration fixes the issue where administrators cannot see all repairs
 * in a single repair shop setup. The problem was caused by overly restrictive 
 * RLS policies that only allowed users to see repairs they created or were assigned to.
 * 
 * For a single repair shop, we can simplify the policies significantly:
 * - Administrators can see ALL repairs
 * - Other roles can see repairs they created or are assigned to
 * - Technicians can use the "show all tickets" toggle
 */

-- Start transaction
BEGIN;

-- =============================================
-- DROP EXISTING RESTRICTIVE REPAIR POLICIES
-- =============================================

-- Drop the overly restrictive policy that only allows users to see their own repairs
DROP POLICY IF EXISTS "Users can view their own repairs" ON public.repairs;
DROP POLICY IF EXISTS "Users can insert repairs" ON public.repairs;
DROP POLICY IF EXISTS "Users can update their own repairs" ON public.repairs;
DROP POLICY IF EXISTS "Users can delete their own repairs" ON public.repairs;

-- Also drop any shop-based policies that might exist
DROP POLICY IF EXISTS "Users can view repairs from their repair shops" ON public.repairs;
DROP POLICY IF EXISTS "Users can insert repairs for their repair shops" ON public.repairs;
DROP POLICY IF EXISTS "Users can update repairs from their repair shops" ON public.repairs;
DROP POLICY IF EXISTS "Users can delete repairs from their repair shops" ON public.repairs;

-- =============================================
-- CREATE SIMPLIFIED SINGLE-SHOP POLICIES
-- =============================================

-- For a single repair shop, we can use role-based access directly
-- Administrators can see all repairs, others see their own/assigned repairs
CREATE POLICY "Role-based repair access" ON public.repairs
    FOR SELECT
    USING (
        -- Administrators can see all repairs
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.user_id = auth.uid()
            AND user_repair_shops.role = 'administrator'
        )
        OR
        -- Non-administrators can see repairs they created or are assigned to
        (
            user_id = auth.uid() OR assigned_technician = auth.uid()
        )
    );

-- All authenticated users can insert repairs (they'll be filtered by the app)
CREATE POLICY "Users can insert repairs" ON public.repairs
    FOR INSERT
    WITH CHECK (auth.uid() IS NOT NULL);

-- Role-based update access
CREATE POLICY "Role-based repair updates" ON public.repairs
    FOR UPDATE
    USING (
        -- Administrators can update any repair
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.user_id = auth.uid()
            AND user_repair_shops.role = 'administrator'
        )
        OR
        -- Others can update repairs they created or are assigned to
        (
            user_id = auth.uid() OR assigned_technician = auth.uid()
        )
    );

-- Role-based delete access
CREATE POLICY "Role-based repair deletes" ON public.repairs
    FOR DELETE
    USING (
        -- Administrators can delete any repair
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.user_id = auth.uid()
            AND user_repair_shops.role = 'administrator'
        )
        OR
        -- Others can only delete repairs they created
        user_id = auth.uid()
    );

-- =============================================
-- COMMIT CHANGES
-- =============================================

COMMIT;

-- =============================================
-- VERIFICATION
-- =============================================

SELECT 'Single shop administrator repair access policies fixed successfully!' as status;

/*
 * NOTES FOR SINGLE REPAIR SHOP SETUP:
 * 
 * 1. Administrators can now see ALL repairs regardless of who created them
 * 2. Technicians/other roles see only repairs they created or are assigned to
 * 3. The application-level filtering in RepairContext.tsx will still work for technicians
 * 4. The "show all tickets" toggle will work as expected for technicians
 * 5. Much simpler than multi-shop setup since we don't need to check repair_shop_id
 */
