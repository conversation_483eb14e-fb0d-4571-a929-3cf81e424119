# 🚀 Repair QR Ninja - Database Migration Guide

This guide will help you set up the complete Repair QR Ninja database with all premium features.

## 📋 Prerequisites

- ✅ Supabase project created
- ✅ Database access (SQL Editor or PostgreSQL client)
- ✅ Admin privileges on the database

## 🎯 Quick Setup Options

### Option 1: One-Click Complete Setup (Recommended)

**For Supabase SQL Editor:**
1. Open your Supabase project dashboard
2. Go to SQL Editor
3. Copy and paste the content of `run_complete_setup.sql`
4. Click "Run" and wait for completion

**For PostgreSQL Client:**
```bash
psql -h your-db-host -U postgres -d your-database -f run_complete_setup.sql
```

### Option 2: Three-Part Setup

Run these files in sequence in your SQL Editor:

1. `complete_database_setup.sql` - Core structure
2. `complete_database_setup_part2.sql` - Security policies  
3. `complete_database_setup_part3.sql` - Final setup

### Option 3: Step-by-Step Migration

For development or detailed control, run individual migrations:

```sql
-- Run in sequence
\i 001_initial_setup.sql
\i 002_row_level_security.sql
\i 003_stock_management.sql
\i 004_stock_security_policies.sql
\i 005_premium_features.sql
\i 006_premium_security_policies.sql
\i 007_user_management_functions.sql
\i 008_realtime_and_final_setup.sql
```

## 🔧 What Gets Created

### Core Tables
- `repair_shops` - Your repair shop information
- `user_repair_shops` - User roles and permissions
- `repairs` - Main repair tracking with all features
- `technician_time_entries` - Time tracking data

### Stock Management
- `product_categories` - Product organization
- `products` - Your inventory catalog
- `sales` - POS sales records
- `sale_items` - Sale line items
- `stock_movements` - Inventory tracking
- `one_time_items` - Unique devices
- `repair_stock_items` - Parts used in repairs

### Premium Features
- `form_templates` - Customizable intake forms
- `form_responses` - Customer form data
- `invoices` - Professional invoicing
- `invoice_items` - Invoice line items
- `user_action_logs` - Complete audit trail

### Sample Data
- Default repair shop setup
- Sample product categories (Screens, Batteries, Accessories)
- Sample products with pricing
- Phone intake form template

## 🛡️ Security Features

- **Row Level Security (RLS)** on all tables
- **Role-based access control** (Administrator, Technician, Receptionist, Cashier)
- **Data isolation** between repair shops
- **Secure API access** through Supabase policies

## ⚡ Performance Features

- **Optimized indexes** for fast queries
- **Real-time subscriptions** for live updates
- **Efficient triggers** for automation
- **Query optimization** with ANALYZE

## 🔍 Verification

After setup, verify everything is working:

```sql
-- Check tables
SELECT COUNT(*) FROM information_schema.tables 
WHERE table_schema = 'public';
-- Should return 16

-- Check sample data
SELECT * FROM public.product_categories;
-- Should show 3 categories

-- Check RLS
SELECT schemaname, tablename, rowsecurity 
FROM pg_tables 
WHERE schemaname = 'public' AND rowsecurity = true;
-- Should show all tables with RLS enabled

-- Check real-time
SELECT * FROM pg_publication WHERE pubname = 'supabase_realtime';
-- Should show the publication
```

## 🚨 Troubleshooting

### Common Issues

**Permission Denied:**
- Ensure you're running as database owner or superuser
- Check your Supabase project permissions

**Table Already Exists:**
- The migrations use `IF NOT EXISTS` - safe to re-run
- For clean setup, see reset instructions below

**Extension Missing:**
- Ensure `uuid-ossp` extension is available
- Run: `CREATE EXTENSION IF NOT EXISTS "uuid-ossp";`

### Reset Database (Development Only)

⚠️ **WARNING: This deletes ALL data**

```sql
-- Complete reset
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO public;

-- Then run migrations again
```

## 📞 Post-Setup Configuration

### 1. Update Repair Shop Information

```sql
UPDATE public.repair_shops 
SET 
    name = 'Your Actual Shop Name',
    address = 'Your Shop Address',
    phone = 'Your Phone Number'
WHERE name = 'Your Repair Shop';
```

### 2. Create First Admin User

1. Sign up through your Supabase Auth
2. The user will automatically be assigned to your repair shop
3. Run the role fix function:

```sql
SELECT check_and_fix_user_role();
```

### 3. Configure Environment Variables

```env
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

### 4. Set Up Real-time (Optional)

In your application, subscribe to real-time changes:

```javascript
const subscription = supabase
  .channel('repairs')
  .on('postgres_changes', 
    { event: '*', schema: 'public', table: 'repairs' },
    (payload) => console.log('Change received!', payload)
  )
  .subscribe()
```

## 🎉 You're Ready!

Your Repair QR Ninja database is now fully configured with:

- ✅ Complete repair management
- ✅ Multi-user support
- ✅ Stock management & POS
- ✅ Time tracking
- ✅ Custom forms
- ✅ Professional invoicing
- ✅ Real-time updates
- ✅ Complete security

Start managing repairs like a pro! 🔧📱

---

**Need help?** Check the README.md files in each migration for detailed documentation.
