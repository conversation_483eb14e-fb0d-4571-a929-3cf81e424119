/*
 * REPAIR QR NINJA - COMPLETE DATABASE SETUP
 * 
 * This is a consolidated migration file that sets up the complete database structure
 * for the Repair QR Ninja application in a single execution.
 * 
 * Features included:
 * ✅ Core repair management system
 * ✅ User management and RBAC
 * ✅ Stock management and POS
 * ✅ Premium features (time tracking, forms, invoices)
 * ✅ Complete security policies
 * ✅ Real-time functionality
 * ✅ Sample data and templates
 * 
 * Version: Premium Edition
 * Last Updated: 2025-01-07
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- EXTENSIONS AND CONFIGURATION
-- =============================================

-- Enable the uuid-ossp extension for UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- CORE TABLE DEFINITIONS
-- =============================================

-- Create the repair_shops table
CREATE TABLE IF NOT EXISTS public.repair_shops (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    address TEXT,
    phone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create the user_repair_shops junction table
CREATE TABLE IF NOT EXISTS public.user_repair_shops (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('administrator', 'technician', 'receptionist', 'cashier')),
    full_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(user_id, repair_shop_id)
);

-- Create the repairs table with all current fields
CREATE TABLE IF NOT EXISTS public.repairs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_name TEXT NOT NULL,
    customer_phone TEXT NOT NULL,
    customer_email TEXT,
    phone_model TEXT NOT NULL,
    problem_description TEXT NOT NULL,
    repair_price NUMERIC NOT NULL,
    repair_cost NUMERIC DEFAULT 0,
    cost_modifications TEXT DEFAULT '[]',
    payment_status TEXT NOT NULL CHECK (payment_status IN ('paid', 'partial', 'unpaid')),
    down_payment NUMERIC NOT NULL DEFAULT 0,
    status TEXT NOT NULL CHECK (status IN ('pending', 'inProgress', 'completed', 'cancelled', 'returned')),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_shop_id UUID REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    assigned_technician UUID REFERENCES auth.users(id),
    ticket_number INTEGER,
    observations JSONB DEFAULT '[]'::jsonb,
    price_modifications JSONB DEFAULT '[]'::jsonb,
    status_history JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    in_progress_at TIMESTAMP WITH TIME ZONE
);

-- =============================================
-- STOCK MANAGEMENT TABLES
-- =============================================

-- Create product_categories table
CREATE TABLE IF NOT EXISTS public.product_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    color TEXT,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create products table
CREATE TABLE IF NOT EXISTS public.products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    sku TEXT NOT NULL,
    barcode TEXT,
    category_id UUID NOT NULL REFERENCES public.product_categories(id) ON DELETE CASCADE,
    price NUMERIC NOT NULL CHECK (price >= 0),
    cost NUMERIC NOT NULL CHECK (cost >= 0),
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    min_stock_level INTEGER NOT NULL DEFAULT 0 CHECK (min_stock_level >= 0),
    max_stock_level INTEGER CHECK (max_stock_level IS NULL OR max_stock_level >= min_stock_level),
    is_active BOOLEAN NOT NULL DEFAULT true,
    image_url TEXT,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(sku, repair_shop_id),
    UNIQUE(barcode, repair_shop_id)
);

-- Create sales table
CREATE TABLE IF NOT EXISTS public.sales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_number TEXT NOT NULL,
    customer_name TEXT,
    customer_phone TEXT,
    subtotal NUMERIC NOT NULL DEFAULT 0,
    discount_amount NUMERIC NOT NULL DEFAULT 0,
    total_amount NUMERIC NOT NULL,
    payment_method TEXT NOT NULL CHECK (payment_method IN ('cash', 'card', 'transfer', 'check')),
    notes TEXT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(sale_number, repair_shop_id)
);

-- Create sale_items table
CREATE TABLE IF NOT EXISTS public.sale_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_id UUID NOT NULL REFERENCES public.sales(id) ON DELETE CASCADE,
    product_id UUID REFERENCES public.products(id) ON DELETE SET NULL,
    one_time_item_id UUID,
    product_name TEXT NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price NUMERIC NOT NULL CHECK (unit_price >= 0),
    total_price NUMERIC GENERATED ALWAYS AS (quantity * unit_price) STORED,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create stock_movements table for inventory tracking
CREATE TABLE IF NOT EXISTS public.stock_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('in', 'out', 'adjustment')),
    quantity INTEGER NOT NULL,
    reason TEXT NOT NULL,
    reference TEXT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create one_time_items table for unique products
CREATE TABLE IF NOT EXISTS public.one_time_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    serial_number TEXT,
    device_type TEXT,
    condition TEXT CHECK (condition IN ('new', 'used', 'refurbished', 'damaged')),
    purchase_price NUMERIC CHECK (purchase_price >= 0),
    selling_price NUMERIC CHECK (selling_price >= 0),
    is_sold BOOLEAN NOT NULL DEFAULT false,
    sold_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create repair_stock_items table to link repairs with stock items
CREATE TABLE IF NOT EXISTS public.repair_stock_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    repair_id UUID NOT NULL REFERENCES public.repairs(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,3) NOT NULL CHECK (unit_price >= 0),
    total_price DECIMAL(10,3) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- PREMIUM FEATURE TABLES
-- =============================================

-- Create the technician_time_entries table
CREATE TABLE IF NOT EXISTS public.technician_time_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_id UUID REFERENCES public.repairs(id) ON DELETE SET NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create the form_templates table
CREATE TABLE IF NOT EXISTS public.form_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    device_type TEXT NOT NULL,
    fields JSONB NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create the form_responses table
CREATE TABLE IF NOT EXISTS public.form_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repair_id UUID NOT NULL REFERENCES public.repairs(id) ON DELETE CASCADE,
    template_id UUID NOT NULL REFERENCES public.form_templates(id) ON DELETE CASCADE,
    responses JSONB NOT NULL,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create an action log table for traceability
CREATE TABLE IF NOT EXISTS public.user_action_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    details JSONB,
    repair_shop_id UUID REFERENCES public.repair_shops(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create invoices table
CREATE TABLE IF NOT EXISTS public.invoices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    invoice_number TEXT NOT NULL UNIQUE,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    customer_name TEXT NOT NULL,
    customer_phone TEXT,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'paid')),
    notes TEXT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create invoice_items table
CREATE TABLE IF NOT EXISTS public.invoice_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    invoice_id UUID NOT NULL REFERENCES public.invoices(id) ON DELETE CASCADE,
    repair_id UUID NOT NULL REFERENCES public.repairs(id) ON DELETE CASCADE,
    description TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- SEQUENCES AND AUTO-INCREMENT
-- =============================================

-- Create a sequence for ticket numbers starting from 10001
CREATE SEQUENCE IF NOT EXISTS repair_ticket_number_seq START WITH 10001;

-- Create sequence for sale numbers
CREATE SEQUENCE IF NOT EXISTS sale_number_seq START WITH 1;

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Repairs table indexes
CREATE INDEX IF NOT EXISTS idx_repairs_user_id ON public.repairs(user_id);
CREATE INDEX IF NOT EXISTS idx_repairs_repair_shop_id ON public.repairs(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_repairs_status ON public.repairs(status);
CREATE INDEX IF NOT EXISTS idx_repairs_payment_status ON public.repairs(payment_status);
CREATE INDEX IF NOT EXISTS idx_repairs_assigned_technician ON public.repairs(assigned_technician);
CREATE INDEX IF NOT EXISTS idx_repairs_ticket_number ON public.repairs(ticket_number);
CREATE INDEX IF NOT EXISTS idx_repairs_customer_email ON public.repairs(customer_email) WHERE customer_email IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_repairs_created_at ON public.repairs(created_at);
CREATE INDEX IF NOT EXISTS idx_repairs_completed_at ON public.repairs(completed_at);

-- User repair shops indexes
CREATE INDEX IF NOT EXISTS idx_user_repair_shops_user_id ON public.user_repair_shops(user_id);
CREATE INDEX IF NOT EXISTS idx_user_repair_shops_repair_shop_id ON public.user_repair_shops(repair_shop_id);

-- Product categories indexes
CREATE INDEX IF NOT EXISTS idx_product_categories_repair_shop_id ON public.product_categories(repair_shop_id);

-- Products indexes
CREATE INDEX IF NOT EXISTS idx_products_category_id ON public.products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_repair_shop_id ON public.products(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_products_sku ON public.products(sku);
CREATE INDEX IF NOT EXISTS idx_products_barcode ON public.products(barcode) WHERE barcode IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_products_is_active ON public.products(is_active);

-- Sales indexes
CREATE INDEX IF NOT EXISTS idx_sales_repair_shop_id ON public.sales(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_sales_user_id ON public.sales(user_id);
CREATE INDEX IF NOT EXISTS idx_sales_created_at ON public.sales(created_at);
CREATE INDEX IF NOT EXISTS idx_sales_sale_number ON public.sales(sale_number);

-- Sale items indexes
CREATE INDEX IF NOT EXISTS idx_sale_items_sale_id ON public.sale_items(sale_id);
CREATE INDEX IF NOT EXISTS idx_sale_items_product_id ON public.sale_items(product_id);
CREATE INDEX IF NOT EXISTS idx_sale_items_one_time_item_id ON public.sale_items(one_time_item_id);
CREATE INDEX IF NOT EXISTS idx_sale_items_repair_shop_id ON public.sale_items(repair_shop_id);

-- Stock movements indexes
CREATE INDEX IF NOT EXISTS idx_stock_movements_product_id ON public.stock_movements(product_id);
CREATE INDEX IF NOT EXISTS idx_stock_movements_user_id ON public.stock_movements(user_id);
CREATE INDEX IF NOT EXISTS idx_stock_movements_repair_shop_id ON public.stock_movements(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_stock_movements_created_at ON public.stock_movements(created_at);
CREATE INDEX IF NOT EXISTS idx_stock_movements_type ON public.stock_movements(type);

-- One-time items indexes
CREATE INDEX IF NOT EXISTS idx_one_time_items_product_id ON public.one_time_items(product_id);
CREATE INDEX IF NOT EXISTS idx_one_time_items_is_sold ON public.one_time_items(is_sold);
CREATE INDEX IF NOT EXISTS idx_one_time_items_device_type ON public.one_time_items(device_type);
CREATE INDEX IF NOT EXISTS idx_one_time_items_serial_number ON public.one_time_items(serial_number);
CREATE INDEX IF NOT EXISTS idx_one_time_items_repair_shop_id ON public.one_time_items(repair_shop_id);

-- Repair stock items indexes
CREATE INDEX IF NOT EXISTS idx_repair_stock_items_repair_id ON public.repair_stock_items(repair_id);
CREATE INDEX IF NOT EXISTS idx_repair_stock_items_product_id ON public.repair_stock_items(product_id);
CREATE INDEX IF NOT EXISTS idx_repair_stock_items_repair_shop_id ON public.repair_stock_items(repair_shop_id);

-- Time entries indexes
CREATE INDEX IF NOT EXISTS idx_time_entries_user_id ON public.technician_time_entries(user_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_repair_id ON public.technician_time_entries(repair_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_repair_shop_id ON public.technician_time_entries(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_start_time ON public.technician_time_entries(start_time);

-- Form templates indexes
CREATE INDEX IF NOT EXISTS idx_form_templates_repair_shop_id ON public.form_templates(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_form_templates_device_type ON public.form_templates(device_type);

-- Form responses indexes
CREATE INDEX IF NOT EXISTS idx_form_responses_repair_id ON public.form_responses(repair_id);
CREATE INDEX IF NOT EXISTS idx_form_responses_template_id ON public.form_responses(template_id);
CREATE INDEX IF NOT EXISTS idx_form_responses_repair_shop_id ON public.form_responses(repair_shop_id);

-- User action logs indexes
CREATE INDEX IF NOT EXISTS idx_user_action_logs_user_id ON public.user_action_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_action_logs_created_at ON public.user_action_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_user_action_logs_repair_shop_id ON public.user_action_logs(repair_shop_id);

-- Invoices indexes
CREATE INDEX IF NOT EXISTS idx_invoices_repair_shop_id ON public.invoices(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_invoices_created_at ON public.invoices(created_at);
CREATE INDEX IF NOT EXISTS idx_invoices_user_id ON public.invoices(user_id);
CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice_id ON public.invoice_items(invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoice_items_repair_id ON public.invoice_items(repair_id);

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically assign ticket numbers to new repairs
CREATE OR REPLACE FUNCTION assign_ticket_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.ticket_number IS NULL THEN
        NEW.ticket_number := nextval('repair_ticket_number_seq');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to ensure all new repairs have a repair_shop_id
CREATE OR REPLACE FUNCTION ensure_repair_has_shop() RETURNS TRIGGER AS $$
DECLARE
    default_shop_id UUID;
BEGIN
    IF NEW.repair_shop_id IS NULL THEN
        SELECT id INTO default_shop_id FROM public.repair_shops LIMIT 1;
        NEW.repair_shop_id := default_shop_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to generate sale number
CREATE OR REPLACE FUNCTION generate_sale_number()
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    result_number TEXT;
BEGIN
    next_number := nextval('sale_number_seq');
    result_number := 'SALE-' || LPAD(next_number::TEXT, 6, '0');
    RETURN result_number;
END;
$$ LANGUAGE plpgsql;

-- Function to update stock quantity when sale items are added/removed
CREATE OR REPLACE FUNCTION update_stock_on_sale()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        UPDATE public.products
        SET stock_quantity = stock_quantity - NEW.quantity, updated_at = NOW()
        WHERE id = NEW.product_id;

        INSERT INTO public.stock_movements (
            product_id, type, quantity, reason, reference, user_id, repair_shop_id
        )
        SELECT NEW.product_id, 'out', NEW.quantity, 'Sale', NEW.sale_id::TEXT, s.user_id, NEW.repair_shop_id
        FROM public.sales s WHERE s.id = NEW.sale_id;

        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        UPDATE public.products
        SET stock_quantity = stock_quantity + OLD.quantity, updated_at = NOW()
        WHERE id = OLD.product_id;

        INSERT INTO public.stock_movements (
            product_id, type, quantity, reason, reference, user_id, repair_shop_id
        )
        SELECT OLD.product_id, 'in', OLD.quantity, 'Sale Cancelled', OLD.sale_id::TEXT, s.user_id, OLD.repair_shop_id
        FROM public.sales s WHERE s.id = OLD.sale_id;

        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function for forms updated_at timestamp
CREATE OR REPLACE FUNCTION update_form_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to generate invoice number
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    result_number TEXT;
BEGIN
    SELECT COALESCE(MAX(CAST(SUBSTRING(i.invoice_number FROM '[0-9]+$') AS INTEGER)), 0) + 1
    INTO next_number FROM public.invoices i WHERE i.invoice_number ~ '^INV-[0-9]+$';
    result_number := 'INV-' || LPAD(next_number::TEXT, 6, '0');
    RETURN result_number;
END;
$$ LANGUAGE plpgsql;

-- Function to update repair status timestamps
CREATE OR REPLACE FUNCTION update_repair_status_timestamps()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.status = 'inProgress' AND OLD.status != 'inProgress' THEN
        NEW.in_progress_at = NOW();
    END IF;

    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        NEW.completed_at = NOW();
    END IF;

    IF NEW.status != 'completed' AND OLD.status = 'completed' THEN
        NEW.completed_at = NULL;
    END IF;

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- CREATE TRIGGERS
-- =============================================

-- Triggers for updated_at columns
DROP TRIGGER IF EXISTS update_repair_shops_updated_at ON public.repair_shops;
CREATE TRIGGER update_repair_shops_updated_at
    BEFORE UPDATE ON public.repair_shops FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_repairs_updated_at ON public.repairs;
CREATE TRIGGER update_repairs_updated_at
    BEFORE UPDATE ON public.repairs FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_repair_shops_updated_at ON public.user_repair_shops;
CREATE TRIGGER update_user_repair_shops_updated_at
    BEFORE UPDATE ON public.user_repair_shops FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_product_categories_updated_at
    BEFORE UPDATE ON public.product_categories FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_products_updated_at
    BEFORE UPDATE ON public.products FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_sales_updated_at
    BEFORE UPDATE ON public.sales FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_one_time_items_updated_at
    BEFORE UPDATE ON public.one_time_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_repair_stock_items_updated_at
    BEFORE UPDATE ON public.repair_stock_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_form_templates_updated_at
    BEFORE UPDATE ON public.form_templates FOR EACH ROW EXECUTE FUNCTION update_form_updated_at_column();

CREATE TRIGGER trigger_update_form_responses_updated_at
    BEFORE UPDATE ON public.form_responses FOR EACH ROW EXECUTE FUNCTION update_form_updated_at_column();

CREATE TRIGGER trigger_update_invoices_updated_at
    BEFORE UPDATE ON public.invoices FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Functional triggers
DROP TRIGGER IF EXISTS assign_ticket_number_trigger ON public.repairs;
CREATE TRIGGER assign_ticket_number_trigger
    BEFORE INSERT ON public.repairs FOR EACH ROW EXECUTE FUNCTION assign_ticket_number();

DROP TRIGGER IF EXISTS ensure_repair_has_shop_trigger ON public.repairs;
CREATE TRIGGER ensure_repair_has_shop_trigger
    BEFORE INSERT ON public.repairs FOR EACH ROW EXECUTE FUNCTION ensure_repair_has_shop();

CREATE TRIGGER trigger_update_stock_on_sale
    AFTER INSERT OR DELETE ON public.sale_items FOR EACH ROW EXECUTE FUNCTION update_stock_on_sale();

DROP TRIGGER IF EXISTS update_repair_status_timestamps_trigger ON public.repairs;
CREATE TRIGGER update_repair_status_timestamps_trigger
    BEFORE UPDATE ON public.repairs FOR EACH ROW EXECUTE FUNCTION update_repair_status_timestamps();

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

-- Enable RLS on all tables
ALTER TABLE public.repair_shops ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_repair_shops ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.repairs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sale_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stock_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.one_time_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.repair_stock_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.technician_time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.form_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.form_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_action_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoice_items ENABLE ROW LEVEL SECURITY;

-- =============================================
-- CONTINUE WITH PART 2
-- =============================================

/*
 * NOTE: This file is part 1 of 3 for the complete database setup.
 *
 * To complete the setup, run these files in order:
 * 1. complete_database_setup.sql (this file)
 * 2. complete_database_setup_part2.sql
 * 3. complete_database_setup_part3.sql
 *
 * Or use the individual numbered migration files for step-by-step setup.
 *
 * The transaction will remain open until part 3 completes.
 */

-- Transaction continues in part 2...
