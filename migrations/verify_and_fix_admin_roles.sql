/*
 * REPAIR QR NINJA - VERIFY AND FIX ADMIN ROLES
 * 
 * This script helps diagnose and fix issues with administrator role assignments.
 * It provides queries to check user roles and functions to fix common issues.
 */

-- =============================================
-- DIAGNOSTIC QUERIES
-- =============================================

-- Check all users and their roles
SELECT 
    u.email,
    u.id as user_id,
    rs.name as repair_shop_name,
    urs.role,
    urs.created_at as role_assigned_at
FROM auth.users u
LEFT JOIN public.user_repair_shops urs ON u.id = urs.user_id
LEFT JOIN public.repair_shops rs ON urs.repair_shop_id = rs.id
ORDER BY u.email, rs.name;

-- Check for users without any role assignments
SELECT 
    u.email,
    u.id as user_id,
    u.created_at as user_created_at
FROM auth.users u
LEFT JOIN public.user_repair_shops urs ON u.id = urs.user_id
WHERE urs.user_id IS NULL
ORDER BY u.created_at DESC;

-- Check for administrators and count their accessible repairs
SELECT 
    u.email,
    urs.role,
    rs.name as repair_shop_name,
    COUNT(r.id) as repair_count
FROM auth.users u
JOIN public.user_repair_shops urs ON u.id = urs.user_id
JOIN public.repair_shops rs ON urs.repair_shop_id = rs.id
LEFT JOIN public.repairs r ON rs.id = r.repair_shop_id
WHERE urs.role = 'administrator'
GROUP BY u.email, urs.role, rs.name
ORDER BY u.email;

-- =============================================
-- HELPER FUNCTION TO ASSIGN ADMIN ROLE
-- =============================================

-- Function to assign administrator role to a user
CREATE OR REPLACE FUNCTION assign_admin_role(
    user_email TEXT,
    repair_shop_name TEXT DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    target_user_id UUID;
    target_shop_id UUID;
    existing_role TEXT;
    result JSON;
BEGIN
    -- Find user by email
    SELECT id INTO target_user_id
    FROM auth.users
    WHERE email = user_email;
    
    IF target_user_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User not found with email: ' || user_email
        );
    END IF;
    
    -- Find repair shop (use first one if not specified)
    IF repair_shop_name IS NULL THEN
        SELECT id INTO target_shop_id
        FROM public.repair_shops
        ORDER BY created_at
        LIMIT 1;
    ELSE
        SELECT id INTO target_shop_id
        FROM public.repair_shops
        WHERE name = repair_shop_name;
    END IF;
    
    IF target_shop_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Repair shop not found: ' || COALESCE(repair_shop_name, 'default')
        );
    END IF;
    
    -- Check if user already has a role in this shop
    SELECT role INTO existing_role
    FROM public.user_repair_shops
    WHERE user_id = target_user_id AND repair_shop_id = target_shop_id;
    
    IF existing_role IS NOT NULL THEN
        -- Update existing role to administrator
        UPDATE public.user_repair_shops
        SET role = 'administrator', updated_at = NOW()
        WHERE user_id = target_user_id AND repair_shop_id = target_shop_id;
        
        RETURN json_build_object(
            'success', true,
            'message', 'Updated role from ' || existing_role || ' to administrator',
            'user_email', user_email,
            'repair_shop_id', target_shop_id
        );
    ELSE
        -- Insert new role assignment
        INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role)
        VALUES (target_user_id, target_shop_id, 'administrator');
        
        RETURN json_build_object(
            'success', true,
            'message', 'Assigned administrator role',
            'user_email', user_email,
            'repair_shop_id', target_shop_id
        );
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'An error occurred: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- EXAMPLE USAGE
-- =============================================

/*
-- To assign administrator role to a user, uncomment and modify:
SELECT assign_admin_role('<EMAIL>');

-- To assign to a specific repair shop:
SELECT assign_admin_role('<EMAIL>', 'My Repair Shop');

-- To check the results:
SELECT * FROM public.user_repair_shops WHERE role = 'administrator';
*/

-- =============================================
-- VERIFICATION
-- =============================================

SELECT 'Admin role verification script ready!' as status;
