/*
 * REPAIR QR NINJA - COMPLETE BRANCH ASSIGNMENT FIX
 * 
 * This script fixes all branch assignment issues:
 * 1. Updates create_user_with_role function to support branch assignment during creation
 * 2. Fixes get_shop_users function to return branch information
 * 3. Ensures update_user_branch_assignment function works properly
 * 4. Adds proper RLS policies for branch operations
 */

-- Start transaction
BEGIN;

-- =============================================
-- STEP 1: FIX CREATE_USER_WITH_ROLE FUNCTION
-- =============================================

-- Drop existing function
DROP FUNCTION IF EXISTS public.create_user_with_role(UUID, TEXT, TEXT, TEXT, TEXT);

-- Create updated function with branch support
CREATE OR REPLACE FUNCTION public.create_user_with_role(
    shop_id UUID,
    user_email TEXT,
    user_full_name TEXT,
    user_password TEXT,
    user_role TEXT DEFAULT 'technician',
    user_branch_id UUID DEFAULT NULL
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
    new_user_id UUID;
    existing_user_id UUID;
    result JSON;
BEGIN
    -- Check if user already exists
    SELECT id INTO existing_user_id FROM auth.users WHERE email = user_email;
    
    IF existing_user_id IS NOT NULL THEN
        -- User exists, just add to shop if not already there
        INSERT INTO public.user_repair_shops (
            user_id,
            repair_shop_id,
            role,
            full_name,
            branch_id
        ) VALUES (
            existing_user_id,
            shop_id,
            user_role,
            user_full_name,
            user_branch_id
        )
        ON CONFLICT (user_id, repair_shop_id) DO UPDATE SET
            role = EXCLUDED.role,
            full_name = EXCLUDED.full_name,
            branch_id = EXCLUDED.branch_id;
        
        result := json_build_object(
            'success', true,
            'user_id', existing_user_id,
            'email', user_email,
            'message', 'User already existed, added to shop'
        );
        
        RETURN result;
    END IF;

    -- Create new user in auth.users
    INSERT INTO auth.users (
        instance_id,
        id,
        aud,
        role,
        email,
        encrypted_password,
        email_confirmed_at,
        recovery_sent_at,
        last_sign_in_at,
        raw_app_meta_data,
        raw_user_meta_data,
        created_at,
        updated_at,
        confirmation_token,
        email_change,
        email_change_token_new,
        recovery_token
    ) VALUES (
        '00000000-0000-0000-0000-000000000000',
        gen_random_uuid(),
        'authenticated',
        'authenticated',
        user_email,
        crypt(user_password, gen_salt('bf')),
        NOW(),
        NOW(),
        NOW(),
        '{"provider":"email","providers":["email"]}',
        '{}',
        NOW(),
        NOW(),
        '',
        '',
        '',
        ''
    )
    RETURNING id INTO new_user_id;

    -- Create identity record
    INSERT INTO auth.identities (
        id,
        user_id,
        identity_data,
        provider,
        provider_id,
        created_at,
        updated_at
    ) VALUES (
        gen_random_uuid(),
        new_user_id,
        format('{"sub":"%s","email":"%s"}', new_user_id::text, user_email)::jsonb,
        'email',
        new_user_id::text,
        NOW(),
        NOW()
    );

    -- Create user-shop association with branch
    INSERT INTO public.user_repair_shops (
        user_id,
        repair_shop_id,
        role,
        full_name,
        branch_id
    ) VALUES (
        new_user_id,
        shop_id,
        user_role,
        user_full_name,
        user_branch_id
    )
    ON CONFLICT (user_id, repair_shop_id) DO UPDATE SET
        role = EXCLUDED.role,
        full_name = EXCLUDED.full_name,
        branch_id = EXCLUDED.branch_id;

    -- Return success with user ID
    result := json_build_object(
        'success', true,
        'user_id', new_user_id,
        'email', user_email
    );
    
    RETURN result;
    
EXCEPTION WHEN OTHERS THEN
    -- Return error
    result := json_build_object(
        'success', false,
        'error', SQLERRM
    );
    RETURN result;
END;
$$;

-- =============================================
-- STEP 2: FIX GET_SHOP_USERS FUNCTION
-- =============================================

-- Drop and recreate the function with branch information
DROP FUNCTION IF EXISTS public.get_shop_users(UUID);

-- Function to get users for a specific repair shop (including branch info)
CREATE OR REPLACE FUNCTION public.get_shop_users(shop_id UUID)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    email TEXT,
    full_name TEXT,
    role TEXT,
    repair_shop_id UUID,
    branch_id UUID,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        urs.id,
        urs.user_id,
        au.email::TEXT,              -- Cast varchar to TEXT to match return type
        COALESCE(urs.full_name, '')::TEXT, -- Ensure full_name is TEXT and handle nulls
        urs.role::TEXT,              -- Cast to TEXT to be safe
        urs.repair_shop_id,
        urs.branch_id,
        urs.created_at,
        urs.updated_at
    FROM public.user_repair_shops urs
    JOIN auth.users au ON urs.user_id = au.id
    WHERE urs.repair_shop_id = shop_id
    ORDER BY urs.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- STEP 3: ENSURE BRANCH ASSIGNMENT FUNCTION EXISTS
-- =============================================

-- Create or replace the branch assignment function
CREATE OR REPLACE FUNCTION public.update_user_branch_assignment(
    p_shop_id UUID,
    p_target_user_id UUID,
    p_branch_id UUID
)
RETURNS JSON AS $$
DECLARE
    current_user_role TEXT;
    target_user_exists BOOLEAN;
    branch_exists BOOLEAN;
    result JSON;
BEGIN
    -- Check if current user is admin for this shop
    SELECT role INTO current_user_role
    FROM public.user_repair_shops
    WHERE user_id = auth.uid()
    AND repair_shop_id = p_shop_id;
    
    IF current_user_role != 'administrator' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Only administrators can update user branch assignments'
        );
    END IF;
    
    -- Check if target user exists in this shop
    SELECT EXISTS(
        SELECT 1 FROM public.user_repair_shops
        WHERE user_id = p_target_user_id
        AND repair_shop_id = p_shop_id
    ) INTO target_user_exists;
    
    IF NOT target_user_exists THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Target user not found in this repair shop'
        );
    END IF;
    
    -- Check if branch exists (if not null)
    IF p_branch_id IS NOT NULL THEN
        SELECT EXISTS(
            SELECT 1 FROM public.store_branches
            WHERE id = p_branch_id
            AND repair_shop_id = p_shop_id
            AND is_active = true
        ) INTO branch_exists;
        
        IF NOT branch_exists THEN
            RETURN json_build_object(
                'success', false,
                'error', 'Branch not found or inactive'
            );
        END IF;
    END IF;
    
    -- Update the user's branch assignment
    UPDATE public.user_repair_shops
    SET branch_id = p_branch_id,
        updated_at = NOW()
    WHERE user_id = p_target_user_id
    AND repair_shop_id = p_shop_id;
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Failed to update user branch assignment'
        );
    END IF;
    
    RETURN json_build_object(
        'success', true,
        'message', 'User branch assignment updated successfully'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- STEP 4: GRANT PERMISSIONS
-- =============================================

-- Grant execute permissions
GRANT EXECUTE ON FUNCTION public.create_user_with_role(UUID, TEXT, TEXT, TEXT, TEXT, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_shop_users(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_user_branch_assignment(UUID, UUID, UUID) TO authenticated;

-- =============================================
-- STEP 5: COMMIT CHANGES
-- =============================================

COMMIT;

-- =============================================
-- STEP 6: VERIFICATION
-- =============================================

-- Test the functions
SELECT 'Branch assignment functions updated successfully!' as status;

-- Verify function signatures
SELECT 
    routine_name,
    routine_type,
    data_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN ('create_user_with_role', 'get_shop_users', 'update_user_branch_assignment')
ORDER BY routine_name;
