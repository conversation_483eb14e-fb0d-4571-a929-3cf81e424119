/*
 * REPAIR QR NINJA - INITIAL DATABASE SETUP
 * Migration 001: Core Tables and Basic Structure
 * 
 * This migration creates the foundational database structure for the Repair QR Ninja application.
 * It includes core tables, basic relationships, and essential functions.
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- EXTENSIONS AND CONFIGURATION
-- =============================================

-- Enable the uuid-ossp extension for UUID generation
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- =============================================
-- CORE TABLE DEFINITIONS
-- =============================================

-- Create the repair_shops table
CREATE TABLE IF NOT EXISTS public.repair_shops (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    address TEXT,
    phone TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create the user_repair_shops junction table
CREATE TABLE IF NOT EXISTS public.user_repair_shops (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    role TEXT NOT NULL CHECK (role IN ('administrator', 'technician', 'receptionist', 'cashier')),
    full_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(user_id, repair_shop_id)
);

-- Create the repairs table with all current fields
CREATE TABLE IF NOT EXISTS public.repairs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    customer_name TEXT NOT NULL,
    customer_phone TEXT NOT NULL,
    customer_email TEXT,
    phone_model TEXT NOT NULL,
    problem_description TEXT NOT NULL,
    repair_price NUMERIC NOT NULL,
    repair_cost NUMERIC DEFAULT 0,
    cost_modifications TEXT DEFAULT '[]',
    payment_status TEXT NOT NULL CHECK (payment_status IN ('paid', 'partial', 'unpaid')),
    down_payment NUMERIC NOT NULL DEFAULT 0,
    status TEXT NOT NULL CHECK (status IN ('pending', 'inProgress', 'completed', 'cancelled', 'returned')),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_shop_id UUID REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    assigned_technician UUID REFERENCES auth.users(id),
    ticket_number INTEGER,
    observations JSONB DEFAULT '[]'::jsonb,
    price_modifications JSONB DEFAULT '[]'::jsonb,
    status_history JSONB DEFAULT '[]'::jsonb,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    completed_at TIMESTAMP WITH TIME ZONE,
    in_progress_at TIMESTAMP WITH TIME ZONE
);

-- =============================================
-- SEQUENCES AND AUTO-INCREMENT
-- =============================================

-- Create a sequence for ticket numbers starting from 10001
CREATE SEQUENCE IF NOT EXISTS repair_ticket_number_seq START WITH 10001;

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Create indexes for faster lookups
CREATE INDEX IF NOT EXISTS idx_repairs_user_id ON public.repairs(user_id);
CREATE INDEX IF NOT EXISTS idx_repairs_repair_shop_id ON public.repairs(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_repairs_status ON public.repairs(status);
CREATE INDEX IF NOT EXISTS idx_repairs_payment_status ON public.repairs(payment_status);
CREATE INDEX IF NOT EXISTS idx_repairs_assigned_technician ON public.repairs(assigned_technician);
CREATE INDEX IF NOT EXISTS idx_repairs_ticket_number ON public.repairs(ticket_number);
CREATE INDEX IF NOT EXISTS idx_repairs_customer_email ON public.repairs(customer_email) WHERE customer_email IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_repairs_created_at ON public.repairs(created_at);
CREATE INDEX IF NOT EXISTS idx_repairs_completed_at ON public.repairs(completed_at);

CREATE INDEX IF NOT EXISTS idx_user_repair_shops_user_id ON public.user_repair_shops(user_id);
CREATE INDEX IF NOT EXISTS idx_user_repair_shops_repair_shop_id ON public.user_repair_shops(repair_shop_id);

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to automatically assign ticket numbers to new repairs
CREATE OR REPLACE FUNCTION assign_ticket_number()
RETURNS TRIGGER AS $$
BEGIN
    -- If ticket_number is NULL, assign a new one from the sequence
    IF NEW.ticket_number IS NULL THEN
        NEW.ticket_number := nextval('repair_ticket_number_seq');
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to ensure all new repairs have a repair_shop_id
CREATE OR REPLACE FUNCTION ensure_repair_has_shop() RETURNS TRIGGER AS $$
DECLARE
    default_shop_id UUID;
BEGIN
    -- If repair_shop_id is NULL, assign the default shop
    IF NEW.repair_shop_id IS NULL THEN
        -- Get the ID of the default repair shop
        SELECT id INTO default_shop_id FROM public.repair_shops LIMIT 1;
        -- Set the repair_shop_id
        NEW.repair_shop_id := default_shop_id;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers to automatically update the updated_at column
DROP TRIGGER IF EXISTS update_repair_shops_updated_at ON public.repair_shops;
CREATE TRIGGER update_repair_shops_updated_at
    BEFORE UPDATE ON public.repair_shops
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_repairs_updated_at ON public.repairs;
CREATE TRIGGER update_repairs_updated_at
    BEFORE UPDATE ON public.repairs
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS update_user_repair_shops_updated_at ON public.user_repair_shops;
CREATE TRIGGER update_user_repair_shops_updated_at
    BEFORE UPDATE ON public.user_repair_shops
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create the trigger for ticket number assignment
DROP TRIGGER IF EXISTS assign_ticket_number_trigger ON public.repairs;
CREATE TRIGGER assign_ticket_number_trigger
    BEFORE INSERT ON public.repairs
    FOR EACH ROW
    EXECUTE FUNCTION assign_ticket_number();

-- Create the trigger for ensuring repairs have a shop
DROP TRIGGER IF EXISTS ensure_repair_has_shop_trigger ON public.repairs;
CREATE TRIGGER ensure_repair_has_shop_trigger
    BEFORE INSERT ON public.repairs
    FOR EACH ROW
    EXECUTE FUNCTION ensure_repair_has_shop();

-- =============================================
-- INITIAL DATA
-- =============================================

-- Create a default repair shop (replace with your actual shop details)
INSERT INTO public.repair_shops (name, address, phone)
VALUES ('Your Repair Shop', 'Your Address', 'Your Phone Number')
ON CONFLICT DO NOTHING;

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;
