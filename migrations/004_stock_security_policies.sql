/*
 * REPAIR QR NINJA - STOCK MANAGEMENT SECURITY
 * Migration 004: Row Level Security for Stock Management
 * 
 * This migration sets up Row Level Security (RLS) policies for all stock management tables.
 * Ensures users can only access stock data from repair shops they belong to.
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

-- Enable RLS on all stock management tables
ALTER TABLE public.product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sale_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stock_movements ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.one_time_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.repair_stock_items ENABLE ROW LEVEL SECURITY;

-- =============================================
-- PRODUCT CATEGORIES POLICIES
-- =============================================

CREATE POLICY "Users can view product categories from their repair shops" ON public.product_categories
    FOR SELECT
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage product categories for their repair shops" ON public.product_categories
    FOR ALL
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

-- =============================================
-- PRODUCTS POLICIES
-- =============================================

CREATE POLICY "Users can view products from their repair shops" ON public.products
    FOR SELECT
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage products for their repair shops" ON public.products
    FOR ALL
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

-- =============================================
-- SALES POLICIES
-- =============================================

CREATE POLICY "Users can view sales from their repair shops" ON public.sales
    FOR SELECT
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert sales for their repair shops" ON public.sales
    FOR INSERT
    WITH CHECK (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
        AND user_id = auth.uid()
    );

CREATE POLICY "Users can update sales from their repair shops" ON public.sales
    FOR UPDATE
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete sales from their repair shops" ON public.sales
    FOR DELETE
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

-- =============================================
-- SALE ITEMS POLICIES
-- =============================================

CREATE POLICY "Users can view sale items from their repair shops" ON public.sale_items
    FOR SELECT
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage sale items for their repair shops" ON public.sale_items
    FOR ALL
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

-- =============================================
-- STOCK MOVEMENTS POLICIES
-- =============================================

CREATE POLICY "Users can view stock movements from their repair shops" ON public.stock_movements
    FOR SELECT
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert stock movements for their repair shop" ON public.stock_movements
    FOR INSERT
    WITH CHECK (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
        AND user_id = auth.uid()
    );

-- =============================================
-- ONE-TIME ITEMS POLICIES
-- =============================================

CREATE POLICY "Users can view one-time items from their repair shops" ON public.one_time_items
    FOR SELECT
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage one-time items for their repair shops" ON public.one_time_items
    FOR ALL
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

-- =============================================
-- REPAIR STOCK ITEMS POLICIES
-- =============================================

CREATE POLICY "Users can view repair stock items from their repair shops" ON public.repair_stock_items
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repair_stock_items.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage repair stock items from their repair shops" ON public.repair_stock_items
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repair_stock_items.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;
