# Repair QR Ninja - Database Migrations

This directory contains the complete database migration structure for the Repair QR Ninja application. The migrations are designed to be run in sequence to build a fully functional premium repair shop management system.

## Migration Overview

### 📁 Migration Files Structure

The migrations are organized in numbered steps for easy execution:

1. **001_initial_setup.sql** - Core tables and basic structure
2. **002_row_level_security.sql** - Security policies and access control
3. **003_stock_management.sql** - Stock management and POS tables
4. **004_stock_security_policies.sql** - Security for stock management
5. **005_premium_features.sql** - Premium features (time tracking, forms, invoices)
6. **006_premium_security_policies.sql** - Security for premium features
7. **007_user_management_functions.sql** - Advanced user management functions
8. **008_realtime_and_final_setup.sql** - Real-time functionality and final setup

### 🚀 Quick Start Options

#### Option 1: Step-by-Step Migration (Recommended for Development)
Run each migration file in sequence:

```sql
-- Run in Supabase SQL Editor, one at a time
\i 001_initial_setup.sql
\i 002_row_level_security.sql
\i 003_stock_management.sql
\i 004_stock_security_policies.sql
\i 005_premium_features.sql
\i 006_premium_security_policies.sql
\i 007_user_management_functions.sql
\i 008_realtime_and_final_setup.sql
```

#### Option 2: Single Consolidated File (Recommended for Production)
Use the consolidated file for one-time setup:

```sql
-- Run the complete setup in one go
\i complete_database_setup.sql
```

## 📋 What's Included

### Core Features
- ✅ **Repair Management** - Complete repair tracking system
- ✅ **Customer Management** - Customer information and communication
- ✅ **User Management** - Multi-user support with role-based access
- ✅ **Ticket System** - Automatic ticket numbering and QR codes
- ✅ **Status Tracking** - Repair status history and timeline

### Premium Features
- ✅ **Stock Management** - Complete inventory system
- ✅ **POS System** - Point of sale for retail items
- ✅ **Time Tracking** - Technician time tracking per repair
- ✅ **Custom Forms** - Customizable intake forms
- ✅ **Invoice Generation** - Professional invoice creation
- ✅ **Action Logging** - Complete audit trail
- ✅ **Cost Tracking** - Repair cost tracking and earnings calculation

### Security & Performance
- ✅ **Row Level Security** - Complete data isolation between repair shops
- ✅ **Real-time Updates** - Live data synchronization
- ✅ **Optimized Indexes** - Performance-optimized database structure
- ✅ **Data Validation** - Comprehensive constraints and checks

## 🔧 Database Schema Overview

### Core Tables
- `repair_shops` - Repair shop information
- `user_repair_shops` - User-shop associations with roles
- `repairs` - Main repair records with all tracking data
- `technician_time_entries` - Time tracking for repairs

### Stock Management Tables
- `product_categories` - Product categorization
- `products` - Product catalog with inventory
- `sales` - POS sales records
- `sale_items` - Individual sale line items
- `stock_movements` - Inventory movement tracking
- `one_time_items` - Unique items (phones, devices)
- `repair_stock_items` - Parts used in repairs

### Premium Feature Tables
- `form_templates` - Customizable form definitions
- `form_responses` - Customer form responses
- `invoices` - Invoice headers
- `invoice_items` - Invoice line items
- `user_action_logs` - System audit trail

## 🛡️ Security Model

### Role-Based Access Control (RBAC)
- **Administrator** - Full access to all features and user management
- **Technician** - Repair management and basic stock operations
- **Receptionist** - Customer service and basic repair operations
- **Cashier** - POS operations and payment processing

### Data Isolation
- Complete data separation between repair shops
- Users can only access data from their assigned repair shop(s)
- Secure API access through Supabase Row Level Security

## 📊 Sample Data

The migrations include sample data for:
- Default repair shop setup
- Sample product categories (Phone Screens, Batteries, Accessories)
- Sample products with pricing and inventory
- Standard phone intake form template
- Initial system configuration

## 🔄 Maintenance Functions

### Automated Functions
- `assign_ticket_number()` - Auto-assigns ticket numbers to new repairs
- `update_repair_status_timestamps()` - Tracks status change timestamps
- `update_stock_on_sale()` - Automatically updates inventory on sales
- `generate_sale_number()` - Generates unique sale numbers
- `generate_invoice_number()` - Generates unique invoice numbers

### Maintenance Functions
- `cleanup_old_action_logs()` - Removes old audit logs (90+ days)
- `check_and_fix_user_role()` - Ensures proper admin user setup
- `get_technician_time_tracking()` - Retrieves time tracking data
- `create_user_with_role()` - Creates users with proper role assignment

## 🚨 Important Notes

### Before Running Migrations
1. **Backup existing data** if upgrading an existing database
2. **Review repair shop information** in the initial setup
3. **Configure Supabase project settings** for your environment
4. **Set up authentication providers** (email, OAuth, etc.)

### After Running Migrations
1. **Update default repair shop information** with your actual details
2. **Create your first administrator user** through Supabase Auth
3. **Configure email templates** for customer notifications
4. **Add your product catalog** and categories
5. **Set up real-time subscriptions** in your application

### Environment Variables
Ensure these are configured in your application:
```env
VITE_SUPABASE_URL=your_supabase_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key
```

## 🔍 Troubleshooting

### Common Issues
1. **Permission Errors** - Ensure you're running as a database owner
2. **Constraint Violations** - Check for existing data conflicts
3. **Missing Extensions** - Ensure `uuid-ossp` extension is available
4. **RLS Policies** - Verify policies are properly configured

### Reset Database (Development Only)
```sql
-- WARNING: This will delete ALL data
DROP SCHEMA public CASCADE;
CREATE SCHEMA public;
GRANT ALL ON SCHEMA public TO postgres;
GRANT ALL ON SCHEMA public TO public;
-- Then run migrations again
```

## 📞 Support

For issues or questions:
1. Check the application logs for detailed error messages
2. Verify all migrations completed successfully
3. Ensure proper user roles and permissions are set
4. Review the Supabase dashboard for any configuration issues

---

**Ready to build an amazing repair shop management system! 🔧📱**
