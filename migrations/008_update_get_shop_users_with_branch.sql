/*
 * REPAIR QR NINJA - UPDATE GET_SHOP_USERS WITH BRANCH INFO
 * Migration 008: Update get_shop_users function to include branch information
 * 
 * This migration updates the get_shop_users function to return branch_id
 * so the user management interface can display and manage user branch assignments.
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- Drop and recreate the function with branch information
DROP FUNCTION IF EXISTS public.get_shop_users(UUID);

-- Function to get users for a specific repair shop (including branch info)
CREATE OR REPLACE FUNCTION public.get_shop_users(shop_id UUID)
RETURNS TABLE (
    id UUID,
    user_id UUID,
    email TEXT,
    full_name TEXT,
    role TEXT,
    repair_shop_id UUID,
    branch_id UUID,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        urs.id,
        urs.user_id,
        au.email::TEXT,              -- Cast varchar to TEXT to match return type
        COALESCE(urs.full_name, '')::TEXT, -- Ensure full_name is TEXT and handle nulls
        urs.role::TEXT,              -- Cast to TEXT to be safe
        urs.repair_shop_id,
        urs.branch_id,
        urs.created_at,
        urs.updated_at
    FROM public.user_repair_shops urs
    JOIN auth.users au ON urs.user_id = au.id
    WHERE urs.repair_shop_id = shop_id
    ORDER BY urs.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.get_shop_users(UUID) TO authenticated;

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;
