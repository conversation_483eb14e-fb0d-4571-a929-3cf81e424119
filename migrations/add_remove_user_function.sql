/*
 * REPAIR QR NINJA - ADD REMOVE USER FUNCTION
 * 
 * This adds a proper function for removing users from repair shops
 * with validation and safety checks.
 */

-- Start transaction
BEGIN;

-- Function to safely remove a user from a repair shop
CREATE OR REPLACE FUNCTION remove_user_from_shop(
  shop_id UUID,
  target_user_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_role TEXT;
  target_user_role TEXT;
  target_user_email TEXT;
  admin_count INTEGER;
  result JSON;
BEGIN
  -- Check if current user is administrator in the specified shop
  SELECT role INTO current_user_role
  FROM public.user_repair_shops
  WHERE user_id = auth.uid() 
  AND repair_shop_id = shop_id;

  IF current_user_role != 'administrator' THEN
    RETURN json_build_object(
      'success', false, 
      'error', 'Only administrators can remove users'
    );
  END IF;

  -- Get target user info
  SELECT urs.role, au.email INTO target_user_role, target_user_email
  FROM public.user_repair_shops urs
  JOIN auth.users au ON urs.user_id = au.id
  WHERE urs.user_id = target_user_id 
  AND urs.repair_shop_id = shop_id;

  IF target_user_role IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User not found in this repair shop'
    );
  END IF;

  -- Prevent removing yourself
  IF target_user_id = auth.uid() THEN
    RETURN json_build_object(
      'success', false,
      'error', 'You cannot remove yourself from the shop'
    );
  END IF;

  -- If removing an administrator, ensure there's at least one other admin
  IF target_user_role = 'administrator' THEN
    SELECT COUNT(*) INTO admin_count
    FROM public.user_repair_shops
    WHERE repair_shop_id = shop_id 
    AND role = 'administrator'
    AND user_id != target_user_id;

    IF admin_count = 0 THEN
      RETURN json_build_object(
        'success', false,
        'error', 'Cannot remove the last administrator from the shop'
      );
    END IF;
  END IF;

  -- Remove user from repair shop
  DELETE FROM public.user_repair_shops
  WHERE user_id = target_user_id 
  AND repair_shop_id = shop_id;

  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User was not found in this repair shop'
    );
  END IF;

  -- Return success
  RETURN json_build_object(
    'success', true,
    'message', format('User %s removed from repair shop successfully', target_user_email),
    'removed_user_id', target_user_id,
    'removed_user_email', target_user_email
  );

EXCEPTION WHEN OTHERS THEN
  -- Return error
  RETURN json_build_object(
    'success', false,
    'error', 'Database error: ' || SQLERRM
  );
END;
$$;

-- Function to update user role with validation
CREATE OR REPLACE FUNCTION update_user_role_in_shop(
  shop_id UUID,
  target_user_id UUID,
  new_role TEXT
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  current_user_role TEXT;
  target_user_role TEXT;
  target_user_email TEXT;
  admin_count INTEGER;
  result JSON;
BEGIN
  -- Check if current user is administrator in the specified shop
  SELECT role INTO current_user_role
  FROM public.user_repair_shops
  WHERE user_id = auth.uid() 
  AND repair_shop_id = shop_id;

  IF current_user_role != 'administrator' THEN
    RETURN json_build_object(
      'success', false, 
      'error', 'Only administrators can update user roles'
    );
  END IF;

  -- Validate new role
  IF new_role NOT IN ('administrator', 'technician', 'receptionist', 'cashier') THEN
    RETURN json_build_object(
      'success', false, 
      'error', 'Invalid role specified'
    );
  END IF;

  -- Get target user info
  SELECT urs.role, au.email INTO target_user_role, target_user_email
  FROM public.user_repair_shops urs
  JOIN auth.users au ON urs.user_id = au.id
  WHERE urs.user_id = target_user_id 
  AND urs.repair_shop_id = shop_id;

  IF target_user_role IS NULL THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User not found in this repair shop'
    );
  END IF;

  -- If demoting an administrator, ensure there's at least one other admin
  IF target_user_role = 'administrator' AND new_role != 'administrator' THEN
    SELECT COUNT(*) INTO admin_count
    FROM public.user_repair_shops
    WHERE repair_shop_id = shop_id 
    AND role = 'administrator'
    AND user_id != target_user_id;

    IF admin_count = 0 THEN
      RETURN json_build_object(
        'success', false,
        'error', 'Cannot demote the last administrator in the shop'
      );
    END IF;
  END IF;

  -- Update user role
  UPDATE public.user_repair_shops
  SET role = new_role, updated_at = NOW()
  WHERE user_id = target_user_id 
  AND repair_shop_id = shop_id;

  IF NOT FOUND THEN
    RETURN json_build_object(
      'success', false,
      'error', 'User was not found in this repair shop'
    );
  END IF;

  -- Return success
  RETURN json_build_object(
    'success', true,
    'message', format('User %s role updated to %s successfully', target_user_email, new_role),
    'user_id', target_user_id,
    'user_email', target_user_email,
    'new_role', new_role
  );

EXCEPTION WHEN OTHERS THEN
  -- Return error
  RETURN json_build_object(
    'success', false,
    'error', 'Database error: ' || SQLERRM
  );
END;
$$;

-- Commit the changes
COMMIT;

SELECT 'User management functions added successfully!' as status;
