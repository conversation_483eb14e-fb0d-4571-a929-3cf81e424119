/*
 * REPAIR QR NINJA - ADD BRANCH TO REPAIRS
 * Migration 007: Add branch tracking to repairs table
 * 
 * This migration adds branch_id to the repairs table and updates related functions
 * to automatically assign the current user's branch to new repairs.
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- ADD BRANCH COLUMN TO REPAIRS
-- =============================================

-- Add branch_id column to repairs table
ALTER TABLE public.repairs 
ADD COLUMN IF NOT EXISTS branch_id UUID REFERENCES public.store_branches(id) ON DELETE SET NULL;

-- Create index for better performance
CREATE INDEX IF NOT EXISTS idx_repairs_branch_id ON public.repairs(branch_id);

-- =============================================
-- UPDATE FUNCTIONS TO HANDLE BRANCH ASSIGNMENT
-- =============================================

-- Function to automatically assign branch to new repairs based on user's branch
CREATE OR REPLACE FUNCTION assign_user_branch_to_repair()
RETURNS TRIGGER AS $$
DECLARE
    user_branch_id UUID;
BEGIN
    -- If branch_id is not already set, try to get it from user's branch assignment
    IF NEW.branch_id IS NULL THEN
        -- Get the user's assigned branch
        SELECT branch_id INTO user_branch_id 
        FROM public.user_repair_shops 
        WHERE user_id = NEW.user_id 
          AND repair_shop_id = NEW.repair_shop_id
          AND branch_id IS NOT NULL
        LIMIT 1;
        
        -- If user has an assigned branch, use it
        IF user_branch_id IS NOT NULL THEN
            NEW.branch_id := user_branch_id;
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically assign branch to new repairs
DROP TRIGGER IF EXISTS assign_user_branch_to_repair_trigger ON public.repairs;
CREATE TRIGGER assign_user_branch_to_repair_trigger
    BEFORE INSERT ON public.repairs
    FOR EACH ROW
    EXECUTE FUNCTION assign_user_branch_to_repair();

-- =============================================
-- UPDATE EXISTING REPAIRS WITH BRANCH DATA
-- =============================================

-- Update existing repairs to have branch_id based on user's current branch assignment
-- This is a one-time update for existing data
UPDATE public.repairs 
SET branch_id = (
    SELECT urs.branch_id 
    FROM public.user_repair_shops urs 
    WHERE urs.user_id = repairs.user_id 
      AND urs.repair_shop_id = repairs.repair_shop_id
      AND urs.branch_id IS NOT NULL
    LIMIT 1
)
WHERE branch_id IS NULL;

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;
