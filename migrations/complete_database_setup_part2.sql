/*
 * REPAIR QR NINJA - COMPLETE DATABASE SETUP (PART 2)
 * 
 * This is the continuation of the consolidated migration file.
 * Run this after complete_database_setup.sql
 * 
 * This part includes:
 * - Row Level Security Policies
 * - User Management Functions
 * - Real-time Setup
 * - Sample Data
 * - Final Configuration
 */

-- Continue transaction from part 1
-- Note: If running separately, uncomment the BEGIN statement
-- BEGIN;

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================

-- REPAIR SHOPS POLICIES
-- Simplified policies to avoid recursion - allow all authenticated users to view repair shops
CREATE POLICY "Users can view repair shops" ON public.repair_shops
    FOR SELECT USING (auth.uid() IS NOT NULL);

CREATE POLICY "Users can manage repair shops" ON public.repair_shops
    FOR ALL USING (auth.uid() IS NOT NULL);

-- USER-REPAIR SHOPS POLICIES
CREATE POLICY "Users can view their own repair shop associations" ON public.user_repair_shops
    FOR SELECT USING (user_id = auth.uid());

CREATE POLICY "Users can insert their own repair shop associations" ON public.user_repair_shops
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own repair shop associations" ON public.user_repair_shops
    FOR UPDATE USING (user_id = auth.uid());

-- Note: We avoid recursive policies by not checking role within the same table
-- Administrators can manage associations through application logic

-- REPAIRS POLICIES
-- Simplified policies to avoid recursion - users can access repairs they created or are assigned to
CREATE POLICY "Users can view their own repairs" ON public.repairs
    FOR SELECT USING (
        user_id = auth.uid() OR assigned_technician = auth.uid()
    );

CREATE POLICY "Users can insert repairs" ON public.repairs
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own repairs" ON public.repairs
    FOR UPDATE USING (
        user_id = auth.uid() OR assigned_technician = auth.uid()
    );

CREATE POLICY "Users can delete their own repairs" ON public.repairs
    FOR DELETE USING (user_id = auth.uid());

-- STOCK MANAGEMENT POLICIES
-- Simplified policies - allow access to default repair shop for now
CREATE POLICY "Users can view product categories" ON public.product_categories
    FOR SELECT USING (true);

CREATE POLICY "Users can manage product categories" ON public.product_categories
    FOR ALL USING (true);

CREATE POLICY "Users can view products" ON public.products
    FOR SELECT USING (true);

CREATE POLICY "Users can manage products" ON public.products
    FOR ALL USING (true);

CREATE POLICY "Users can view sales" ON public.sales
    FOR SELECT USING (true);

CREATE POLICY "Users can insert sales" ON public.sales
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update sales" ON public.sales
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete sales" ON public.sales
    FOR DELETE USING (user_id = auth.uid());

CREATE POLICY "Users can view sale items" ON public.sale_items
    FOR SELECT USING (true);

CREATE POLICY "Users can manage sale items" ON public.sale_items
    FOR ALL USING (true);

CREATE POLICY "Users can view stock movements" ON public.stock_movements
    FOR SELECT USING (true);

CREATE POLICY "Users can insert stock movements" ON public.stock_movements
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can view one-time items" ON public.one_time_items
    FOR SELECT USING (true);

CREATE POLICY "Users can manage one-time items" ON public.one_time_items
    FOR ALL USING (true);

CREATE POLICY "Users can view repair stock items" ON public.repair_stock_items
    FOR SELECT USING (true);

CREATE POLICY "Users can manage repair stock items" ON public.repair_stock_items
    FOR ALL USING (true);

-- PREMIUM FEATURES POLICIES
CREATE POLICY "Users can view time entries" ON public.technician_time_entries
    FOR SELECT USING (true);

CREATE POLICY "Users can insert their own time entries" ON public.technician_time_entries
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update their own time entries" ON public.technician_time_entries
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can view form templates" ON public.form_templates
    FOR SELECT USING (true);

CREATE POLICY "Users can manage form templates" ON public.form_templates
    FOR ALL USING (true);

CREATE POLICY "Users can view form responses" ON public.form_responses
    FOR SELECT USING (true);

CREATE POLICY "Users can insert form responses" ON public.form_responses
    FOR INSERT WITH CHECK (true);

CREATE POLICY "Users can update form responses" ON public.form_responses
    FOR UPDATE USING (true);

CREATE POLICY "Users can insert their own action logs" ON public.user_action_logs
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can view action logs" ON public.user_action_logs
    FOR SELECT USING (true);

CREATE POLICY "Users can view invoices" ON public.invoices
    FOR SELECT USING (true);

CREATE POLICY "Users can insert invoices" ON public.invoices
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Users can update invoices" ON public.invoices
    FOR UPDATE USING (user_id = auth.uid());

CREATE POLICY "Users can delete invoices" ON public.invoices
    FOR DELETE USING (user_id = auth.uid());

CREATE POLICY "Users can view invoice items" ON public.invoice_items
    FOR SELECT USING (true);

CREATE POLICY "Users can manage invoice items" ON public.invoice_items
    FOR ALL USING (true);
