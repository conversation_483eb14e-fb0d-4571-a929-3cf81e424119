/*
 * REPAIR QR NINJA - USER MANAGEMENT FUNCTIONS
 * Migration 007: Advanced User Management and RBAC Functions
 * 
 * This migration adds advanced user management functions for creating users,
 * updating roles, and managing repair shop associations.
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- USER CREATION AND MANAGEMENT FUNCTIONS
-- =============================================

-- Function to create a user with a specific role
CREATE OR REPLACE FUNCTION create_user_with_role(
    p_email TEXT,
    p_password TEXT,
    p_full_name TEXT,
    p_role TEXT DEFAULT 'technician',
    p_repair_shop_id UUID DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    new_user_id UUID;
    default_shop_id UUID;
    result JSON;
BEGIN
    -- Validate role
    IF p_role NOT IN ('administrator', 'technician', 'receptionist', 'cashier') THEN
        RETURN json_build_object('success', false, 'error', 'Invalid role specified');
    END IF;

    -- Get default repair shop if none specified
    IF p_repair_shop_id IS NULL THEN
        SELECT id INTO default_shop_id FROM public.repair_shops LIMIT 1;
        p_repair_shop_id := default_shop_id;
    END IF;

    -- Check if repair shop exists
    IF NOT EXISTS (SELECT 1 FROM public.repair_shops WHERE id = p_repair_shop_id) THEN
        RETURN json_build_object('success', false, 'error', 'Repair shop not found');
    END IF;

    -- Create the user in auth.users (this would typically be done via Supabase Auth API)
    -- For now, we'll assume the user is created externally and we get the ID
    -- This function primarily handles the repair shop association

    -- Check if user already exists
    SELECT id INTO new_user_id FROM auth.users WHERE email = p_email;
    
    IF new_user_id IS NULL THEN
        RETURN json_build_object('success', false, 'error', 'User must be created in auth system first');
    END IF;

    -- Create or update user-repair shop association
    INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role, full_name)
    VALUES (new_user_id, p_repair_shop_id, p_role, p_full_name)
    ON CONFLICT (user_id, repair_shop_id) 
    DO UPDATE SET 
        role = EXCLUDED.role,
        full_name = EXCLUDED.full_name,
        updated_at = NOW();

    -- Log the action
    INSERT INTO public.user_action_logs (user_id, action, details, repair_shop_id)
    VALUES (
        auth.uid(),
        'user_created',
        json_build_object(
            'target_user_id', new_user_id,
            'email', p_email,
            'role', p_role,
            'full_name', p_full_name
        ),
        p_repair_shop_id
    );

    result := json_build_object(
        'success', true,
        'user_id', new_user_id,
        'message', 'User created and associated with repair shop successfully'
    );

    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'An error occurred: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to update user role
CREATE OR REPLACE FUNCTION update_user_role(
    p_user_id UUID,
    p_new_role TEXT,
    p_repair_shop_id UUID DEFAULT NULL
)
RETURNS JSON AS $$
DECLARE
    current_user_role TEXT;
    target_repair_shop_id UUID;
    result JSON;
BEGIN
    -- Validate role
    IF p_new_role NOT IN ('administrator', 'technician', 'receptionist', 'cashier') THEN
        RETURN json_build_object('success', false, 'error', 'Invalid role specified');
    END IF;

    -- Get repair shop ID if not provided
    IF p_repair_shop_id IS NULL THEN
        SELECT repair_shop_id INTO target_repair_shop_id 
        FROM public.user_repair_shops 
        WHERE user_id = auth.uid() 
        LIMIT 1;
    ELSE
        target_repair_shop_id := p_repair_shop_id;
    END IF;

    -- Check if current user is administrator in the repair shop
    SELECT role INTO current_user_role
    FROM public.user_repair_shops
    WHERE user_id = auth.uid() 
    AND repair_shop_id = target_repair_shop_id;

    IF current_user_role != 'administrator' THEN
        RETURN json_build_object('success', false, 'error', 'Only administrators can update user roles');
    END IF;

    -- Check if target user exists in the repair shop
    IF NOT EXISTS (
        SELECT 1 FROM public.user_repair_shops 
        WHERE user_id = p_user_id 
        AND repair_shop_id = target_repair_shop_id
    ) THEN
        RETURN json_build_object('success', false, 'error', 'User not found in this repair shop');
    END IF;

    -- Update the user role
    UPDATE public.user_repair_shops
    SET role = p_new_role, updated_at = NOW()
    WHERE user_id = p_user_id 
    AND repair_shop_id = target_repair_shop_id;

    -- Log the action
    INSERT INTO public.user_action_logs (user_id, action, details, repair_shop_id)
    VALUES (
        auth.uid(),
        'user_role_updated',
        json_build_object(
            'target_user_id', p_user_id,
            'new_role', p_new_role,
            'repair_shop_id', target_repair_shop_id
        ),
        target_repair_shop_id
    );

    result := json_build_object(
        'success', true,
        'message', 'User role updated successfully'
    );

    RETURN result;

EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'An error occurred: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user details with role information
CREATE OR REPLACE FUNCTION get_user_details(p_user_id UUID DEFAULT NULL)
RETURNS TABLE (
    user_id UUID,
    email TEXT,
    full_name TEXT,
    role TEXT,
    repair_shop_id UUID,
    repair_shop_name TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    -- If no user_id provided, get current user
    IF p_user_id IS NULL THEN
        p_user_id := auth.uid();
    END IF;

    RETURN QUERY
    SELECT 
        urs.user_id,
        au.email,
        urs.full_name,
        urs.role,
        urs.repair_shop_id,
        rs.name as repair_shop_name,
        urs.created_at,
        urs.updated_at
    FROM public.user_repair_shops urs
    JOIN auth.users au ON urs.user_id = au.id
    JOIN public.repair_shops rs ON urs.repair_shop_id = rs.id
    WHERE urs.user_id = p_user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check and fix user roles (maintenance function)
CREATE OR REPLACE FUNCTION check_and_fix_user_role()
RETURNS JSON AS $$
DECLARE
    user_count INTEGER;
    admin_count INTEGER;
    default_shop_id UUID;
    result JSON;
BEGIN
    -- Get default repair shop
    SELECT id INTO default_shop_id FROM public.repair_shops LIMIT 1;
    
    -- Count total users in the default shop
    SELECT COUNT(*) INTO user_count
    FROM public.user_repair_shops
    WHERE repair_shop_id = default_shop_id;
    
    -- Count administrators in the default shop
    SELECT COUNT(*) INTO admin_count
    FROM public.user_repair_shops
    WHERE repair_shop_id = default_shop_id
    AND role = 'administrator';
    
    -- If no administrators exist, promote the first user to administrator
    IF admin_count = 0 AND user_count > 0 THEN
        UPDATE public.user_repair_shops
        SET role = 'administrator', updated_at = NOW()
        WHERE repair_shop_id = default_shop_id
        AND user_id = (
            SELECT user_id 
            FROM public.user_repair_shops 
            WHERE repair_shop_id = default_shop_id 
            ORDER BY created_at ASC 
            LIMIT 1
        );
        
        result := json_build_object(
            'success', true,
            'message', 'Promoted first user to administrator',
            'total_users', user_count,
            'admin_count', 1
        );
    ELSE
        result := json_build_object(
            'success', true,
            'message', 'User roles are properly configured',
            'total_users', user_count,
            'admin_count', admin_count
        );
    END IF;
    
    RETURN result;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;
