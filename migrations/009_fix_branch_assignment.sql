/*
 * REPAIR QR NINJA - FIX BRANCH ASSIGNMENT
 * Migration 009: Fix branch assignment functionality
 * 
 * This migration creates a secure function to update user branch assignments
 * that works with Row Level Security policies.
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- SECURE BRANCH ASSIGNMENT FUNCTION
-- =============================================

-- Function to update user branch assignment (admin only)
CREATE OR REPLACE FUNCTION public.update_user_branch_assignment(
    p_shop_id UUID,
    p_target_user_id UUID,
    p_branch_id UUID
)
RETURNS JSON AS $$
DECLARE
    current_user_role TEXT;
    target_user_exists BOOLEAN;
    branch_exists BOOLEAN;
    result JSON;
BEGIN
    -- Check if current user is admin for this shop
    SELECT role INTO current_user_role
    FROM public.user_repair_shops
    WHERE user_id = auth.uid()
    AND repair_shop_id = p_shop_id;
    
    IF current_user_role != 'administrator' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Only administrators can update user branch assignments'
        );
    END IF;
    
    -- Check if target user exists in this shop
    SELECT EXISTS(
        SELECT 1 FROM public.user_repair_shops
        WHERE user_id = p_target_user_id
        AND repair_shop_id = p_shop_id
    ) INTO target_user_exists;
    
    IF NOT target_user_exists THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Target user not found in this repair shop'
        );
    END IF;
    
    -- Check if branch exists (if not null)
    IF p_branch_id IS NOT NULL THEN
        SELECT EXISTS(
            SELECT 1 FROM public.store_branches
            WHERE id = p_branch_id
            AND repair_shop_id = p_shop_id
            AND is_active = true
        ) INTO branch_exists;
        
        IF NOT branch_exists THEN
            RETURN json_build_object(
                'success', false,
                'error', 'Branch not found or inactive'
            );
        END IF;
    END IF;
    
    -- Update the user's branch assignment
    UPDATE public.user_repair_shops
    SET branch_id = p_branch_id,
        updated_at = NOW()
    WHERE user_id = p_target_user_id
    AND repair_shop_id = p_shop_id;
    
    IF NOT FOUND THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Failed to update user branch assignment'
        );
    END IF;
    
    RETURN json_build_object(
        'success', true,
        'message', 'User branch assignment updated successfully'
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.update_user_branch_assignment(UUID, UUID, UUID) TO authenticated;

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;