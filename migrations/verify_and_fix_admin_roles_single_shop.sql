/*
 * REPAIR QR NINJA - VERIFY AND FIX ADMIN ROLES (SINGLE SHOP)
 * 
 * This script helps diagnose and fix issues with administrator role assignments
 * in a single repair shop setup.
 */

-- =============================================
-- DIAGN<PERSON><PERSON>C QUERIES FOR SINGLE SHOP
-- =============================================

-- Check all users and their roles
SELECT 
    u.email,
    u.id as user_id,
    urs.role,
    urs.created_at as role_assigned_at,
    CASE 
        WHEN urs.role = 'administrator' THEN 'Can see all repairs'
        WHEN urs.role = 'technician' THEN 'Can see assigned repairs (toggle for all)'
        WHEN urs.role IS NULL THEN 'NO ROLE ASSIGNED - ISSUE!'
        ELSE 'Can see own/assigned repairs'
    END as access_level
FROM auth.users u
LEFT JOIN public.user_repair_shops urs ON u.id = urs.user_id
ORDER BY u.email;

-- Check for users without any role assignments (PROBLEM!)
SELECT 
    u.email,
    u.id as user_id,
    u.created_at as user_created_at,
    'NEEDS ROLE ASSIGNMENT' as issue
FROM auth.users u
LEFT JOIN public.user_repair_shops urs ON u.id = urs.user_id
WHERE urs.user_id IS NULL
ORDER BY u.created_at DESC;

-- Count repairs accessible by each administrator
SELECT 
    u.email,
    urs.role,
    COUNT(r.id) as total_repairs_in_system,
    CASE 
        WHEN urs.role = 'administrator' THEN 'Should see ALL repairs'
        ELSE 'Should see limited repairs'
    END as expected_access
FROM auth.users u
JOIN public.user_repair_shops urs ON u.id = urs.user_id
LEFT JOIN public.repairs r ON true  -- All repairs for single shop
WHERE urs.role = 'administrator'
GROUP BY u.email, urs.role
ORDER BY u.email;

-- Check repair shop setup
SELECT 
    rs.id,
    rs.name,
    rs.created_at,
    COUNT(urs.user_id) as user_count,
    COUNT(r.id) as repair_count
FROM public.repair_shops rs
LEFT JOIN public.user_repair_shops urs ON rs.id = urs.repair_shop_id
LEFT JOIN public.repairs r ON rs.id = r.repair_shop_id
GROUP BY rs.id, rs.name, rs.created_at
ORDER BY rs.created_at;

-- =============================================
-- SIMPLIFIED ADMIN ASSIGNMENT FUNCTION
-- =============================================

-- Function to assign administrator role to a user (single shop)
CREATE OR REPLACE FUNCTION assign_admin_role_single_shop(
    user_email TEXT
)
RETURNS JSON AS $$
DECLARE
    target_user_id UUID;
    shop_id UUID;
    existing_role TEXT;
    result JSON;
BEGIN
    -- Find user by email
    SELECT id INTO target_user_id
    FROM auth.users
    WHERE email = user_email;
    
    IF target_user_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User not found with email: ' || user_email
        );
    END IF;
    
    -- Get the repair shop (should be only one)
    SELECT id INTO shop_id
    FROM public.repair_shops
    ORDER BY created_at
    LIMIT 1;
    
    IF shop_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'No repair shop found in the system'
        );
    END IF;
    
    -- Check if user already has a role
    SELECT role INTO existing_role
    FROM public.user_repair_shops
    WHERE user_id = target_user_id AND repair_shop_id = shop_id;
    
    IF existing_role IS NOT NULL THEN
        -- Update existing role to administrator
        UPDATE public.user_repair_shops
        SET role = 'administrator', updated_at = NOW()
        WHERE user_id = target_user_id AND repair_shop_id = shop_id;
        
        RETURN json_build_object(
            'success', true,
            'message', 'Updated role from ' || existing_role || ' to administrator',
            'user_email', user_email,
            'previous_role', existing_role
        );
    ELSE
        -- Insert new role assignment
        INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role)
        VALUES (target_user_id, shop_id, 'administrator');
        
        RETURN json_build_object(
            'success', true,
            'message', 'Assigned administrator role (was unassigned)',
            'user_email', user_email,
            'previous_role', 'none'
        );
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'An error occurred: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- BULK ADMIN ASSIGNMENT (if needed)
-- =============================================

-- Function to make all existing users administrators (emergency fix)
CREATE OR REPLACE FUNCTION make_all_users_admin()
RETURNS JSON AS $$
DECLARE
    shop_id UUID;
    user_count INTEGER := 0;
    result JSON;
BEGIN
    -- Get the repair shop
    SELECT id INTO shop_id
    FROM public.repair_shops
    ORDER BY created_at
    LIMIT 1;
    
    IF shop_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'No repair shop found'
        );
    END IF;
    
    -- Insert administrator role for all users who don't have one
    INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role)
    SELECT u.id, shop_id, 'administrator'
    FROM auth.users u
    WHERE NOT EXISTS (
        SELECT 1 FROM public.user_repair_shops urs 
        WHERE urs.user_id = u.id AND urs.repair_shop_id = shop_id
    );
    
    GET DIAGNOSTICS user_count = ROW_COUNT;
    
    -- Update existing non-admin users to admin
    UPDATE public.user_repair_shops
    SET role = 'administrator', updated_at = NOW()
    WHERE repair_shop_id = shop_id AND role != 'administrator';
    
    RETURN json_build_object(
        'success', true,
        'message', 'Made all users administrators',
        'new_admins', user_count,
        'shop_id', shop_id
    );
    
EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'An error occurred: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- EXAMPLE USAGE FOR SINGLE SHOP
-- =============================================

/*
-- To assign administrator role to a specific user:
SELECT assign_admin_role_single_shop('<EMAIL>');

-- To make ALL users administrators (emergency fix):
SELECT make_all_users_admin();

-- To check the results:
SELECT * FROM public.user_repair_shops ORDER BY role, user_id;
*/

-- =============================================
-- VERIFICATION
-- =============================================

SELECT 'Single shop admin role verification script ready!' as status;
