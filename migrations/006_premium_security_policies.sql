/*
 * REPAIR QR NINJA - PREMIUM FEATURES SECURITY
 * Migration 006: Row Level Security for Premium Features
 * 
 * This migration sets up Row Level Security (RLS) policies for premium feature tables.
 * Ensures proper access control for time tracking, forms, action logs, and invoices.
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- ENABLE ROW LEVEL SECURITY
-- =============================================

-- Enable RLS on all premium feature tables
ALTER TABLE public.technician_time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.form_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.form_responses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.user_action_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoice_items ENABLE ROW LEVEL SECURITY;

-- =============================================
-- TECHNICIAN TIME ENTRIES POLICIES
-- =============================================

CREATE POLICY "Users can view time entries from their repair shop" ON public.technician_time_entries
    FOR SELECT USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert their own time entries" ON public.technician_time_entries
    FOR INSERT WITH CHECK (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
        AND user_id = auth.uid()
    );

CREATE POLICY "Users can update their own time entries" ON public.technician_time_entries
    FOR UPDATE USING (
        user_id = auth.uid()
    );

CREATE POLICY "Administrators can update any time entries in their shop" ON public.technician_time_entries
    FOR UPDATE USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
            AND role = 'administrator'
        )
    );

-- =============================================
-- FORM TEMPLATES POLICIES
-- =============================================

CREATE POLICY "Users can view form templates from their repair shop" ON public.form_templates
    FOR SELECT USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Administrators can manage form templates" ON public.form_templates
    FOR ALL USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
            AND role = 'administrator'
        )
    );

-- =============================================
-- FORM RESPONSES POLICIES
-- =============================================

CREATE POLICY "Users can view form responses from their repair shop" ON public.form_responses
    FOR SELECT USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert form responses for their repair shop" ON public.form_responses
    FOR INSERT WITH CHECK (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update form responses from their repair shop" ON public.form_responses
    FOR UPDATE USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

-- =============================================
-- USER ACTION LOGS POLICIES
-- =============================================

CREATE POLICY "Users can insert their own action logs" ON public.user_action_logs
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Administrators can view action logs from their repair shop" ON public.user_action_logs
    FOR SELECT USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
            AND role = 'administrator'
        )
    );

-- =============================================
-- INVOICES POLICIES
-- =============================================

CREATE POLICY "Users can view invoices from their repair shops" ON public.invoices
    FOR SELECT
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert invoices for their repair shops" ON public.invoices
    FOR INSERT
    WITH CHECK (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
        AND user_id = auth.uid()
    );

CREATE POLICY "Users can update invoices from their repair shops" ON public.invoices
    FOR UPDATE
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete invoices from their repair shops" ON public.invoices
    FOR DELETE
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

-- =============================================
-- INVOICE ITEMS POLICIES
-- =============================================

CREATE POLICY "Users can view invoice items from their repair shops" ON public.invoice_items
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.invoices i
            JOIN public.user_repair_shops urs ON i.repair_shop_id = urs.repair_shop_id
            WHERE i.id = invoice_items.invoice_id
            AND urs.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage invoice items for their repair shops" ON public.invoice_items
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.invoices i
            JOIN public.user_repair_shops urs ON i.repair_shop_id = urs.repair_shop_id
            WHERE i.id = invoice_items.invoice_id
            AND urs.user_id = auth.uid()
        )
    );

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;
