/*
 * REPAIR QR NINJA - FIX CREATE_USER_WITH_ROLE FUNCTION
 * 
 * This script adds the missing create_user_with_role function with the correct signature
 * that matches what the application expects.
 */

-- Start transaction
BEGIN;

-- Drop existing function if it exists with different signature
DROP FUNCTION IF EXISTS public.create_user_with_role(TEXT, TEXT, TEXT, UUID, TEXT);
DROP FUNCTION IF EXISTS public.create_user_with_role(UUID, TEXT, TEXT, TEXT, TEXT);

-- Create the function with the signature the application expects
CREATE OR REPLACE FUNCTION public.create_user_with_role(
    shop_id UUID,
    user_email TEXT,
    user_full_name TEXT,
    user_password TEXT,
    user_role TEXT DEFAULT 'technician'
)
RETURNS JSON AS $$
DECLARE
    current_user_role TEXT;
    new_user_id UUID;
    result JSON;
BEGIN
    -- Check if current user is administrator in the specified shop
    SELECT role INTO current_user_role
    FROM public.user_repair_shops
    WHERE user_id = auth.uid()
    AND repair_shop_id = shop_id;

    IF current_user_role != 'administrator' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Only administrators can create users'
        );
    END IF;

    -- Validate role
    IF user_role NOT IN ('administrator', 'technician', 'receptionist', 'cashier') THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Invalid role specified'
        );
    END IF;

    -- Check if user already exists
    SELECT id INTO new_user_id FROM auth.users WHERE email = user_email;

    IF new_user_id IS NOT NULL THEN
        -- User exists, check if already in shop
        IF EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_id = new_user_id AND repair_shop_id = shop_id
        ) THEN
            RETURN json_build_object(
                'success', false,
                'error', 'User with this email already exists in this repair shop'
            );
        ELSE
            -- Add existing user to shop
            INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role, full_name)
            VALUES (new_user_id, shop_id, user_role, user_full_name);

            RETURN json_build_object(
                'success', true,
                'message', 'Existing user added to repair shop successfully',
                'user_id', new_user_id,
                'action', 'added_existing_user'
            );
        END IF;
    END IF;

    -- For new user creation, we need to use Supabase's admin API
    -- This function will return instructions for the frontend to handle user creation
    RETURN json_build_object(
        'success', true,
        'action', 'create_new_user',
        'message', 'Ready to create new user',
        'user_data', json_build_object(
            'email', user_email,
            'full_name', user_full_name,
            'role', user_role,
            'shop_id', shop_id
        ),
        'instructions', 'Use Supabase Admin API to create user, then call complete_user_creation function'
    );

EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'An error occurred: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Also create an alternative function for inviting existing users
CREATE OR REPLACE FUNCTION public.invite_user_to_shop(
    shop_id UUID,
    user_email TEXT,
    user_full_name TEXT,
    user_role TEXT DEFAULT 'technician'
)
RETURNS JSON AS $$
DECLARE
    target_user_id UUID;
    current_user_role TEXT;
    result JSON;
BEGIN
    -- Check if current user is administrator in the specified shop
    SELECT role INTO current_user_role
    FROM public.user_repair_shops
    WHERE user_id = auth.uid() 
    AND repair_shop_id = shop_id;

    IF current_user_role != 'administrator' THEN
        RETURN json_build_object(
            'success', false, 
            'error', 'Only administrators can invite users'
        );
    END IF;

    -- Validate role
    IF user_role NOT IN ('administrator', 'technician', 'receptionist', 'cashier') THEN
        RETURN json_build_object(
            'success', false, 
            'error', 'Invalid role specified'
        );
    END IF;

    -- Find the user by email
    SELECT id INTO target_user_id 
    FROM auth.users 
    WHERE email = user_email;

    IF target_user_id IS NULL THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User with this email not found. User must sign up first.',
            'suggestion', 'Send them the signup link and then add them to the shop'
        );
    END IF;

    -- Check if user is already in the shop
    IF EXISTS (
        SELECT 1 FROM public.user_repair_shops 
        WHERE user_id = target_user_id AND repair_shop_id = shop_id
    ) THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User is already associated with this repair shop'
        );
    END IF;

    -- Add user to repair shop
    INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role, full_name)
    VALUES (target_user_id, shop_id, user_role, user_full_name);

    RETURN json_build_object(
        'success', true,
        'message', 'User successfully added to repair shop',
        'user_id', target_user_id
    );

EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'An error occurred: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to complete user creation after Supabase Auth user is created
CREATE OR REPLACE FUNCTION public.complete_user_creation(
    new_user_id UUID,
    shop_id UUID,
    user_full_name TEXT,
    user_role TEXT
)
RETURNS JSON AS $$
DECLARE
    current_user_role TEXT;
    result JSON;
BEGIN
    -- Check if current user is administrator in the specified shop
    SELECT role INTO current_user_role
    FROM public.user_repair_shops
    WHERE user_id = auth.uid()
    AND repair_shop_id = shop_id;

    IF current_user_role != 'administrator' THEN
        RETURN json_build_object(
            'success', false,
            'error', 'Only administrators can complete user creation'
        );
    END IF;

    -- Check if user is already in the shop
    IF EXISTS (
        SELECT 1 FROM public.user_repair_shops
        WHERE user_id = new_user_id AND repair_shop_id = shop_id
    ) THEN
        RETURN json_build_object(
            'success', false,
            'error', 'User is already associated with this repair shop'
        );
    END IF;

    -- Add user to repair shop
    INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role, full_name)
    VALUES (new_user_id, shop_id, user_role, user_full_name);

    RETURN json_build_object(
        'success', true,
        'message', 'User creation completed successfully',
        'user_id', new_user_id
    );

EXCEPTION
    WHEN OTHERS THEN
        RETURN json_build_object(
            'success', false,
            'error', 'An error occurred: ' || SQLERRM
        );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create a function to get the current user's default shop ID
CREATE OR REPLACE FUNCTION public.get_current_user_shop_id()
RETURNS UUID AS $$
DECLARE
    shop_id UUID;
BEGIN
    SELECT repair_shop_id INTO shop_id
    FROM public.user_repair_shops
    WHERE user_id = auth.uid()
    LIMIT 1;

    RETURN shop_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Commit the changes
COMMIT;

-- Test the function
SELECT 'create_user_with_role function added successfully!' as status;
