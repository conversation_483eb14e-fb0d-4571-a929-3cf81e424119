    /*
    * REPAIR QR NINJA - ADD MISSING FUNCTIONS
    * 
    * This script adds missing functions that the application expects.
    * Run this after fixing the RLS policies.
    */

    -- Start transaction
    BEGIN;

    -- =============================================
    -- USER MANAGEMENT FUNCTIONS
    -- =============================================

    -- Function to get users for a specific repair shop
    CREATE OR REPLACE FUNCTION public.get_shop_users(shop_id UUID)
    RETURNS TABLE (
        user_id UUID,
        email TEXT,
        full_name TEXT,
        role TEXT,
        created_at TIMESTAMP WITH TIME ZONE,
        updated_at TIMESTAMP WITH TIME ZONE
    ) AS $$
    BEGIN
        RETURN QUERY
        SELECT 
            urs.user_id,
            au.email,
            urs.full_name,
            urs.role,
            urs.created_at,
            urs.updated_at
        FROM public.user_repair_shops urs
        JOIN auth.users au ON urs.user_id = au.id
        WHERE urs.repair_shop_id = shop_id
        ORDER BY urs.created_at ASC;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;

    -- Function to get current user's repair shop info
    CREATE OR REPLACE FUNCTION public.get_user_repair_shop()
    RETURNS TABLE (
        repair_shop_id UUID,
        repair_shop_name TEXT,
        user_role TEXT,
        full_name TEXT
    ) AS $$
    BEGIN
        RETURN QUERY
        SELECT 
            urs.repair_shop_id,
            rs.name as repair_shop_name,
            urs.role as user_role,
            urs.full_name
        FROM public.user_repair_shops urs
        JOIN public.repair_shops rs ON urs.repair_shop_id = rs.id
        WHERE urs.user_id = auth.uid()
        LIMIT 1;
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;

    -- Function to update user role (for administrators)
    CREATE OR REPLACE FUNCTION public.update_user_role(
        target_user_id UUID,
        new_role TEXT,
        target_repair_shop_id UUID DEFAULT NULL
    )
    RETURNS JSON AS $$
    DECLARE
        current_user_role TEXT;
        shop_id UUID;
        result JSON;
    BEGIN
        -- Get the repair shop ID if not provided
        IF target_repair_shop_id IS NULL THEN
            SELECT repair_shop_id INTO shop_id 
            FROM public.user_repair_shops 
            WHERE user_id = auth.uid() 
            LIMIT 1;
        ELSE
            shop_id := target_repair_shop_id;
        END IF;

        -- Check if current user is administrator
        SELECT role INTO current_user_role
        FROM public.user_repair_shops
        WHERE user_id = auth.uid() 
        AND repair_shop_id = shop_id;

        IF current_user_role != 'administrator' THEN
            RETURN json_build_object(
                'success', false, 
                'error', 'Only administrators can update user roles'
            );
        END IF;

        -- Validate the new role
        IF new_role NOT IN ('administrator', 'technician', 'receptionist', 'cashier') THEN
            RETURN json_build_object(
                'success', false, 
                'error', 'Invalid role specified'
            );
        END IF;

        -- Update the user role
        UPDATE public.user_repair_shops
        SET role = new_role, updated_at = NOW()
        WHERE user_id = target_user_id 
        AND repair_shop_id = shop_id;

        -- Check if update was successful
        IF FOUND THEN
            result := json_build_object(
                'success', true,
                'message', 'User role updated successfully'
            );
        ELSE
            result := json_build_object(
                'success', false,
                'error', 'User not found in this repair shop'
            );
        END IF;

        RETURN result;

    EXCEPTION
        WHEN OTHERS THEN
            RETURN json_build_object(
                'success', false,
                'error', 'An error occurred: ' || SQLERRM
            );
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;

    -- Function to add user to repair shop
    CREATE OR REPLACE FUNCTION public.add_user_to_shop(
        user_email TEXT,
        user_full_name TEXT,
        user_role TEXT DEFAULT 'technician',
        target_repair_shop_id UUID DEFAULT NULL
    )
    RETURNS JSON AS $$
    DECLARE
        target_user_id UUID;
        shop_id UUID;
        current_user_role TEXT;
        result JSON;
    BEGIN
        -- Get the repair shop ID if not provided
        IF target_repair_shop_id IS NULL THEN
            SELECT repair_shop_id INTO shop_id 
            FROM public.user_repair_shops 
            WHERE user_id = auth.uid() 
            LIMIT 1;
        ELSE
            shop_id := target_repair_shop_id;
        END IF;

        -- Check if current user is administrator
        SELECT role INTO current_user_role
        FROM public.user_repair_shops
        WHERE user_id = auth.uid() 
        AND repair_shop_id = shop_id;

        IF current_user_role != 'administrator' THEN
            RETURN json_build_object(
                'success', false, 
                'error', 'Only administrators can add users'
            );
        END IF;

        -- Find the user by email
        SELECT id INTO target_user_id 
        FROM auth.users 
        WHERE email = user_email;

        IF target_user_id IS NULL THEN
            RETURN json_build_object(
                'success', false,
                'error', 'User with this email not found. User must sign up first.'
            );
        END IF;

        -- Check if user is already in the shop
        IF EXISTS (
            SELECT 1 FROM public.user_repair_shops 
            WHERE user_id = target_user_id AND repair_shop_id = shop_id
        ) THEN
            RETURN json_build_object(
                'success', false,
                'error', 'User is already associated with this repair shop'
            );
        END IF;

        -- Add user to repair shop
        INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role, full_name)
        VALUES (target_user_id, shop_id, user_role, user_full_name);

        RETURN json_build_object(
            'success', true,
            'message', 'User added to repair shop successfully'
        );

    EXCEPTION
        WHEN OTHERS THEN
            RETURN json_build_object(
                'success', false,
                'error', 'An error occurred: ' || SQLERRM
            );
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;

    -- Function to remove user from repair shop
    CREATE OR REPLACE FUNCTION public.remove_user_from_shop(
        target_user_id UUID,
        target_repair_shop_id UUID DEFAULT NULL
    )
    RETURNS JSON AS $$
    DECLARE
        shop_id UUID;
        current_user_role TEXT;
        target_user_role TEXT;
        admin_count INTEGER;
        result JSON;
    BEGIN
        -- Get the repair shop ID if not provided
        IF target_repair_shop_id IS NULL THEN
            SELECT repair_shop_id INTO shop_id 
            FROM public.user_repair_shops 
            WHERE user_id = auth.uid() 
            LIMIT 1;
        ELSE
            shop_id := target_repair_shop_id;
        END IF;

        -- Check if current user is administrator
        SELECT role INTO current_user_role
        FROM public.user_repair_shops
        WHERE user_id = auth.uid() 
        AND repair_shop_id = shop_id;

        IF current_user_role != 'administrator' THEN
            RETURN json_build_object(
                'success', false, 
                'error', 'Only administrators can remove users'
            );
        END IF;

        -- Get target user role
        SELECT role INTO target_user_role
        FROM public.user_repair_shops
        WHERE user_id = target_user_id 
        AND repair_shop_id = shop_id;

        -- If removing an administrator, ensure there's at least one other admin
        IF target_user_role = 'administrator' THEN
            SELECT COUNT(*) INTO admin_count
            FROM public.user_repair_shops
            WHERE repair_shop_id = shop_id 
            AND role = 'administrator'
            AND user_id != target_user_id;

            IF admin_count = 0 THEN
                RETURN json_build_object(
                    'success', false,
                    'error', 'Cannot remove the last administrator'
                );
            END IF;
        END IF;

        -- Remove user from repair shop
        DELETE FROM public.user_repair_shops
        WHERE user_id = target_user_id 
        AND repair_shop_id = shop_id;

        IF FOUND THEN
            RETURN json_build_object(
                'success', true,
                'message', 'User removed from repair shop successfully'
            );
        ELSE
            RETURN json_build_object(
                'success', false,
                'error', 'User not found in this repair shop'
            );
        END IF;

    EXCEPTION
        WHEN OTHERS THEN
            RETURN json_build_object(
                'success', false,
                'error', 'An error occurred: ' || SQLERRM
            );
    END;
    $$ LANGUAGE plpgsql SECURITY DEFINER;

    -- =============================================
    -- COMMIT CHANGES
    -- =============================================

    COMMIT;

    -- Verification
    SELECT 'Missing functions added successfully!' as status;
