/*
 * REPAIR QR NINJA - STORE BRANCHES
 * Migration 006: Store Branches Implementation
 * 
 * This migration adds store branches functionality including:
 * - Store branches table
 * - Branch assignment to users
 * - Branch-based data filtering
 * - Admin branch management capabilities
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- STORE BRANCHES TABLE
-- =============================================

-- Create the store_branches table
CREATE TABLE IF NOT EXISTS public.store_branches (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    code TEXT NOT NULL UNIQUE, -- Short code for the branch (e.g., 'NYC01', 'LA02')
    address TEXT,
    phone TEXT,
    email TEXT,
    manager_name TEXT,
    is_active BOOLEAN NOT NULL DEFAULT true,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_store_branches_repair_shop_id ON public.store_branches(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_store_branches_code ON public.store_branches(code);
CREATE INDEX IF NOT EXISTS idx_store_branches_active ON public.store_branches(is_active);

-- =============================================
-- UPDATE USER_REPAIR_SHOPS TABLE
-- =============================================

-- Add branch_id column to user_repair_shops table
ALTER TABLE public.user_repair_shops 
ADD COLUMN IF NOT EXISTS branch_id UUID REFERENCES public.store_branches(id) ON DELETE SET NULL;

-- Create index for branch_id
CREATE INDEX IF NOT EXISTS idx_user_repair_shops_branch_id ON public.user_repair_shops(branch_id);

-- =============================================
-- UPDATE EXISTING TABLES FOR BRANCH SUPPORT
-- =============================================

-- Add branch_id to repairs table
ALTER TABLE public.repairs 
ADD COLUMN IF NOT EXISTS branch_id UUID REFERENCES public.store_branches(id) ON DELETE SET NULL;

-- Create index for repairs branch_id
CREATE INDEX IF NOT EXISTS idx_repairs_branch_id ON public.repairs(branch_id);

-- Add branch_id to invoices table (if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'invoices' AND table_schema = 'public') THEN
        ALTER TABLE public.invoices 
        ADD COLUMN IF NOT EXISTS branch_id UUID REFERENCES public.store_branches(id) ON DELETE SET NULL;
        
        CREATE INDEX IF NOT EXISTS idx_invoices_branch_id ON public.invoices(branch_id);
    END IF;
END $$;

-- Add branch_id to stock_items table (if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'stock_items' AND table_schema = 'public') THEN
        ALTER TABLE public.stock_items 
        ADD COLUMN IF NOT EXISTS branch_id UUID REFERENCES public.store_branches(id) ON DELETE SET NULL;
        
        CREATE INDEX IF NOT EXISTS idx_stock_items_branch_id ON public.stock_items(branch_id);
    END IF;
END $$;

-- Add branch_id to technician_time_entries table (if it exists)
DO $$ 
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.tables WHERE table_name = 'technician_time_entries' AND table_schema = 'public') THEN
        ALTER TABLE public.technician_time_entries 
        ADD COLUMN IF NOT EXISTS branch_id UUID REFERENCES public.store_branches(id) ON DELETE SET NULL;
        
        CREATE INDEX IF NOT EXISTS idx_technician_time_entries_branch_id ON public.technician_time_entries(branch_id);
    END IF;
END $$;

-- =============================================
-- FUNCTIONS
-- =============================================

-- Function to get user's branch information
CREATE OR REPLACE FUNCTION get_user_branch_info(p_user_id UUID)
RETURNS TABLE (
    branch_id UUID,
    branch_name TEXT,
    branch_code TEXT,
    repair_shop_id UUID
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sb.id as branch_id,
        sb.name as branch_name,
        sb.code as branch_code,
        sb.repair_shop_id
    FROM public.user_repair_shops urs
    JOIN public.store_branches sb ON urs.branch_id = sb.id
    WHERE urs.user_id = p_user_id
    AND sb.is_active = true
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get all branches for a repair shop
CREATE OR REPLACE FUNCTION get_repair_shop_branches(p_repair_shop_id UUID)
RETURNS TABLE (
    id UUID,
    name TEXT,
    code TEXT,
    address TEXT,
    phone TEXT,
    email TEXT,
    manager_name TEXT,
    is_active BOOLEAN,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        sb.id,
        sb.name,
        sb.code,
        sb.address,
        sb.phone,
        sb.email,
        sb.manager_name,
        sb.is_active,
        sb.created_at,
        sb.updated_at
    FROM public.store_branches sb
    WHERE sb.repair_shop_id = p_repair_shop_id
    ORDER BY sb.name;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to assign user to branch
CREATE OR REPLACE FUNCTION assign_user_to_branch(
    p_user_id UUID,
    p_repair_shop_id UUID,
    p_branch_id UUID
)
RETURNS BOOLEAN AS $$
DECLARE
    branch_exists BOOLEAN;
BEGIN
    -- Check if branch exists and belongs to the repair shop
    SELECT EXISTS(
        SELECT 1 FROM public.store_branches 
        WHERE id = p_branch_id 
        AND repair_shop_id = p_repair_shop_id 
        AND is_active = true
    ) INTO branch_exists;
    
    IF NOT branch_exists THEN
        RETURN FALSE;
    END IF;
    
    -- Update user's branch assignment
    UPDATE public.user_repair_shops 
    SET branch_id = p_branch_id,
        updated_at = NOW()
    WHERE user_id = p_user_id 
    AND repair_shop_id = p_repair_shop_id;
    
    RETURN FOUND;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- TRIGGERS
-- =============================================

-- Create trigger for updated_at on store_branches
CREATE TRIGGER trigger_update_store_branches_updated_at
    BEFORE UPDATE ON public.store_branches
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- ROW LEVEL SECURITY (RLS)
-- =============================================

-- Enable RLS on store_branches
ALTER TABLE public.store_branches ENABLE ROW LEVEL SECURITY;

-- Policy for store_branches: Users can only see branches from their repair shop
CREATE POLICY store_branches_select_policy ON public.store_branches
    FOR SELECT
    USING (
        repair_shop_id IN (
            SELECT repair_shop_id 
            FROM public.user_repair_shops 
            WHERE user_id = auth.uid()
        )
    );

-- Policy for store_branches: Only admins can insert/update/delete
CREATE POLICY store_branches_admin_policy ON public.store_branches
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 
            FROM public.user_repair_shops 
            WHERE user_id = auth.uid() 
            AND repair_shop_id = store_branches.repair_shop_id
            AND role = 'administrator'
        )
    );

-- =============================================
-- GRANT PERMISSIONS
-- =============================================

-- Grant permissions on functions
GRANT EXECUTE ON FUNCTION get_user_branch_info(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_repair_shop_branches(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION assign_user_to_branch(UUID, UUID, UUID) TO authenticated;

-- Grant permissions on store_branches table
GRANT SELECT ON public.store_branches TO authenticated;
GRANT INSERT, UPDATE, DELETE ON public.store_branches TO authenticated;

-- =============================================
-- SAMPLE DATA (OPTIONAL)
-- =============================================

-- Insert sample branches for existing repair shops
-- Note: This will only work if you have existing repair shops
-- You can customize this section based on your needs

/*
-- Example: Insert sample branches for the first repair shop
INSERT INTO public.store_branches (name, code, address, phone, repair_shop_id)
SELECT 
    'Main Branch',
    'MAIN01',
    '123 Main Street, City, State',
    '******-0101',
    id
FROM public.repair_shops 
LIMIT 1
ON CONFLICT (code) DO NOTHING;

INSERT INTO public.store_branches (name, code, address, phone, repair_shop_id)
SELECT 
    'Downtown Branch',
    'DOWN01',
    '456 Downtown Ave, City, State',
    '******-0102',
    id
FROM public.repair_shops 
LIMIT 1
ON CONFLICT (code) DO NOTHING;

INSERT INTO public.store_branches (name, code, address, phone, repair_shop_id)
SELECT 
    'Mall Branch',
    'MALL01',
    '789 Shopping Mall, City, State',
    '******-0103',
    id
FROM public.repair_shops 
LIMIT 1
ON CONFLICT (code) DO NOTHING;
*/

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;