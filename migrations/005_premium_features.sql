/*
 * REPAIR QR NINJA - PREMIUM FEATURES
 * Migration 005: Premium Plan Features
 * 
 * This migration adds premium features including:
 * - Technician time tracking
 * - Customizable forms
 * - User action logging
 * - Invoice generation
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- TECHNICIAN TIME TRACKING
-- =============================================

-- Create the technician_time_entries table
CREATE TABLE IF NOT EXISTS public.technician_time_entries (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_id UUID REFERENCES public.repairs(id) ON DELETE SET NULL,
    start_time TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT NOW(),
    end_time TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_time_entries_user_id ON public.technician_time_entries(user_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_repair_id ON public.technician_time_entries(repair_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_repair_shop_id ON public.technician_time_entries(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_time_entries_start_time ON public.technician_time_entries(start_time);

-- =============================================
-- CUSTOMIZABLE FORMS
-- =============================================

-- Create the form_templates table
CREATE TABLE IF NOT EXISTS public.form_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    device_type TEXT NOT NULL,
    fields JSONB NOT NULL,
    is_active BOOLEAN NOT NULL DEFAULT true,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create the form_responses table
CREATE TABLE IF NOT EXISTS public.form_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repair_id UUID NOT NULL REFERENCES public.repairs(id) ON DELETE CASCADE,
    template_id UUID NOT NULL REFERENCES public.form_templates(id) ON DELETE CASCADE,
    responses JSONB NOT NULL,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_form_templates_repair_shop_id ON public.form_templates(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_form_templates_device_type ON public.form_templates(device_type);
CREATE INDEX IF NOT EXISTS idx_form_responses_repair_id ON public.form_responses(repair_id);
CREATE INDEX IF NOT EXISTS idx_form_responses_template_id ON public.form_responses(template_id);
CREATE INDEX IF NOT EXISTS idx_form_responses_repair_shop_id ON public.form_responses(repair_shop_id);

-- =============================================
-- USER ACTION LOGGING
-- =============================================

-- Create an action log table for traceability
CREATE TABLE IF NOT EXISTS public.user_action_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    details JSONB,
    repair_shop_id UUID REFERENCES public.repair_shops(id) ON DELETE SET NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_user_action_logs_user_id ON public.user_action_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_action_logs_created_at ON public.user_action_logs(created_at);
CREATE INDEX IF NOT EXISTS idx_user_action_logs_repair_shop_id ON public.user_action_logs(repair_shop_id);

-- =============================================
-- INVOICE GENERATION
-- =============================================

-- Create invoices table
CREATE TABLE IF NOT EXISTS public.invoices (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    invoice_number TEXT NOT NULL UNIQUE,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    customer_name TEXT NOT NULL,
    customer_phone TEXT,
    subtotal DECIMAL(10,2) NOT NULL DEFAULT 0,
    discount_amount DECIMAL(10,2) NOT NULL DEFAULT 0,
    total_amount DECIMAL(10,2) NOT NULL,
    status TEXT NOT NULL DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'paid')),
    notes TEXT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create invoice_items table
CREATE TABLE IF NOT EXISTS public.invoice_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    invoice_id UUID NOT NULL REFERENCES public.invoices(id) ON DELETE CASCADE,
    repair_id UUID NOT NULL REFERENCES public.repairs(id) ON DELETE CASCADE,
    description TEXT NOT NULL,
    amount DECIMAL(10,2) NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for invoices
CREATE INDEX IF NOT EXISTS idx_invoices_repair_shop_id ON public.invoices(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_invoices_created_at ON public.invoices(created_at);
CREATE INDEX IF NOT EXISTS idx_invoices_user_id ON public.invoices(user_id);
CREATE INDEX IF NOT EXISTS idx_invoice_items_invoice_id ON public.invoice_items(invoice_id);
CREATE INDEX IF NOT EXISTS idx_invoice_items_repair_id ON public.invoice_items(repair_id);

-- =============================================
-- FUNCTIONS
-- =============================================

-- Create function for updated_at timestamp on forms
CREATE OR REPLACE FUNCTION update_form_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Function to generate invoice number
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    result_number TEXT;
BEGIN
    -- Get the next invoice number
    SELECT COALESCE(MAX(CAST(SUBSTRING(i.invoice_number FROM '[0-9]+$') AS INTEGER)), 0) + 1
    INTO next_number
    FROM public.invoices i
    WHERE i.invoice_number ~ '^INV-[0-9]+$';
    
    -- Format as INV-000001
    result_number := 'INV-' || LPAD(next_number::TEXT, 6, '0');
    
    RETURN result_number;
END;
$$ LANGUAGE plpgsql;

-- Function to get technician time tracking data
CREATE OR REPLACE FUNCTION get_technician_time_tracking(
    p_user_id UUID,
    p_repair_shop_id UUID,
    p_start_date DATE DEFAULT NULL,
    p_end_date DATE DEFAULT NULL
)
RETURNS TABLE (
    repair_id UUID,
    customer_name TEXT,
    phone_model TEXT,
    total_time_minutes INTEGER,
    in_progress_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        r.id as repair_id,
        r.customer_name,
        r.phone_model,
        CASE 
            WHEN r.completed_at IS NOT NULL AND r.in_progress_at IS NOT NULL THEN
                EXTRACT(EPOCH FROM (r.completed_at - r.in_progress_at))::INTEGER / 60
            ELSE NULL
        END as total_time_minutes,
        r.in_progress_at,
        r.completed_at
    FROM public.repairs r
    WHERE r.assigned_technician = p_user_id
        AND r.repair_shop_id = p_repair_shop_id
        AND (p_start_date IS NULL OR DATE(r.created_at) >= p_start_date)
        AND (p_end_date IS NULL OR DATE(r.created_at) <= p_end_date)
        AND r.in_progress_at IS NOT NULL
    ORDER BY r.created_at DESC;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- TRIGGERS
-- =============================================

-- Create triggers for updated_at on forms
CREATE TRIGGER trigger_update_form_templates_updated_at
    BEFORE UPDATE ON public.form_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_form_updated_at_column();

CREATE TRIGGER trigger_update_form_responses_updated_at
    BEFORE UPDATE ON public.form_responses
    FOR EACH ROW
    EXECUTE FUNCTION update_form_updated_at_column();

-- Create trigger for invoices updated_at
CREATE TRIGGER trigger_update_invoices_updated_at
    BEFORE UPDATE ON public.invoices
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;
