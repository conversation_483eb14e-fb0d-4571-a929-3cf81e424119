/*
 * REPAIR QR NINJA - STOCK MANAGEMENT SYSTEM
 * Migration 003: Stock Management and POS Tables
 * 
 * This migration creates the complete stock management system including:
 * - Product categories and products
 * - Sales and sale items
 * - Stock movements and inventory tracking
 * - One-time items for unique products
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- PRODUCT MANAGEMENT TABLES
-- =============================================

-- Create product_categories table
CREATE TABLE IF NOT EXISTS public.product_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    color TEXT,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create products table
CREATE TABLE IF NOT EXISTS public.products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    sku TEXT NOT NULL,
    barcode TEXT,
    category_id UUID NOT NULL REFERENCES public.product_categories(id) ON DELETE CASCADE,
    price NUMERIC NOT NULL CHECK (price >= 0),
    cost NUMERIC NOT NULL CHECK (cost >= 0),
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    min_stock_level INTEGER NOT NULL DEFAULT 0 CHECK (min_stock_level >= 0),
    max_stock_level INTEGER CHECK (max_stock_level IS NULL OR max_stock_level >= min_stock_level),
    is_active BOOLEAN NOT NULL DEFAULT true,
    image_url TEXT,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(sku, repair_shop_id),
    UNIQUE(barcode, repair_shop_id) -- Allow NULL barcodes but unique when present
);

-- =============================================
-- SALES AND POS TABLES
-- =============================================

-- Create sales table
CREATE TABLE IF NOT EXISTS public.sales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_number TEXT NOT NULL,
    customer_name TEXT,
    customer_phone TEXT,
    subtotal NUMERIC NOT NULL DEFAULT 0,
    discount_amount NUMERIC NOT NULL DEFAULT 0,
    total_amount NUMERIC NOT NULL,
    payment_method TEXT NOT NULL CHECK (payment_method IN ('cash', 'card', 'transfer', 'check')),
    notes TEXT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(sale_number, repair_shop_id)
);

-- Create sale_items table
CREATE TABLE IF NOT EXISTS public.sale_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_id UUID NOT NULL REFERENCES public.sales(id) ON DELETE CASCADE,
    product_id UUID REFERENCES public.products(id) ON DELETE SET NULL,
    one_time_item_id UUID, -- Will reference one_time_items table
    product_name TEXT NOT NULL,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price NUMERIC NOT NULL CHECK (unit_price >= 0),
    total_price NUMERIC GENERATED ALWAYS AS (quantity * unit_price) STORED,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- =============================================
-- INVENTORY TRACKING TABLES
-- =============================================

-- Create stock_movements table for inventory tracking
CREATE TABLE IF NOT EXISTS public.stock_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('in', 'out', 'adjustment')),
    quantity INTEGER NOT NULL,
    reason TEXT NOT NULL,
    reference TEXT, -- Sale ID, Purchase ID, etc.
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create one_time_items table for unique products
CREATE TABLE IF NOT EXISTS public.one_time_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    serial_number TEXT,
    device_type TEXT,
    condition TEXT CHECK (condition IN ('new', 'used', 'refurbished', 'damaged')),
    purchase_price NUMERIC CHECK (purchase_price >= 0),
    selling_price NUMERIC CHECK (selling_price >= 0),
    is_sold BOOLEAN NOT NULL DEFAULT false,
    sold_at TIMESTAMP WITH TIME ZONE,
    notes TEXT,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create repair_stock_items table to link repairs with stock items
CREATE TABLE IF NOT EXISTS public.repair_stock_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    repair_id UUID NOT NULL REFERENCES public.repairs(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,3) NOT NULL CHECK (unit_price >= 0),
    total_price DECIMAL(10,3) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- =============================================
-- INDEXES FOR PERFORMANCE
-- =============================================

-- Product categories indexes
CREATE INDEX IF NOT EXISTS idx_product_categories_repair_shop_id ON public.product_categories(repair_shop_id);

-- Products indexes
CREATE INDEX IF NOT EXISTS idx_products_category_id ON public.products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_repair_shop_id ON public.products(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_products_sku ON public.products(sku);
CREATE INDEX IF NOT EXISTS idx_products_barcode ON public.products(barcode) WHERE barcode IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_products_is_active ON public.products(is_active);

-- Sales indexes
CREATE INDEX IF NOT EXISTS idx_sales_repair_shop_id ON public.sales(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_sales_user_id ON public.sales(user_id);
CREATE INDEX IF NOT EXISTS idx_sales_created_at ON public.sales(created_at);
CREATE INDEX IF NOT EXISTS idx_sales_sale_number ON public.sales(sale_number);

-- Sale items indexes
CREATE INDEX IF NOT EXISTS idx_sale_items_sale_id ON public.sale_items(sale_id);
CREATE INDEX IF NOT EXISTS idx_sale_items_product_id ON public.sale_items(product_id);
CREATE INDEX IF NOT EXISTS idx_sale_items_one_time_item_id ON public.sale_items(one_time_item_id);
CREATE INDEX IF NOT EXISTS idx_sale_items_repair_shop_id ON public.sale_items(repair_shop_id);

-- Stock movements indexes
CREATE INDEX IF NOT EXISTS idx_stock_movements_product_id ON public.stock_movements(product_id);
CREATE INDEX IF NOT EXISTS idx_stock_movements_user_id ON public.stock_movements(user_id);
CREATE INDEX IF NOT EXISTS idx_stock_movements_repair_shop_id ON public.stock_movements(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_stock_movements_created_at ON public.stock_movements(created_at);
CREATE INDEX IF NOT EXISTS idx_stock_movements_type ON public.stock_movements(type);

-- One-time items indexes
CREATE INDEX IF NOT EXISTS idx_one_time_items_product_id ON public.one_time_items(product_id);
CREATE INDEX IF NOT EXISTS idx_one_time_items_is_sold ON public.one_time_items(is_sold);
CREATE INDEX IF NOT EXISTS idx_one_time_items_device_type ON public.one_time_items(device_type);
CREATE INDEX IF NOT EXISTS idx_one_time_items_serial_number ON public.one_time_items(serial_number);
CREATE INDEX IF NOT EXISTS idx_one_time_items_repair_shop_id ON public.one_time_items(repair_shop_id);

-- Repair stock items indexes
CREATE INDEX IF NOT EXISTS idx_repair_stock_items_repair_id ON public.repair_stock_items(repair_id);
CREATE INDEX IF NOT EXISTS idx_repair_stock_items_product_id ON public.repair_stock_items(product_id);
CREATE INDEX IF NOT EXISTS idx_repair_stock_items_repair_shop_id ON public.repair_stock_items(repair_shop_id);

-- =============================================
-- SEQUENCES AND FUNCTIONS
-- =============================================

-- Create sequence for sale numbers
CREATE SEQUENCE IF NOT EXISTS sale_number_seq START WITH 1;

-- Function to generate sale number
CREATE OR REPLACE FUNCTION generate_sale_number()
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    result_number TEXT;
BEGIN
    -- Get the next sale number
    next_number := nextval('sale_number_seq');

    -- Format as SALE-000001
    result_number := 'SALE-' || LPAD(next_number::TEXT, 6, '0');

    RETURN result_number;
END;
$$ LANGUAGE plpgsql;

-- Function to update stock quantity when sale items are added/removed
CREATE OR REPLACE FUNCTION update_stock_on_sale()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Decrease stock when sale item is added
        UPDATE public.products
        SET stock_quantity = stock_quantity - NEW.quantity,
            updated_at = NOW()
        WHERE id = NEW.product_id;

        -- Create stock movement record
        INSERT INTO public.stock_movements (
            product_id, type, quantity, reason, reference, user_id, repair_shop_id
        )
        SELECT
            NEW.product_id,
            'out',
            NEW.quantity,
            'Sale',
            NEW.sale_id::TEXT,
            s.user_id,
            NEW.repair_shop_id
        FROM public.sales s
        WHERE s.id = NEW.sale_id;

        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Increase stock when sale item is removed
        UPDATE public.products
        SET stock_quantity = stock_quantity + OLD.quantity,
            updated_at = NOW()
        WHERE id = OLD.product_id;

        -- Create stock movement record
        INSERT INTO public.stock_movements (
            product_id, type, quantity, reason, reference, user_id, repair_shop_id
        )
        SELECT
            OLD.product_id,
            'in',
            OLD.quantity,
            'Sale Cancelled',
            OLD.sale_id::TEXT,
            s.user_id,
            OLD.repair_shop_id
        FROM public.sales s
        WHERE s.id = OLD.sale_id;

        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Function to update one-time items updated_at timestamp
CREATE OR REPLACE FUNCTION update_one_time_items_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- =============================================
-- TRIGGERS
-- =============================================

-- Create triggers for updated_at columns
CREATE TRIGGER trigger_update_product_categories_updated_at
    BEFORE UPDATE ON public.product_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_products_updated_at
    BEFORE UPDATE ON public.products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_sales_updated_at
    BEFORE UPDATE ON public.sales
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_update_one_time_items_updated_at
    BEFORE UPDATE ON public.one_time_items
    FOR EACH ROW
    EXECUTE FUNCTION update_one_time_items_updated_at();

CREATE TRIGGER update_repair_stock_items_updated_at
    BEFORE UPDATE ON public.repair_stock_items
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for stock management
CREATE TRIGGER trigger_update_stock_on_sale
    AFTER INSERT OR DELETE ON public.sale_items
    FOR EACH ROW
    EXECUTE FUNCTION update_stock_on_sale();

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;
