/*
 * REPAIR QR NINJA - REALTIME AND FINAL SETUP
 * Migration 008: Enable Real-time Functionality and Final Configuration
 * 
 * This migration enables real-time functionality for all tables and performs
 * final setup tasks including data population and system configuration.
 */

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- REAL-TIME FUNCTIONALITY
-- =============================================

-- Drop existing publication if it exists
DROP PUBLICATION IF EXISTS supabase_realtime;

-- Create publication that tracks all changes to all tables
CREATE PUBLICATION supabase_realtime FOR TABLE 
    public.repairs,
    public.repair_shops,
    public.user_repair_shops,
    public.product_categories,
    public.products,
    public.sales,
    public.sale_items,
    public.stock_movements,
    public.one_time_items,
    public.repair_stock_items,
    public.technician_time_entries,
    public.form_templates,
    public.form_responses,
    public.user_action_logs,
    public.invoices,
    public.invoice_items;

-- =============================================
-- SAMPLE DATA POPULATION
-- =============================================

-- Insert sample product categories
INSERT INTO public.product_categories (name, description, icon, color, repair_shop_id)
SELECT 
    'Phone Screens', 
    'Replacement screens for various phone models', 
    'smartphone', 
    '#3B82F6',
    rs.id
FROM public.repair_shops rs
WHERE NOT EXISTS (
    SELECT 1 FROM public.product_categories 
    WHERE name = 'Phone Screens' AND repair_shop_id = rs.id
);

INSERT INTO public.product_categories (name, description, icon, color, repair_shop_id)
SELECT 
    'Batteries', 
    'Replacement batteries for phones and tablets', 
    'battery', 
    '#10B981',
    rs.id
FROM public.repair_shops rs
WHERE NOT EXISTS (
    SELECT 1 FROM public.product_categories 
    WHERE name = 'Batteries' AND repair_shop_id = rs.id
);

INSERT INTO public.product_categories (name, description, icon, color, repair_shop_id)
SELECT 
    'Accessories', 
    'Phone cases, chargers, and other accessories', 
    'cable', 
    '#F59E0B',
    rs.id
FROM public.repair_shops rs
WHERE NOT EXISTS (
    SELECT 1 FROM public.product_categories 
    WHERE name = 'Accessories' AND repair_shop_id = rs.id
);

-- Insert sample products
INSERT INTO public.products (name, description, sku, category_id, price, cost, stock_quantity, min_stock_level, repair_shop_id)
SELECT 
    'iPhone 12 Screen', 
    'Original quality replacement screen for iPhone 12', 
    'IP12-SCR-001',
    pc.id,
    150.00,
    80.00,
    10,
    5,
    rs.id
FROM public.repair_shops rs
JOIN public.product_categories pc ON pc.repair_shop_id = rs.id
WHERE pc.name = 'Phone Screens'
AND NOT EXISTS (
    SELECT 1 FROM public.products 
    WHERE sku = 'IP12-SCR-001' AND repair_shop_id = rs.id
);

INSERT INTO public.products (name, description, sku, category_id, price, cost, stock_quantity, min_stock_level, repair_shop_id)
SELECT 
    'Samsung Galaxy S21 Battery', 
    'High capacity replacement battery for Galaxy S21', 
    'SGS21-BAT-001',
    pc.id,
    45.00,
    25.00,
    15,
    8,
    rs.id
FROM public.repair_shops rs
JOIN public.product_categories pc ON pc.repair_shop_id = rs.id
WHERE pc.name = 'Batteries'
AND NOT EXISTS (
    SELECT 1 FROM public.products 
    WHERE sku = 'SGS21-BAT-001' AND repair_shop_id = rs.id
);

-- =============================================
-- SAMPLE FORM TEMPLATES
-- =============================================

-- Insert sample form template for phone intake
INSERT INTO public.form_templates (name, description, device_type, fields, repair_shop_id)
SELECT 
    'Phone Intake Form',
    'Standard intake form for phone repairs',
    'phone',
    '[
        {
            "id": "device_condition",
            "type": "select",
            "label": "Device Condition",
            "required": true,
            "options": ["Excellent", "Good", "Fair", "Poor", "Damaged"]
        },
        {
            "id": "accessories_included",
            "type": "checkbox",
            "label": "Accessories Included",
            "options": ["Charger", "Case", "Screen Protector", "Earphones"]
        },
        {
            "id": "passcode_provided",
            "type": "radio",
            "label": "Passcode Provided",
            "required": true,
            "options": ["Yes", "No"]
        },
        {
            "id": "backup_status",
            "type": "radio",
            "label": "Data Backup Status",
            "required": true,
            "options": ["Backed up", "Not backed up", "Customer declined"]
        },
        {
            "id": "special_instructions",
            "type": "textarea",
            "label": "Special Instructions",
            "placeholder": "Any special handling instructions..."
        }
    ]'::jsonb,
    rs.id
FROM public.repair_shops rs
WHERE NOT EXISTS (
    SELECT 1 FROM public.form_templates 
    WHERE name = 'Phone Intake Form' AND repair_shop_id = rs.id
);

-- =============================================
-- SYSTEM MAINTENANCE FUNCTIONS
-- =============================================

-- Function to clean up old action logs (keep last 90 days)
CREATE OR REPLACE FUNCTION cleanup_old_action_logs()
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER;
BEGIN
    DELETE FROM public.user_action_logs
    WHERE created_at < NOW() - INTERVAL '90 days';
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
END;
$$ LANGUAGE plpgsql;

-- Function to update repair status timestamps
CREATE OR REPLACE FUNCTION update_repair_status_timestamps()
RETURNS TRIGGER AS $$
BEGIN
    -- Update in_progress_at when status changes to inProgress
    IF NEW.status = 'inProgress' AND OLD.status != 'inProgress' THEN
        NEW.in_progress_at = NOW();
    END IF;
    
    -- Update completed_at when status changes to completed
    IF NEW.status = 'completed' AND OLD.status != 'completed' THEN
        NEW.completed_at = NOW();
    END IF;
    
    -- Clear completed_at if status changes from completed to something else
    IF NEW.status != 'completed' AND OLD.status = 'completed' THEN
        NEW.completed_at = NULL;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for repair status timestamps
DROP TRIGGER IF EXISTS update_repair_status_timestamps_trigger ON public.repairs;
CREATE TRIGGER update_repair_status_timestamps_trigger
    BEFORE UPDATE ON public.repairs
    FOR EACH ROW
    EXECUTE FUNCTION update_repair_status_timestamps();

-- =============================================
-- FINAL SYSTEM CHECKS AND SETUP
-- =============================================

-- Ensure all existing repairs have ticket numbers
DO $$
DECLARE
    repair_record RECORD;
BEGIN
    FOR repair_record IN 
        SELECT id FROM public.repairs 
        WHERE ticket_number IS NULL
        ORDER BY created_at ASC
    LOOP
        UPDATE public.repairs 
        SET ticket_number = nextval('repair_ticket_number_seq')
        WHERE id = repair_record.id;
    END LOOP;
END $$;

-- Run user role check and fix
SELECT check_and_fix_user_role();

-- Create initial action log entry
INSERT INTO public.user_action_logs (user_id, action, details)
VALUES (
    NULL,
    'system_initialized',
    json_build_object(
        'timestamp', NOW(),
        'migration_version', '008',
        'message', 'Database fully initialized with all features'
    )
);

-- =============================================
-- PERFORMANCE OPTIMIZATION
-- =============================================

-- Analyze all tables for better query planning
ANALYZE public.repairs;
ANALYZE public.repair_shops;
ANALYZE public.user_repair_shops;
ANALYZE public.products;
ANALYZE public.product_categories;
ANALYZE public.sales;
ANALYZE public.sale_items;
ANALYZE public.stock_movements;
ANALYZE public.technician_time_entries;
ANALYZE public.form_templates;
ANALYZE public.form_responses;
ANALYZE public.user_action_logs;
ANALYZE public.invoices;
ANALYZE public.invoice_items;

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;

-- =============================================
-- POST-MIGRATION NOTES
-- =============================================

/*
 * MIGRATION COMPLETE!
 * 
 * The Repair QR Ninja database has been fully set up with:
 * 
 * ✅ Core repair management system
 * ✅ User management and RBAC
 * ✅ Stock management and POS
 * ✅ Premium features (time tracking, forms, invoices)
 * ✅ Complete security policies
 * ✅ Real-time functionality
 * ✅ Sample data and templates
 * 
 * Next steps:
 * 1. Configure your Supabase project settings
 * 2. Set up authentication providers
 * 3. Configure email templates for notifications
 * 4. Customize the default repair shop information
 * 5. Add your products and categories
 * 
 * For maintenance:
 * - Run cleanup_old_action_logs() periodically
 * - Monitor table sizes and performance
 * - Update sample data as needed
 */
