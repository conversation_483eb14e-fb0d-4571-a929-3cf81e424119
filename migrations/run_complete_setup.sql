/*
 * REPAIR QR NINJA - COMPLETE SETUP RUNNER
 * 
 * This script runs all three parts of the complete database setup in sequence.
 * Use this for a one-time complete setup of the Repair QR Ninja database.
 * 
 * IMPORTANT: Run this in your Supabase SQL Editor or PostgreSQL client.
 * 
 * The setup includes:
 * ✅ Core repair management system
 * ✅ User management and RBAC
 * ✅ Stock management and POS
 * ✅ Premium features (time tracking, forms, invoices)
 * ✅ Complete security policies
 * ✅ Real-time functionality
 * ✅ Sample data and templates
 */

-- =============================================
-- PART 1: CORE TABLES AND STRUCTURE
-- =============================================

\echo 'Starting Repair QR Ninja Database Setup...'
\echo 'Part 1: Core tables and structure'

\i complete_database_setup.sql

-- =============================================
-- PART 2: SECURITY POLICIES
-- =============================================

\echo 'Part 2: Security policies and access control'

\i complete_database_setup_part2.sql

-- =============================================
-- PART 3: FINAL SETUP AND CONFIGURATION
-- =============================================

\echo 'Part 3: Final setup and configuration'

\i complete_database_setup_part3.sql

-- =============================================
-- SETUP VERIFICATION
-- =============================================

\echo 'Verifying database setup...'

-- Check if all main tables exist
SELECT 
    CASE 
        WHEN COUNT(*) = 16 THEN '✅ All tables created successfully'
        ELSE '❌ Missing tables: ' || (16 - COUNT(*))::text
    END as table_status
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN (
    'repair_shops', 'user_repair_shops', 'repairs',
    'product_categories', 'products', 'sales', 'sale_items',
    'stock_movements', 'one_time_items', 'repair_stock_items',
    'technician_time_entries', 'form_templates', 'form_responses',
    'user_action_logs', 'invoices', 'invoice_items'
);

-- Check if sequences exist
SELECT 
    CASE 
        WHEN COUNT(*) = 2 THEN '✅ All sequences created successfully'
        ELSE '❌ Missing sequences: ' || (2 - COUNT(*))::text
    END as sequence_status
FROM information_schema.sequences 
WHERE sequence_schema = 'public' 
AND sequence_name IN ('repair_ticket_number_seq', 'sale_number_seq');

-- Check if sample data exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ Sample data populated'
        ELSE '❌ No sample data found'
    END as sample_data_status
FROM public.product_categories;

-- Check if RLS is enabled
SELECT 
    CASE 
        WHEN COUNT(*) = 16 THEN '✅ Row Level Security enabled on all tables'
        ELSE '❌ RLS not enabled on all tables'
    END as rls_status
FROM information_schema.tables t
JOIN pg_class c ON c.relname = t.table_name
WHERE t.table_schema = 'public' 
AND t.table_name IN (
    'repair_shops', 'user_repair_shops', 'repairs',
    'product_categories', 'products', 'sales', 'sale_items',
    'stock_movements', 'one_time_items', 'repair_stock_items',
    'technician_time_entries', 'form_templates', 'form_responses',
    'user_action_logs', 'invoices', 'invoice_items'
)
AND c.relrowsecurity = true;

-- Check if publication exists
SELECT 
    CASE 
        WHEN COUNT(*) > 0 THEN '✅ Real-time publication configured'
        ELSE '❌ Real-time publication not found'
    END as realtime_status
FROM pg_publication 
WHERE pubname = 'supabase_realtime';

\echo ''
\echo '🎉 REPAIR QR NINJA DATABASE SETUP COMPLETE! 🎉'
\echo ''
\echo 'Your premium repair shop management system is ready!'
\echo ''
\echo 'Next steps:'
\echo '1. Update the default repair shop information'
\echo '2. Create your first administrator user'
\echo '3. Configure your product catalog'
\echo '4. Set up email templates'
\echo '5. Configure environment variables'
\echo ''
\echo 'Happy repairing! 🔧📱'
