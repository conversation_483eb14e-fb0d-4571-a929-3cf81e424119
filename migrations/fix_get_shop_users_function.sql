/*
 * REPAIR QR NINJA - FIX GET_SHOP_USERS FUNCTION
 * 
 * This script fixes the data type mismatch in the get_shop_users function.
 * The error occurs because auth.users.email is varchar(255) but function expects TEXT.
 */

-- Start transaction
BEGIN;

-- Drop and recreate the function with proper type casting
DROP FUNCTION IF EXISTS public.get_shop_users(UUID);

-- Function to get users for a specific repair shop (with proper type casting)
CREATE OR REPLACE FUNCTION public.get_shop_users(shop_id UUID)
RETURNS TABLE (
    user_id UUID,
    email TEXT,
    full_name TEXT,
    role TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        urs.user_id,
        au.email::TEXT,  -- Cast varchar to TEXT to match return type
        COALESCE(urs.full_name, '')::TEXT,  -- Ensure full_name is TEXT and handle nulls
        urs.role::TEXT,  -- Cast to TEXT to be safe
        urs.created_at,
        urs.updated_at
    FROM public.user_repair_shops urs
    JOIN auth.users au ON urs.user_id = au.id
    WHERE urs.repair_shop_id = shop_id
    ORDER BY urs.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Also fix the get_user_repair_shop function if it has similar issues
DROP FUNCTION IF EXISTS public.get_user_repair_shop();

CREATE OR REPLACE FUNCTION public.get_user_repair_shop()
RETURNS TABLE (
    repair_shop_id UUID,
    repair_shop_name TEXT,
    user_role TEXT,
    full_name TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        urs.repair_shop_id,
        rs.name::TEXT as repair_shop_name,  -- Cast to TEXT
        urs.role::TEXT as user_role,        -- Cast to TEXT
        COALESCE(urs.full_name, '')::TEXT   -- Cast to TEXT and handle nulls
    FROM public.user_repair_shops urs
    JOIN public.repair_shops rs ON urs.repair_shop_id = rs.id
    WHERE urs.user_id = auth.uid()
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Commit the changes
COMMIT;

-- Test the function
SELECT 'get_shop_users function fixed successfully!' as status;
