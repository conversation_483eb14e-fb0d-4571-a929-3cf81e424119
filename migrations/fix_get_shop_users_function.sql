/*
 * REPAIR QR NINJA - FIX INFINITE RECURSION IN RLS POLICIES
 *
 * This script fixes the infinite recursion error in Row Level Security policies
 * for the user_repair_shops table and related functions.
 *
 * The error "infinite recursion detected in policy for relation user_repair_shops"
 * occurs when RLS policies reference the same table they're protecting, creating
 * circular dependencies.
 */

-- Start transaction
BEGIN;

-- =============================================
-- STEP 1: DROP ALL PROBLEMATIC RLS POLICIES
-- =============================================

-- Drop all existing policies that might cause recursion
DROP POLICY IF EXISTS "Users can view repair shops they belong to" ON public.repair_shops;
DROP POLICY IF EXISTS "Administrators can manage repair shops" ON public.repair_shops;
DROP POLICY IF EXISTS "All authenticated users can view repair shops" ON public.repair_shops;

DROP POLICY IF EXISTS "Users can view their own repair shop associations" ON public.user_repair_shops;
DROP POLICY IF EXISTS "Administrators can view repair shop associations" ON public.user_repair_shops;
DROP POLICY IF EXISTS "Administrators can manage user-repair shop associations" ON public.user_repair_shops;
DROP POLICY IF EXISTS "Admins can manage user-repair shop associations" ON public.user_repair_shops;
DROP POLICY IF EXISTS "Users can insert their own repair shop associations" ON public.user_repair_shops;
DROP POLICY IF EXISTS "Users can update their own repair shop associations" ON public.user_repair_shops;

-- =============================================
-- STEP 2: CREATE SIMPLE, NON-RECURSIVE POLICIES
-- =============================================

-- REPAIR SHOPS: Allow all authenticated users to view and manage
CREATE POLICY "Authenticated users can access repair shops" ON public.repair_shops
    FOR ALL USING (auth.uid() IS NOT NULL);

-- USER_REPAIR_SHOPS: Simple policies without recursion
-- Users can only see their own associations
CREATE POLICY "Users can view own associations" ON public.user_repair_shops
    FOR SELECT USING (user_id = auth.uid());

-- Users can insert their own associations
CREATE POLICY "Users can insert own associations" ON public.user_repair_shops
    FOR INSERT WITH CHECK (user_id = auth.uid());

-- Users can update their own associations
CREATE POLICY "Users can update own associations" ON public.user_repair_shops
    FOR UPDATE USING (user_id = auth.uid());

-- Users can delete their own associations
CREATE POLICY "Users can delete own associations" ON public.user_repair_shops
    FOR DELETE USING (user_id = auth.uid());

-- =============================================
-- STEP 3: FIX FUNCTIONS WITH PROPER TYPE CASTING
-- =============================================

-- Drop and recreate the function with proper type casting
DROP FUNCTION IF EXISTS public.get_shop_users(UUID);

-- Function to get users for a specific repair shop (with proper type casting)
CREATE OR REPLACE FUNCTION public.get_shop_users(shop_id UUID)
RETURNS TABLE (
    user_id UUID,
    email TEXT,
    full_name TEXT,
    role TEXT,
    created_at TIMESTAMP WITH TIME ZONE,
    updated_at TIMESTAMP WITH TIME ZONE
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        urs.user_id,
        au.email::TEXT,  -- Cast varchar to TEXT to match return type
        COALESCE(urs.full_name, '')::TEXT,  -- Ensure full_name is TEXT and handle nulls
        urs.role::TEXT,  -- Cast to TEXT to be safe
        urs.created_at,
        urs.updated_at
    FROM public.user_repair_shops urs
    JOIN auth.users au ON urs.user_id = au.id
    WHERE urs.repair_shop_id = shop_id
    ORDER BY urs.created_at ASC;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Also fix the get_user_repair_shop function if it has similar issues
DROP FUNCTION IF EXISTS public.get_user_repair_shop();

CREATE OR REPLACE FUNCTION public.get_user_repair_shop()
RETURNS TABLE (
    repair_shop_id UUID,
    repair_shop_name TEXT,
    user_role TEXT,
    full_name TEXT
) AS $$
BEGIN
    RETURN QUERY
    SELECT
        urs.repair_shop_id,
        rs.name::TEXT as repair_shop_name,  -- Cast to TEXT
        urs.role::TEXT as user_role,        -- Cast to TEXT
        COALESCE(urs.full_name, '')::TEXT   -- Cast to TEXT and handle nulls
    FROM public.user_repair_shops urs
    JOIN public.repair_shops rs ON urs.repair_shop_id = rs.id
    WHERE urs.user_id = auth.uid()
    LIMIT 1;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- =============================================
-- STEP 4: COMMIT CHANGES
-- =============================================

COMMIT;

-- =============================================
-- STEP 5: VERIFICATION
-- =============================================

-- Test the function
SELECT 'RLS policies and functions fixed successfully!' as status;

-- Verify policies are in place
SELECT
    schemaname,
    tablename,
    policyname,
    cmd
FROM pg_policies
WHERE tablename IN ('repair_shops', 'user_repair_shops')
ORDER BY tablename, policyname;
