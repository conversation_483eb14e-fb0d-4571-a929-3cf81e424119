-- Fix for the ambiguous column reference error in sale_number generation
-- Run this script in your Supabase SQL Editor to fix the issue

-- Drop and recreate the function with proper table aliases
CREATE OR REPLACE FUNCTION generate_sale_number(shop_id UUID)
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    new_sale_number TEXT;
BEGIN
    -- Get the next sale number for this repair shop
    SELECT COALESCE(MAX(CAST(SUBSTRING(s.sale_number FROM '[0-9]+$') AS INTEGER)), 0) + 1
    INTO next_number
    FROM public.sales s
    WHERE s.repair_shop_id = shop_id
    AND s.sale_number ~ '^SALE-[0-9]+$';
    
    -- Format as SALE-XXXX
    new_sale_number := 'SALE-' || LPAD(next_number::TEXT, 4, '0');
    
    RETURN new_sale_number;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger function as well
CREATE OR REPLACE FUNCTION set_sale_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.sale_number IS NULL OR NEW.sale_number = '' THEN
        NEW.sale_number := generate_sale_number(NEW.repair_shop_id);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Recreate the trigger
DROP TRIGGER IF EXISTS trigger_set_sale_number ON public.sales;
CREATE TRIGGER trigger_set_sale_number
    BEFORE INSERT ON public.sales
    FOR EACH ROW
    EXECUTE FUNCTION set_sale_number();
