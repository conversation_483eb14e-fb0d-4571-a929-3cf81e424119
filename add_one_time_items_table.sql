-- Create one_time_items table for tracking unique items like refurbished phones, laptops, etc.
CREATE TABLE IF NOT EXISTS one_time_items (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    product_id UUID NOT NULL REFERENCES products(id) ON DELETE CASCADE,
    serial_number TEXT, -- IMEI for phones, serial number for other devices
    battery_percentage INTEGER CHECK (battery_percentage >= 0 AND battery_percentage <= 100),
    condition TEXT NOT NULL CHECK (condition IN ('excellent', 'good', 'fair', 'poor')),
    device_type TEXT NOT NULL CHECK (device_type IN ('iphone', 'android', 'mac', 'pc', 'laptop', 'other')),
    model TEXT,
    storage TEXT, -- e.g., "128GB", "256GB"
    color TEXT,
    notes TEXT,
    is_sold BOOLEAN DEFAULT FALSE,
    sold_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_one_time_items_product_id ON one_time_items(product_id);
CREATE INDEX IF NOT EXISTS idx_one_time_items_is_sold ON one_time_items(is_sold);
CREATE INDEX IF NOT EXISTS idx_one_time_items_device_type ON one_time_items(device_type);
CREATE INDEX IF NOT EXISTS idx_one_time_items_serial_number ON one_time_items(serial_number);

-- Add trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_one_time_items_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_one_time_items_updated_at
    BEFORE UPDATE ON one_time_items
    FOR EACH ROW
    EXECUTE FUNCTION update_one_time_items_updated_at();

-- Add is_one_time_item column to products table
ALTER TABLE products ADD COLUMN IF NOT EXISTS is_one_time_item BOOLEAN DEFAULT FALSE;

-- Add index for one-time item products
CREATE INDEX IF NOT EXISTS idx_products_is_one_time_item ON products(is_one_time_item);

-- Update the products table to set stock_quantity to 1 for one-time items
-- and ensure min_stock_level is 0 for one-time items
CREATE OR REPLACE FUNCTION enforce_one_time_item_constraints()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.is_one_time_item = TRUE THEN
        NEW.stock_quantity = 1;
        NEW.min_stock_level = 0;
        NEW.max_stock_level = 1;
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_enforce_one_time_item_constraints
    BEFORE INSERT OR UPDATE ON products
    FOR EACH ROW
    EXECUTE FUNCTION enforce_one_time_item_constraints();

-- Add RLS policies for one_time_items table
ALTER TABLE one_time_items ENABLE ROW LEVEL SECURITY;

-- Policy for authenticated users to see one-time items from their repair shop
CREATE POLICY "Users can view one-time items from their repair shop" ON one_time_items
    FOR SELECT USING (
        product_id IN (
            SELECT id FROM products 
            WHERE repair_shop_id IN (
                SELECT repair_shop_id FROM user_repair_shops 
                WHERE user_id = auth.uid()
            )
        )
    );

-- Policy for authenticated users to insert one-time items for their repair shop
CREATE POLICY "Users can insert one-time items for their repair shop" ON one_time_items
    FOR INSERT WITH CHECK (
        product_id IN (
            SELECT id FROM products 
            WHERE repair_shop_id IN (
                SELECT repair_shop_id FROM user_repair_shops 
                WHERE user_id = auth.uid()
            )
        )
    );

-- Policy for authenticated users to update one-time items from their repair shop
CREATE POLICY "Users can update one-time items from their repair shop" ON one_time_items
    FOR UPDATE USING (
        product_id IN (
            SELECT id FROM products 
            WHERE repair_shop_id IN (
                SELECT repair_shop_id FROM user_repair_shops 
                WHERE user_id = auth.uid()
            )
        )
    );

-- Policy for authenticated users to delete one-time items from their repair shop
CREATE POLICY "Users can delete one-time items from their repair shop" ON one_time_items
    FOR DELETE USING (
        product_id IN (
            SELECT id FROM products 
            WHERE repair_shop_id IN (
                SELECT repair_shop_id FROM user_repair_shops 
                WHERE user_id = auth.uid()
            )
        )
    );
