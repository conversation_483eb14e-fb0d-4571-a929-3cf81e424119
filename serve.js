import express from "express";
import path from "path";
import { fileURLToPath } from "url";
import compression from "compression";
import helmet from "helmet";

// Get __dirname equivalent in ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Create Express app
const app = express();
const PORT = process.env.PORT || 3000;

// Security middleware
// app.use(
//   helmet({
//     contentSecurityPolicy: {
//       directives: {
//         defaultSrc: ["'self'"],
//         connectSrc: [
//           "'self'",
//           process.env.SUPABASE_URL ||
//             "https://qvwbetihlprjphvyaawi.supabase.co",
//           "https://*.supabase.co",
//           "wss://*.supabase.co",
//           "https://qvwbetihlprjphvyaawi.supabase.co",
//           "wss://qvwbetihlprjphvyaawi.supabase.co",
//           "https://Multitech.talifouni.com",
//           "wss://Multitech.talifouni.com",
//         ],
//         imgSrc: ["'self'", "data:", "blob:"],
//         scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.gpteng.co"],
//         styleSrc: ["'self'", "'unsafe-inline'"],
//         fontSrc: ["'self'", "data:"],
//         formAction: ["'self'"],
//         frameAncestors: ["'none'"],
//       },
//     },
//   })
// );

// Compression middleware
app.use(compression());

// Serve static files
app.use(express.static(path.join(__dirname, "dist")));

// CSP reporting endpoint
app.get("/csp-report", (_, res) => {
  res.setHeader("Content-Type", "text/plain");
  res.send("CSP Report Endpoint");
});

// Serve test CSP HTML file
app.get("/test-csp", (_, res) => {
  res.sendFile(path.join(__dirname, "test-csp.html"));
});

// Handle SPA routing - send all requests to index.html
app.get("*", (_, res) => {
  res.sendFile(path.join(__dirname, "dist", "index.html"));
});

// Start server
app.listen(PORT, () => {
  console.log(`Server running on port ${PORT}`);
});
