<!DOCTYPE html>
<html>
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Thermal Printer Compatibility Test</title>
  <style>
    body {
      font-family: Arial, sans-serif;
      margin: 0;
      padding: 20px;
      line-height: 1.6;
    }
    .container {
      max-width: 800px;
      margin: 0 auto;
    }
    .test-area {
      border: 1px solid #ccc;
      padding: 20px;
      margin-bottom: 20px;
      background-color: #f9f9f9;
    }
    .controls {
      margin-bottom: 20px;
    }
    button {
      padding: 10px 15px;
      margin-right: 10px;
      cursor: pointer;
    }
    .print-preview {
      display: none;
    }
    @media print {
      .no-print {
        display: none;
      }
      .print-preview {
        display: block;
      }
      @page {
        size: auto;
        margin: 0;
      }
      body {
        width: 100%;
        margin: 0;
        padding: 0;
      }
      .ticket {
        width: 95%;
        max-width: 300px;
        margin: 0 auto;
        font-family: 'Arial', sans-serif;
        font-size: 9px;
        line-height: 1.2;
      }
    }
    .debug-info {
      position: fixed;
      top: 10px;
      right: 10px;
      background: #fff;
      border: 1px solid #000;
      padding: 10px;
      font-size: 12px;
      z-index: 9999;
    }
    .ticket {
      font-family: 'Arial', sans-serif;
      width: 95%;
      max-width: 300px;
      margin: 0 auto;
      padding: 5px;
      box-sizing: border-box;
      font-size: 9px;
      line-height: 1.2;
    }
    .header {
      text-align: center;
      margin-bottom: 5px;
      border-bottom: 1px dashed #000;
      padding-bottom: 3px;
    }
    .shop-name {
      font-size: 14px;
      font-weight: 900;
    }
    .section {
      margin-bottom: 5px;
      border-bottom: 1px dashed #000;
      padding-bottom: 3px;
    }
    .info-row {
      display: block;
      margin: 1px 0;
      width: 100%;
      overflow: hidden;
    }
    .label {
      font-weight: 900;
      display: inline-block;
      width: 40%;
      vertical-align: top;
    }
    .value {
      font-weight: 700;
      text-align: right;
      display: inline-block;
      width: 58%;
      vertical-align: top;
    }
  </style>
</head>
<body>
  <div class="container no-print">
    <h1>Thermal Printer Compatibility Test</h1>
    <p>This page helps you test if your thermal printer will work with the responsive layout.</p>
    
    <div class="controls">
      <button id="print-test">Print Test Page</button>
      <button id="toggle-debug">Show Debug Info</button>
    </div>
    
    <div class="test-area">
      <h2>Preview:</h2>
      <div id="ticket-preview" class="ticket">
        <div class="header">
          <div class="shop-name">Repair QR Ninja</div>
          <div>Contact us: 24025024 - 24300025</div>
          <div>Repair Ticket #12345</div>
          <div>Jan 15, 2023 - 14:30</div>
        </div>

        <div class="section">
          <div style="font-weight: 900; text-align: center; margin-bottom: 3px;">Customer Information</div>
          <div class="info-row">
            <span class="label">Customer Name:</span>
            <span class="value">John Doe</span>
          </div>
          <div class="info-row">
            <span class="label">Customer Phone:</span>
            <span class="value">+216 55 123 456</span>
          </div>
        </div>

        <div class="section">
          <div style="font-weight: 900; text-align: center; margin-bottom: 3px;">Repair Details</div>
          <div class="info-row">
            <span class="label">Phone Model:</span>
            <span class="value">iPhone 13 Pro</span>
          </div>
          <div class="info-row">
            <span class="label">Status:</span>
            <span class="value">In Progress</span>
          </div>
          
          <div style="margin-top: 4px; border: 1px solid #000; padding: 2px;">
            <p style="font-weight: 900; text-align: center; text-decoration: underline; font-size: 10px; margin-bottom: 2px;">Problem Description:</p>
            <p style="font-weight: 800; font-size: 9px; text-align: left;">Screen is cracked and battery drains quickly. Customer mentioned water damage a few weeks ago.</p>
          </div>
        </div>

        <div class="section">
          <div style="font-weight: 900; text-align: center; margin-bottom: 3px;">Payment Status</div>
          <div class="info-row">
            <span class="label">Initial Price:</span>
            <span class="value" style="font-weight: 800;">150.00 TND</span>
          </div>

          <div style="margin-top: 3px; border-top: 1px dashed #000; padding-top: 2px;">
            <div style="margin-bottom: 1px;">
              <span style="font-weight: 700; display: inline-block; width: 50%;">Down Payment:</span>
              <span style="font-weight: 700; display: inline-block; width: 50%; text-align: right;">100.00 TND</span>
            </div>
            <div>
              <span style="font-weight: 700; display: inline-block; width: 50%;">Balance:</span>
              <span style="font-weight: 800; display: inline-block; width: 50%; text-align: right;">50.00 TND</span>
            </div>
          </div>
        </div>

        <div style="text-align: center; margin-top: 5px;">
          <div style="width: 50px; height: 50px; background-color: #000; margin: 0 auto;"></div>
          <p style="font-weight: 700; text-align: center;">Scan QR Code: abcd1234</p>
          <p style="font-weight: 800; text-align: center;">Ticket Number: #12345</p>
        </div>

        <div style="text-align: center; margin-top: 5px;">
          <p style="font-weight: 700;">Thank you for your visit!</p>
          <p style="font-weight: 700;">Please bring this receipt when you pick up your device.</p>
        </div>
      </div>
    </div>
    
    <div class="instructions">
      <h2>How to Test:</h2>
      <ol>
        <li>Click the "Print Test Page" button to open the print dialog</li>
        <li>In the print dialog, select your thermal printer</li>
        <li>Check the print preview to see how it will look</li>
        <li>If the content fits within the width of your thermal printer paper, it will work correctly</li>
        <li>Click "Show Debug Info" to see details about your current window size and how it would scale</li>
      </ol>
      <p><strong>Note:</strong> The actual print may look different from the preview due to printer driver settings. The responsive design should adapt to most thermal printer widths.</p>
    </div>
  </div>
  
  <div class="print-preview">
    <div id="print-ticket" class="ticket">
      <!-- This will be populated with the same content as the preview when printing -->
    </div>
  </div>
  
  <div id="debug-info" class="debug-info" style="display: none;">
    <h3>Debug Information</h3>
    <div id="debug-content"></div>
    <button id="close-debug">Close</button>
  </div>

  <script>
    // Function to update debug info
    function updateDebugInfo() {
      const availableWidth = window.innerWidth;
      const debugContent = document.getElementById('debug-content');
      
      debugContent.innerHTML = `
        <p><strong>Window Width:</strong> ${availableWidth}px</p>
        <p><strong>Approx Width in mm:</strong> ${Math.round(availableWidth / 3.78)}mm</p>
        <p><strong>Font Size:</strong> ${availableWidth < 200 ? '7px' : availableWidth < 250 ? '8px' : availableWidth < 300 ? '9px' : '10px'}</p>
        <p><strong>Ticket Width:</strong> ${Math.min(availableWidth - 10, 300)}px</p>
        <p><strong>Recommended for:</strong> ${availableWidth < 220 ? '58mm printer' : availableWidth < 300 ? '80mm printer' : 'Standard printer'}</p>
      `;
    }
    
    // Function to adjust content to fit printer width
    function adjustContentToFit() {
      const ticket = document.querySelector('.ticket');
      if (!ticket) return;
      
      const availableWidth = window.innerWidth;
      
      // Adjust font size based on available width
      if (availableWidth < 200) {
        document.body.style.fontSize = '7px';
        ticket.style.fontSize = '7px';
      } else if (availableWidth < 250) {
        document.body.style.fontSize = '8px';
        ticket.style.fontSize = '8px';
      } else if (availableWidth < 300) {
        document.body.style.fontSize = '9px';
        ticket.style.fontSize = '9px';
      } else {
        document.body.style.fontSize = '10px';
        ticket.style.fontSize = '10px';
      }
      
      // Set ticket width to fit available space
      ticket.style.width = Math.min(availableWidth - 10, 300) + 'px';
      
      // Update debug info
      updateDebugInfo();
    }
    
    // Initialize
    document.addEventListener('DOMContentLoaded', function() {
      // Set up print button
      document.getElementById('print-test').addEventListener('click', function() {
        // Copy content to print area
        const printTicket = document.getElementById('print-ticket');
        const ticketPreview = document.getElementById('ticket-preview');
        printTicket.innerHTML = ticketPreview.innerHTML;
        
        // Print
        window.print();
      });
      
      // Set up debug toggle
      document.getElementById('toggle-debug').addEventListener('click', function() {
        const debugInfo = document.getElementById('debug-info');
        if (debugInfo.style.display === 'none') {
          debugInfo.style.display = 'block';
          updateDebugInfo();
        } else {
          debugInfo.style.display = 'none';
        }
      });
      
      // Set up close debug button
      document.getElementById('close-debug').addEventListener('click', function() {
        document.getElementById('debug-info').style.display = 'none';
      });
      
      // Adjust on resize
      window.addEventListener('resize', adjustContentToFit);
      
      // Initial adjustment
      adjustContentToFit();
    });
  </script>
</body>
</html>
