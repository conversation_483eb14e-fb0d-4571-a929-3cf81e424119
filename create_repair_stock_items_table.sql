-- Create repair_stock_items table to link repairs with stock items
BEGIN;

-- Create repair_stock_items table
CREATE TABLE IF NOT EXISTS public.repair_stock_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repair_id UUID NOT NULL REFERENCES public.repairs(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price DECIMAL(10,3) NOT NULL CHECK (unit_price >= 0),
    total_price DECIMAL(10,3) GENERATED ALWAYS AS (quantity * unit_price) STORED,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_repair_stock_items_repair_id ON public.repair_stock_items(repair_id);
CREATE INDEX IF NOT EXISTS idx_repair_stock_items_product_id ON public.repair_stock_items(product_id);
CREATE INDEX IF NOT EXISTS idx_repair_stock_items_repair_shop_id ON public.repair_stock_items(repair_shop_id);

-- Enable RLS (Row Level Security)
ALTER TABLE public.repair_stock_items ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view repair stock items from their repair shops" ON public.repair_stock_items
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repair_stock_items.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage repair stock items from their repair shops" ON public.repair_stock_items
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repair_stock_items.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

-- Create trigger to update updated_at timestamp
CREATE TRIGGER update_repair_stock_items_updated_at 
    BEFORE UPDATE ON public.repair_stock_items 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

COMMIT;