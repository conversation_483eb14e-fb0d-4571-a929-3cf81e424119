-- Create function to get users with emails for a shop
CREATE OR R<PERSON>LACE FUNCTION get_shop_users(shop_id UUID)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  role VARCHAR,
  repair_shop_id UUID,
  email VARCHAR
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Return users with emails
  RETURN QUERY
  SELECT 
    urs.id,
    urs.user_id,
    urs.role::VARCHAR,
    urs.repair_shop_id,
    COALESCE(au.email, urs.user_id::text)::VARCHAR as email
  FROM public.user_repair_shops urs
  LEFT JOIN auth.users au ON au.id = urs.user_id
  WHERE urs.repair_shop_id = shop_id;
END;
$$;