// preload.js
const { ipc<PERSON><PERSON><PERSON>, contextBridge } = require("electron");

console.log("✅ preload.js is running");

contextBridge.exposeInMainWorld("electron", {
  submitGate: (data) => ipcRenderer.send("GATE_SUBMIT", data),
  onShowError: (callback) =>
    ipcRenderer.on("show-error", (event, message) => callback(message)),
});

console.log("✅ contextBridge exposeInMainWorld executed");
