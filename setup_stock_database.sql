-- Stock Management Database Setup for Repair QR Ninja
-- This script creates tables for stock management and POS functionality

-- Enable UUID extension if not already enabled
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Create product_categories table
CREATE TABLE IF NOT EXISTS public.product_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    icon TEXT,
    color TEXT,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create products table
CREATE TABLE IF NOT EXISTS public.products (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name TEXT NOT NULL,
    description TEXT,
    sku TEXT NOT NULL,
    barcode TEXT,
    category_id UUID NOT NULL REFERENCES public.product_categories(id) ON DELETE CASCADE,
    price NUMERIC NOT NULL CHECK (price >= 0),
    cost NUMERIC NOT NULL CHECK (cost >= 0),
    stock_quantity INTEGER NOT NULL DEFAULT 0 CHECK (stock_quantity >= 0),
    min_stock_level INTEGER NOT NULL DEFAULT 0 CHECK (min_stock_level >= 0),
    max_stock_level INTEGER CHECK (max_stock_level IS NULL OR max_stock_level >= min_stock_level),
    is_active BOOLEAN NOT NULL DEFAULT true,
    image_url TEXT,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(sku, repair_shop_id),
    UNIQUE(barcode, repair_shop_id) -- Allow NULL barcodes but unique when present
);

-- Create sales table
CREATE TABLE IF NOT EXISTS public.sales (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_number TEXT NOT NULL,
    customer_id UUID, -- Optional customer reference
    customer_name TEXT,
    customer_phone TEXT,
    subtotal NUMERIC NOT NULL CHECK (subtotal >= 0),
    tax_amount NUMERIC NOT NULL DEFAULT 0 CHECK (tax_amount >= 0),
    discount_amount NUMERIC NOT NULL DEFAULT 0 CHECK (discount_amount >= 0),
    total_amount NUMERIC NOT NULL CHECK (total_amount >= 0),
    payment_method TEXT NOT NULL CHECK (payment_method IN ('cash', 'card', 'mobile', 'mixed')),
    payment_status TEXT NOT NULL CHECK (payment_status IN ('paid', 'partial', 'pending')),
    amount_paid NUMERIC NOT NULL DEFAULT 0 CHECK (amount_paid >= 0),
    change_amount NUMERIC NOT NULL DEFAULT 0 CHECK (change_amount >= 0),
    notes TEXT,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL,
    UNIQUE(sale_number, repair_shop_id)
);

-- Create sale_items table
CREATE TABLE IF NOT EXISTS public.sale_items (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    sale_id UUID NOT NULL REFERENCES public.sales(id) ON DELETE CASCADE,
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL CHECK (quantity > 0),
    unit_price NUMERIC NOT NULL CHECK (unit_price >= 0),
    discount NUMERIC NOT NULL DEFAULT 0 CHECK (discount >= 0),
    total_price NUMERIC NOT NULL CHECK (total_price >= 0),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create stock_movements table for inventory tracking
CREATE TABLE IF NOT EXISTS public.stock_movements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    product_id UUID NOT NULL REFERENCES public.products(id) ON DELETE CASCADE,
    type TEXT NOT NULL CHECK (type IN ('in', 'out', 'adjustment')),
    quantity INTEGER NOT NULL,
    reason TEXT NOT NULL,
    reference TEXT, -- Sale ID, Purchase ID, etc.
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW() NOT NULL
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_products_repair_shop_id ON public.products(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_products_category_id ON public.products(category_id);
CREATE INDEX IF NOT EXISTS idx_products_sku ON public.products(sku);
CREATE INDEX IF NOT EXISTS idx_products_barcode ON public.products(barcode) WHERE barcode IS NOT NULL;
CREATE INDEX IF NOT EXISTS idx_products_stock_quantity ON public.products(stock_quantity);
CREATE INDEX IF NOT EXISTS idx_products_is_active ON public.products(is_active);

CREATE INDEX IF NOT EXISTS idx_product_categories_repair_shop_id ON public.product_categories(repair_shop_id);

CREATE INDEX IF NOT EXISTS idx_sales_repair_shop_id ON public.sales(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_sales_user_id ON public.sales(user_id);
CREATE INDEX IF NOT EXISTS idx_sales_created_at ON public.sales(created_at);
CREATE INDEX IF NOT EXISTS idx_sales_payment_status ON public.sales(payment_status);

CREATE INDEX IF NOT EXISTS idx_sale_items_sale_id ON public.sale_items(sale_id);
CREATE INDEX IF NOT EXISTS idx_sale_items_product_id ON public.sale_items(product_id);

CREATE INDEX IF NOT EXISTS idx_stock_movements_product_id ON public.stock_movements(product_id);
CREATE INDEX IF NOT EXISTS idx_stock_movements_repair_shop_id ON public.stock_movements(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_stock_movements_created_at ON public.stock_movements(created_at);
CREATE INDEX IF NOT EXISTS idx_stock_movements_type ON public.stock_movements(type);

-- Create functions for automatic sale number generation
CREATE OR REPLACE FUNCTION generate_sale_number(shop_id UUID)
RETURNS TEXT AS $$
DECLARE
    next_number INTEGER;
    new_sale_number TEXT;
BEGIN
    -- Get the next sale number for this repair shop
    SELECT COALESCE(MAX(CAST(SUBSTRING(s.sale_number FROM '[0-9]+$') AS INTEGER)), 0) + 1
    INTO next_number
    FROM public.sales s
    WHERE s.repair_shop_id = shop_id
    AND s.sale_number ~ '^SALE-[0-9]+$';

    -- Format as SALE-XXXX
    new_sale_number := 'SALE-' || LPAD(next_number::TEXT, 4, '0');

    RETURN new_sale_number;
END;
$$ LANGUAGE plpgsql;

-- Create trigger function for automatic sale number assignment
CREATE OR REPLACE FUNCTION set_sale_number()
RETURNS TRIGGER AS $$
BEGIN
    IF NEW.sale_number IS NULL OR NEW.sale_number = '' THEN
        NEW.sale_number := generate_sale_number(NEW.repair_shop_id);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for automatic sale number generation
DROP TRIGGER IF EXISTS trigger_set_sale_number ON public.sales;
CREATE TRIGGER trigger_set_sale_number
    BEFORE INSERT ON public.sales
    FOR EACH ROW
    EXECUTE FUNCTION set_sale_number();

-- Create function to update stock quantity when sale items are added/removed
CREATE OR REPLACE FUNCTION update_stock_on_sale()
RETURNS TRIGGER AS $$
BEGIN
    IF TG_OP = 'INSERT' THEN
        -- Decrease stock when sale item is added
        UPDATE public.products 
        SET stock_quantity = stock_quantity - NEW.quantity,
            updated_at = NOW()
        WHERE id = NEW.product_id;
        
        -- Create stock movement record
        INSERT INTO public.stock_movements (
            product_id, type, quantity, reason, reference, user_id, repair_shop_id
        )
        SELECT 
            NEW.product_id, 
            'out', 
            NEW.quantity, 
            'Sale', 
            s.sale_number,
            s.user_id,
            s.repair_shop_id
        FROM public.sales s 
        WHERE s.id = NEW.sale_id;
        
        RETURN NEW;
    ELSIF TG_OP = 'DELETE' THEN
        -- Increase stock when sale item is removed (refund)
        UPDATE public.products 
        SET stock_quantity = stock_quantity + OLD.quantity,
            updated_at = NOW()
        WHERE id = OLD.product_id;
        
        -- Create stock movement record
        INSERT INTO public.stock_movements (
            product_id, type, quantity, reason, reference, user_id, repair_shop_id
        )
        SELECT 
            OLD.product_id, 
            'in', 
            OLD.quantity, 
            'Sale Refund', 
            s.sale_number,
            s.user_id,
            s.repair_shop_id
        FROM public.sales s 
        WHERE s.id = OLD.sale_id;
        
        RETURN OLD;
    END IF;
    RETURN NULL;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for stock updates
DROP TRIGGER IF EXISTS trigger_update_stock_on_sale ON public.sale_items;
CREATE TRIGGER trigger_update_stock_on_sale
    AFTER INSERT OR DELETE ON public.sale_items
    FOR EACH ROW
    EXECUTE FUNCTION update_stock_on_sale();

-- Create function for updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for updated_at
DROP TRIGGER IF EXISTS trigger_update_products_updated_at ON public.products;
CREATE TRIGGER trigger_update_products_updated_at
    BEFORE UPDATE ON public.products
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_update_categories_updated_at ON public.product_categories;
CREATE TRIGGER trigger_update_categories_updated_at
    BEFORE UPDATE ON public.product_categories
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

DROP TRIGGER IF EXISTS trigger_update_sales_updated_at ON public.sales;
CREATE TRIGGER trigger_update_sales_updated_at
    BEFORE UPDATE ON public.sales
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Enable Row Level Security
ALTER TABLE public.product_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.products ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sales ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.sale_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.stock_movements ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for product_categories
CREATE POLICY "Users can view categories from their repair shop" ON public.product_categories
    FOR SELECT USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert categories for their repair shop" ON public.product_categories
    FOR INSERT WITH CHECK (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update categories from their repair shop" ON public.product_categories
    FOR UPDATE USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete categories from their repair shop" ON public.product_categories
    FOR DELETE USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

-- Create RLS policies for products
CREATE POLICY "Users can view products from their repair shop" ON public.products
    FOR SELECT USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert products for their repair shop" ON public.products
    FOR INSERT WITH CHECK (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can update products from their repair shop" ON public.products
    FOR UPDATE USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete products from their repair shop" ON public.products
    FOR DELETE USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

-- Create RLS policies for sales
CREATE POLICY "Users can view sales from their repair shop" ON public.sales
    FOR SELECT USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert sales for their repair shop" ON public.sales
    FOR INSERT WITH CHECK (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
        AND user_id = auth.uid()
    );

CREATE POLICY "Users can update sales from their repair shop" ON public.sales
    FOR UPDATE USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can delete sales from their repair shop" ON public.sales
    FOR DELETE USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

-- Create RLS policies for sale_items
CREATE POLICY "Users can view sale items from their repair shop" ON public.sale_items
    FOR SELECT USING (
        sale_id IN (
            SELECT id FROM public.sales
            WHERE repair_shop_id IN (
                SELECT repair_shop_id FROM public.user_repair_shops
                WHERE user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can insert sale items for their repair shop" ON public.sale_items
    FOR INSERT WITH CHECK (
        sale_id IN (
            SELECT id FROM public.sales
            WHERE repair_shop_id IN (
                SELECT repair_shop_id FROM public.user_repair_shops
                WHERE user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can update sale items from their repair shop" ON public.sale_items
    FOR UPDATE USING (
        sale_id IN (
            SELECT id FROM public.sales
            WHERE repair_shop_id IN (
                SELECT repair_shop_id FROM public.user_repair_shops
                WHERE user_id = auth.uid()
            )
        )
    );

CREATE POLICY "Users can delete sale items from their repair shop" ON public.sale_items
    FOR DELETE USING (
        sale_id IN (
            SELECT id FROM public.sales
            WHERE repair_shop_id IN (
                SELECT repair_shop_id FROM public.user_repair_shops
                WHERE user_id = auth.uid()
            )
        )
    );

-- Create RLS policies for stock_movements
CREATE POLICY "Users can view stock movements from their repair shop" ON public.stock_movements
    FOR SELECT USING (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
    );

CREATE POLICY "Users can insert stock movements for their repair shop" ON public.stock_movements
    FOR INSERT WITH CHECK (
        repair_shop_id IN (
            SELECT repair_shop_id FROM public.user_repair_shops
            WHERE user_id = auth.uid()
        )
        AND user_id = auth.uid()
    );

-- Enable real-time for stock management tables
ALTER PUBLICATION supabase_realtime ADD TABLE public.product_categories;
ALTER PUBLICATION supabase_realtime ADD TABLE public.products;
ALTER PUBLICATION supabase_realtime ADD TABLE public.sales;
ALTER PUBLICATION supabase_realtime ADD TABLE public.sale_items;
ALTER PUBLICATION supabase_realtime ADD TABLE public.stock_movements;
