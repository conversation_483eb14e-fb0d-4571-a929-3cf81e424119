-- User Action Logs Migration
-- This script adds the user_action_logs table for the premium plan features

-- Start transaction to ensure all operations succeed or fail together
BEGIN;

-- =============================================
-- USER ACTION LOGGING
-- =============================================

-- Create an action log table for traceability
CREATE TABLE IF NOT EXISTS public.user_action_logs (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action TEXT NOT NULL,
    details JSONB,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT now()
);

-- Create indexes for faster queries
CREATE INDEX IF NOT EXISTS idx_user_action_logs_user_id ON public.user_action_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_user_action_logs_created_at ON public.user_action_logs(created_at);

-- Enable Row Level Security
ALTER TABLE public.user_action_logs ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for user_action_logs
CREATE POLICY "Users can insert their own action logs" ON public.user_action_logs
    FOR INSERT WITH CHECK (user_id = auth.uid());

CREATE POLICY "Administrators can view action logs from their repair shop" ON public.user_action_logs
    FOR SELECT USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.user_id = auth.uid()
            AND user_repair_shops.role = 'administrator'
        )
    );

-- Add to real-time publication
ALTER PUBLICATION supabase_realtime ADD TABLE public.user_action_logs;

-- =============================================
-- COMMIT TRANSACTION
-- =============================================

COMMIT;