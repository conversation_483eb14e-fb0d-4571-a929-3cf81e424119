-- Create custom forms tables for premium features
BEGIN;

-- Create form_templates table
CREATE TABLE IF NOT EXISTS public.form_templates (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    description TEXT,
    device_type VARCHAR(100) NOT NULL,
    fields JSONB NOT NULL DEFAULT '[]',
    is_active BOOLEAN NOT NULL DEFAULT true,
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create form_responses table
CREATE TABLE IF NOT EXISTS public.form_responses (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    repair_id UUID NOT NULL REFERENCES public.repairs(id) ON DELETE CASCADE,
    template_id UUID NOT NULL REFERENCES public.form_templates(id) ON DELETE CASCADE,
    responses JSONB NOT NULL DEFAULT '{}',
    repair_shop_id UUID NOT NULL REFERENCES public.repair_shops(id) ON DELETE CASCADE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_form_templates_repair_shop_id ON public.form_templates(repair_shop_id);
CREATE INDEX IF NOT EXISTS idx_form_templates_device_type ON public.form_templates(device_type);
CREATE INDEX IF NOT EXISTS idx_form_templates_is_active ON public.form_templates(is_active);

CREATE INDEX IF NOT EXISTS idx_form_responses_repair_id ON public.form_responses(repair_id);
CREATE INDEX IF NOT EXISTS idx_form_responses_template_id ON public.form_responses(template_id);
CREATE INDEX IF NOT EXISTS idx_form_responses_repair_shop_id ON public.form_responses(repair_shop_id);

-- Enable RLS (Row Level Security)
ALTER TABLE public.form_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.form_responses ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for form_templates
CREATE POLICY "Users can view form templates from their repair shops" ON public.form_templates
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = form_templates.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage form templates from their repair shops" ON public.form_templates
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = form_templates.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
            AND user_repair_shops.role IN ('administrator', 'technician')
        )
    );

-- Create RLS policies for form_responses
CREATE POLICY "Users can view form responses from their repair shops" ON public.form_responses
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = form_responses.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

CREATE POLICY "Users can manage form responses from their repair shops" ON public.form_responses
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = form_responses.repair_shop_id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

-- Create trigger to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_form_templates_updated_at 
    BEFORE UPDATE ON public.form_templates 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_form_responses_updated_at 
    BEFORE UPDATE ON public.form_responses 
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

COMMIT;