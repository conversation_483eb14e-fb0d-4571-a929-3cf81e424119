-- Sample data for stock management system
-- This script adds sample categories and products for testing

-- Insert sample categories (using repair shop ID: 09432f5a-5db9-4b21-a69a-24dd7afe57f5)
INSERT INTO public.product_categories (name, description, icon, color, repair_shop_id) VALUES
('Mobile Phones', 'Smartphones and feature phones', 'smartphone', 'blue', '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Phone Accessories', 'Cases, chargers, cables, and other accessories', 'cable', 'green', '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Screen Protectors', 'Tempered glass and film protectors', 'shield', 'purple', '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Repair Tools', 'Tools and equipment for device repair', 'wrench', 'orange', '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Audio Accessories', 'Headphones, speakers, and audio equipment', 'headphones', 'red', '09432f5a-5db9-4b21-a69a-24dd7afe57f5');

-- Insert sample products (you'll need to replace category IDs with actual ones from the categories table)
-- First, get the category IDs:
-- SELECT id, name FROM public.product_categories WHERE repair_shop_id = '09432f5a-5db9-4b21-a69a-24dd7afe57f5';

-- Sample products for Mobile Phones category
INSERT INTO public.products (name, description, sku, barcode, category_id, price, cost, stock_quantity, min_stock_level, max_stock_level, is_active, repair_shop_id) VALUES
('iPhone 15 Pro 128GB', 'Latest iPhone model with advanced camera system', 'IP15P-128', '1234567890123', 'mobile-phones-category-id', 999.99, 800.00, 5, 2, 20, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('iPhone 15 256GB', 'iPhone 15 with 256GB storage', 'IP15-256', '1234567890124', 'mobile-phones-category-id', 899.99, 720.00, 8, 3, 25, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Samsung Galaxy S24', 'Latest Samsung flagship phone', 'SGS24-128', '1234567890125', 'mobile-phones-category-id', 799.99, 640.00, 6, 2, 15, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Google Pixel 8', 'Google Pixel with advanced AI features', 'GP8-128', '1234567890126', 'mobile-phones-category-id', 699.99, 560.00, 4, 2, 12, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5');

-- Sample products for Phone Accessories category
INSERT INTO public.products (name, description, sku, barcode, category_id, price, cost, stock_quantity, min_stock_level, max_stock_level, is_active, repair_shop_id) VALUES
('Clear Phone Case', 'Transparent protective case for various phone models', 'CASE-CLR', '2234567890123', 'accessories-category-id', 24.99, 10.00, 50, 10, 100, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Silicone Phone Case', 'Soft silicone case with multiple color options', 'CASE-SIL', '2234567890124', 'accessories-category-id', 19.99, 8.00, 75, 15, 150, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('USB-C Cable 1m', 'Fast charging USB-C cable', 'CABLE-USBC-1M', '2234567890125', 'accessories-category-id', 14.99, 5.00, 100, 20, 200, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Lightning Cable 1m', 'Apple Lightning charging cable', 'CABLE-LIGHT-1M', '2234567890126', 'accessories-category-id', 19.99, 7.00, 80, 15, 150, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Wireless Charger', '15W fast wireless charging pad', 'CHARGER-WIRELESS', '2234567890127', 'accessories-category-id', 39.99, 20.00, 25, 5, 50, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Car Phone Mount', 'Magnetic car phone holder', 'MOUNT-CAR', '2234567890128', 'accessories-category-id', 29.99, 12.00, 30, 8, 60, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5');

-- Sample products for Screen Protectors category
INSERT INTO public.products (name, description, sku, barcode, category_id, price, cost, stock_quantity, min_stock_level, max_stock_level, is_active, repair_shop_id) VALUES
('Tempered Glass Screen Protector', 'Premium tempered glass protection', 'SCREEN-GLASS', '3234567890123', 'screen-protectors-category-id', 12.99, 4.00, 60, 15, 120, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Privacy Screen Protector', 'Anti-spy privacy screen protection', 'SCREEN-PRIVACY', '3234567890124', 'screen-protectors-category-id', 19.99, 8.00, 40, 10, 80, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Anti-Blue Light Screen Protector', 'Blue light filtering screen protection', 'SCREEN-BLUE', '3234567890125', 'screen-protectors-category-id', 16.99, 6.00, 35, 8, 70, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5');

-- Sample products for Repair Tools category
INSERT INTO public.products (name, description, sku, barcode, category_id, price, cost, stock_quantity, min_stock_level, max_stock_level, is_active, repair_shop_id) VALUES
('Precision Screwdriver Set', 'Complete set of precision screwdrivers', 'TOOL-SCREW-SET', '4234567890123', 'repair-tools-category-id', 49.99, 25.00, 10, 3, 20, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Phone Opening Tools Kit', 'Plastic tools for safe phone opening', 'TOOL-OPENING', '4234567890124', 'repair-tools-category-id', 19.99, 8.00, 15, 5, 30, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Heat Gun', 'Professional heat gun for repairs', 'TOOL-HEAT-GUN', '4234567890125', 'repair-tools-category-id', 89.99, 45.00, 3, 1, 8, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Suction Cup Tool', 'Strong suction cup for screen removal', 'TOOL-SUCTION', '4234567890126', 'repair-tools-category-id', 9.99, 3.00, 20, 5, 40, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5');

-- Sample products for Audio Accessories category
INSERT INTO public.products (name, description, sku, barcode, category_id, price, cost, stock_quantity, min_stock_level, max_stock_level, is_active, repair_shop_id) VALUES
('Bluetooth Earbuds', 'True wireless Bluetooth earbuds', 'AUDIO-EARBUDS', '5234567890123', 'audio-category-id', 79.99, 40.00, 20, 5, 40, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Wired Earphones', 'High-quality wired earphones', 'AUDIO-WIRED', '5234567890124', 'audio-category-id', 24.99, 10.00, 45, 10, 90, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Portable Speaker', 'Compact Bluetooth speaker', 'AUDIO-SPEAKER', '5234567890125', 'audio-category-id', 59.99, 30.00, 12, 3, 25, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Phone Stand with Speaker', 'Phone stand with built-in amplifier', 'AUDIO-STAND', '5234567890126', 'audio-category-id', 34.99, 15.00, 18, 5, 35, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5');

-- Sample low stock products for testing alerts
INSERT INTO public.products (name, description, sku, barcode, category_id, price, cost, stock_quantity, min_stock_level, max_stock_level, is_active, repair_shop_id) VALUES
('iPhone 14 Screen Replacement', 'OEM quality iPhone 14 screen', 'SCREEN-IP14', '6234567890123', 'repair-tools-category-id', 149.99, 80.00, 1, 5, 20, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5'),
('Samsung S23 Battery', 'Replacement battery for Samsung S23', 'BATTERY-S23', '6234567890124', 'repair-tools-category-id', 39.99, 20.00, 2, 8, 30, true, '09432f5a-5db9-4b21-a69a-24dd7afe57f5');

-- Sample inactive product
INSERT INTO public.products (name, description, sku, barcode, category_id, price, cost, stock_quantity, min_stock_level, max_stock_level, is_active, repair_shop_id) VALUES
('Discontinued Phone Model', 'Old phone model no longer sold', 'PHONE-OLD', '7234567890123', 'mobile-phones-category-id', 299.99, 200.00, 0, 0, 0, false, '09432f5a-5db9-4b21-a69a-24dd7afe57f5');

-- Instructions for use:
-- 1. ✅ Repair shop ID has been set to: 09432f5a-5db9-4b21-a69a-24dd7afe57f5
-- 2. After inserting categories, get their actual IDs and replace the placeholder category IDs in the products
-- 3. Run this script in your Supabase SQL editor or through your database client

-- To get category IDs after insertion, run:
-- SELECT id, name FROM public.product_categories WHERE repair_shop_id = '09432f5a-5db9-4b21-a69a-24dd7afe57f5';

-- Replace these placeholder category IDs in the products above:
-- 'mobile-phones-category-id' → actual Mobile Phones category ID
-- 'accessories-category-id' → actual Phone Accessories category ID
-- 'screen-protectors-category-id' → actual Screen Protectors category ID
-- 'repair-tools-category-id' → actual Repair Tools category ID
-- 'audio-category-id' → actual Audio Accessories category ID
