-- Check current user roles and fix if needed
-- Replace '<EMAIL>' with your actual email address

-- First, let's see what users and roles exist
SELECT 
    u.email,
    rs.name as shop_name,
    urs.role,
    urs.created_at
FROM auth.users u
LEFT JOIN public.user_repair_shops urs ON u.id = urs.user_id
LEFT JOIN public.repair_shops rs ON urs.repair_shop_id = rs.id
ORDER BY u.email, rs.name;

-- Check if there are any repair shops without administrators
SELECT 
    rs.id,
    rs.name,
    COUNT(urs.id) as admin_count
FROM public.repair_shops rs
LEFT JOIN public.user_repair_shops urs ON rs.id = urs.repair_shop_id AND urs.role = 'administrator'
GROUP BY rs.id, rs.name
HAVING COUNT(urs.id) = 0;

-- To fix your specific user (uncomment and replace email):
/*
DO $$
DECLARE
    user_email TEXT := '<EMAIL>'; -- Replace with your email
    user_uuid UUID;
    shop_uuid UUID;
BEGIN
    -- Get user ID
    SELECT id INTO user_uuid FROM auth.users WHERE email = user_email;
    
    IF user_uuid IS NULL THEN
        RAISE NOTICE 'User with email % not found', user_email;
        RETURN;
    END IF;
    
    -- Get first repair shop (or create one if none exists)
    SELECT id INTO shop_uuid FROM public.repair_shops LIMIT 1;
    
    IF shop_uuid IS NULL THEN
        -- Create a default repair shop
        INSERT INTO public.repair_shops (name, address, phone, email)
        VALUES ('Default Repair Shop', '123 Main St', '555-0123', user_email)
        RETURNING id INTO shop_uuid;
        
        RAISE NOTICE 'Created default repair shop with ID: %', shop_uuid;
    END IF;
    
    -- Check if user already has a role in this shop
    IF NOT EXISTS (
        SELECT 1 FROM public.user_repair_shops 
        WHERE user_id = user_uuid AND repair_shop_id = shop_uuid
    ) THEN
        -- Add administrator role
        INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role)
        VALUES (user_uuid, shop_uuid, 'administrator');
        
        RAISE NOTICE 'Added administrator role for user % in shop %', user_email, shop_uuid;
    ELSE
        -- Update existing role to administrator
        UPDATE public.user_repair_shops 
        SET role = 'administrator'
        WHERE user_id = user_uuid AND repair_shop_id = shop_uuid;
        
        RAISE NOTICE 'Updated role to administrator for user % in shop %', user_email, shop_uuid;
    END IF;
END $$;
*/