-- Add fullname column to user_repair_shops table
ALTER TABLE public.user_repair_shops 
ADD COLUMN IF NOT EXISTS full_name TEXT;

-- Update the get_shop_users function to include full_name with correct types
CREATE OR REPLACE FUNCTION get_shop_users(shop_id UUID)
RETURNS TABLE (
  id UUID,
  user_id UUID,
  role VARCHAR(255),
  repair_shop_id UUID,
  email VARCHAR(255),
  full_name TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    urs.id,
    urs.user_id,
    urs.role::VA<PERSON>HA<PERSON>(255),
    urs.repair_shop_id,
    u.email::VA<PERSON>HAR(255),
    urs.full_name
  FROM public.user_repair_shops urs
  JOIN auth.users u ON urs.user_id = u.id
  WHERE urs.repair_shop_id = shop_id
  ORDER BY u.email;
END;
$$;

-- Update the create_user_with_role function to include full_name
CREATE OR REPLACE FUNCTION create_user_with_role(
  user_email TEXT,
  user_password TEXT,
  user_full_name TEXT,
  user_role TEXT,
  shop_id UUID
)
RETURNS JSON
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  new_user_id UUID;
  result JSON;
BEGIN
  -- Create the user in auth.users
  INSERT INTO auth.users (
    instance_id,
    id,
    aud,
    role,
    email,
    encrypted_password,
    email_confirmed_at,
    created_at,
    updated_at,
    confirmation_token,
    email_change,
    email_change_token_new,
    recovery_token
  ) VALUES (
    '00000000-0000-0000-0000-000000000000',
    gen_random_uuid(),
    'authenticated',
    'authenticated',
    user_email,
    crypt(user_password, gen_salt('bf')),
    NOW(),
    NOW(),
    NOW(),
    '',
    '',
    '',
    ''
  )
  RETURNING id INTO new_user_id;
  
  -- Add user to repair shop with role and full_name
  INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role, full_name)
  VALUES (new_user_id, shop_id, user_role, user_full_name);
  
  result := json_build_object(
    'success', true,
    'user_id', new_user_id,
    'message', 'User created successfully'
  );
  
  RETURN result;
  
EXCEPTION WHEN OTHERS THEN
  result := json_build_object(
    'success', false,
    'error', SQLERRM
  );
  RETURN result;
END;
$$;

-- Grant necessary permissions
GRANT EXECUTE ON FUNCTION get_shop_users(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION create_user_with_role(TEXT, TEXT, TEXT, TEXT, UUID) TO authenticated;