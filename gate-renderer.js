// gate-renderer.js (run in renderer context)
window.addEventListener("DOMContentLoaded", () => {
  const gate = document.getElementById("license-gate");

  console.log("Form loaded:", !!gate);
  console.log("window.electron available:", !!window.electron);

  gate.addEventListener("submit", (event) => {
    event.preventDefault();
    console.log("Submitting form...");

    const key = gate.querySelector('input[name="key"]').value;

    if (key) {
      window.electron.submitGate({ key });
    } else {
      alert("Please enter a license key.");
    }
  });

  window.electron.onShowError((message) => {
    alert(message);
  });
});
