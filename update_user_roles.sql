-- Update the user_repair_shops role constraint to match RBAC implementation
BEGIN;

-- Drop the existing constraint
ALTER TABLE public.user_repair_shops DROP CONSTRAINT IF EXISTS user_repair_shops_role_check;

-- Add the new constraint with the correct roles
ALTER TABLE public.user_repair_shops 
    ADD CONSTRAINT user_repair_shops_role_check 
    CHECK (role IN ('administrator', 'technician', 'receptionist', 'cashier'));

-- Update any existing 'owner' roles to 'administrator'
UPDATE public.user_repair_shops SET role = 'administrator' WHERE role = 'owner';

-- Update any existing 'admin' roles to 'administrator'
UPDATE public.user_repair_shops SET role = 'administrator' WHERE role = 'admin';

COMMIT;