-- Add customer_email field to repairs table
ALTER TABLE public.repairs ADD COLUMN IF NOT EXISTS customer_email TEXT;

-- Add a comment to describe the field
COMMENT ON COLUMN public.repairs.customer_email IS 'Optional email address for the customer to receive repair status notifications';

-- Update the supabase_realtime publication to include the new field
ALTER PUBLICATION supabase_realtime REFRESH TABLE repairs;

-- Create an index for better performance when querying by email
CREATE INDEX IF NOT EXISTS idx_repairs_customer_email ON public.repairs(customer_email) WHERE customer_email IS NOT NULL;
