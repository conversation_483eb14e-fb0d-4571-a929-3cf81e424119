-- Update time tracking to use repair status history timeline
BEGIN;

-- Drop the old time_tracking table and triggers
DROP TABLE IF EXISTS public.time_tracking CASCADE;
DROP TRIGGER IF EXISTS repair_status_change_trigger ON public.repairs;
DROP TRIGGER IF EXISTS technician_assignment_trigger ON public.repairs;
DROP FUNCTION IF EXISTS handle_repair_status_change();
DROP FUNCTION IF EXISTS handle_technician_assignment();

-- Add columns to track inProgress timing
ALTER TABLE public.repairs 
ADD COLUMN IF NOT EXISTS in_progress_at TIMESTAMP WITH TIME ZONE,
ADD COLUMN IF NOT EXISTS completed_at TIMESTAMP WITH TIME ZONE;

-- Create trigger to track status timing
CREATE OR REPLACE FUNCTION track_repair_timing()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
  -- Track when repair moves to inProgress
  IF OLD.status != 'inProgress' AND NEW.status = 'inProgress' THEN
    NEW.in_progress_at = NOW();
  END IF;
  
  -- Track when repair is completed
  IF OLD.status != 'completed' AND NEW.status = 'completed' THEN
    NEW.completed_at = NOW();
  END IF;
  
  RETURN NEW;
END;
$$;

-- Create the trigger
DROP TRIGGER IF EXISTS repair_timing_trigger ON public.repairs;
CREATE TRIGGER repair_timing_trigger
  BEFORE UPDATE ON public.repairs
  FOR EACH ROW
  EXECUTE FUNCTION track_repair_timing();

-- Update existing repairs to set in_progress_at for current inProgress repairs
UPDATE public.repairs 
SET in_progress_at = created_at 
WHERE status = 'inProgress' AND in_progress_at IS NULL;

-- Update existing completed repairs
UPDATE public.repairs 
SET in_progress_at = created_at, completed_at = updated_at 
WHERE status = 'completed' AND in_progress_at IS NULL;

-- Create function to get technician time tracking
CREATE OR REPLACE FUNCTION get_technician_time_tracking(shop_id UUID)
RETURNS TABLE (
  repair_id UUID,
  technician_id UUID,
  repair_shop_id UUID,
  customer_name TEXT,
  phone_model TEXT,
  ticket_number INTEGER,
  status_from TEXT,
  status_to TEXT,
  started_at TIMESTAMP WITH TIME ZONE,
  ended_at TIMESTAMP WITH TIME ZONE,
  duration_minutes NUMERIC
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  RETURN QUERY
  SELECT 
    r.id,
    r.assigned_technician,
    r.repair_shop_id,
    r.customer_name,
    r.phone_model,
    r.ticket_number,
    'inProgress'::TEXT,
    r.status,
    COALESCE(r.in_progress_at, r.created_at) as start_time,
    CASE 
      WHEN r.status = 'completed' THEN COALESCE(r.completed_at, r.updated_at)
      ELSE NULL
    END as end_time,
    CASE 
      WHEN r.status = 'completed' THEN
        EXTRACT(EPOCH FROM (COALESCE(r.completed_at, r.updated_at) - COALESCE(r.in_progress_at, r.created_at))) / 60
      WHEN r.status = 'inProgress' THEN
        EXTRACT(EPOCH FROM (NOW() - COALESCE(r.in_progress_at, r.created_at))) / 60
      ELSE NULL
    END
  FROM public.repairs r
  WHERE r.repair_shop_id = shop_id
  AND r.assigned_technician IS NOT NULL
  AND r.status IN ('inProgress', 'completed')
  ORDER BY COALESCE(r.in_progress_at, r.created_at) DESC;
END;
$$;

-- Grant execute permission on the function
GRANT EXECUTE ON FUNCTION get_technician_time_tracking(UUID) TO authenticated;

COMMIT;