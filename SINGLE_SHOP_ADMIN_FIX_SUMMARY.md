# Single Shop Administrator Access Fix - Quick Summary

## 🔍 **The Problem**
Administrators in your single repair shop cannot see all repairs - they can only see repairs they personally created or are assigned to as technicians.

## 🎯 **Root Cause**
The database Row Level Security (RLS) policies are too restrictive. The current policy only allows:
```sql
user_id = auth.uid() OR assigned_technician = auth.uid()
```

But administrators should see **ALL repairs** in the shop.

## ⚡ **Quick Fix (Single Shop)**

### Step 1: Apply the Database Migration
1. Go to your **Supabase project dashboard**
2. Navigate to **SQL Editor**
3. Copy and paste this SQL:

```sql
-- SINGLE SHOP ADMIN FIX
BEGIN;

-- Drop restrictive policies
DROP POLICY IF EXISTS "Users can view their own repairs" ON public.repairs;
DROP POLICY IF EXISTS "Users can insert repairs" ON public.repairs;
DROP POLICY IF EXISTS "Users can update their own repairs" ON public.repairs;
DROP POLICY IF EXISTS "Users can delete their own repairs" ON public.repairs;

-- Create role-based policy for single shop
CREATE POLICY "Role-based repair access" ON public.repairs
    FOR SELECT
    USING (
        -- Administrators can see ALL repairs
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.user_id = auth.uid()
            AND user_repair_shops.role = 'administrator'
        )
        OR
        -- Others see only their own/assigned repairs
        (user_id = auth.uid() OR assigned_technician = auth.uid())
    );

-- Other policies...
CREATE POLICY "Users can insert repairs" ON public.repairs
    FOR INSERT WITH CHECK (auth.uid() IS NOT NULL);

CREATE POLICY "Role-based repair updates" ON public.repairs
    FOR UPDATE
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.user_id = auth.uid()
            AND user_repair_shops.role = 'administrator'
        )
        OR (user_id = auth.uid() OR assigned_technician = auth.uid())
    );

CREATE POLICY "Role-based repair deletes" ON public.repairs
    FOR DELETE
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.user_id = auth.uid()
            AND user_repair_shops.role = 'administrator'
        )
        OR user_id = auth.uid()
    );

COMMIT;
```

4. **Run the query**

### Step 2: Check/Fix User Roles
If the new admin account still shows 0 repairs, run this to assign admin role:

```sql
-- Check current user roles
SELECT u.email, urs.role 
FROM auth.users u 
LEFT JOIN public.user_repair_shops urs ON u.id = urs.user_id;

-- Assign admin role to specific user
SELECT assign_admin_role_single_shop('<EMAIL>');

-- OR make ALL users administrators (emergency fix)
SELECT make_all_users_admin();
```

## ✅ **Expected Results**
- **Administrators**: See ALL repairs in the shop
- **Technicians**: See assigned repairs (toggle OFF) or all repairs (toggle ON)
- **Other roles**: See repairs they created or are assigned to

## 🧪 **Test the Fix**
1. Login as administrator
2. Go to `/admin-test` in your app
3. Run the diagnostic tests
4. Should see "Can access X repairs" where X = total repairs in shop

## 🚨 **Emergency Fix**
If you want to make ALL existing users administrators immediately:

```sql
SELECT make_all_users_admin();
```

This will assign administrator role to every user in your system.

## 📁 **Files Created**
- `migrations/fix_administrator_repair_access_single_shop.sql` - Main fix
- `migrations/verify_and_fix_admin_roles_single_shop.sql` - Role management
- `src/components/AdminAccessTest.tsx` - Test component
- `src/pages/AdminTest.tsx` - Test page (accessible at `/admin-test`)

## 🔧 **Why This Works for Single Shop**
Since you only have one repair shop, we can simplify the policies significantly:
- No need to check `repair_shop_id` 
- Direct role-based access control
- Simpler logic = fewer potential issues

The application-level filtering in `RepairContext.tsx` will continue to work correctly for technicians using the "show all tickets" toggle.

## 🆘 **Still Having Issues?**
1. Check browser console for errors
2. Verify user is logged in
3. Run the diagnostic queries to check role assignments
4. Use the `/admin-test` page to get detailed diagnostics

This fix specifically addresses the single repair shop scenario and should resolve the administrator access issue immediately.
