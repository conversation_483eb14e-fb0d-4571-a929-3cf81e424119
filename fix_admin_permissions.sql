-- Fix administrator permissions and role assignments
BEGIN;

-- First, let's check and fix the user_repair_shops table structure
-- Ensure the role column exists and has the correct constraint
ALTER TABLE public.user_repair_shops 
    DROP CONSTRAINT IF EXISTS user_repair_shops_role_check;

ALTER TABLE public.user_repair_shops 
    ADD CONSTRAINT user_repair_shops_role_check 
    CHECK (role IN ('administrator', 'technician', 'receptionist', 'cashier'));

-- Update any existing 'owner' or 'admin' roles to 'administrator'
UPDATE public.user_repair_shops 
SET role = 'administrator' 
WHERE role IN ('owner', 'admin');

-- Ensure all authenticated users can see repair shops they belong to
DROP POLICY IF EXISTS "Users can view repair shops they belong to" ON public.repair_shops;
CREATE POLICY "Users can view repair shops they belong to" ON public.repair_shops
    FOR SELECT
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops
            WHERE user_repair_shops.repair_shop_id = repair_shops.id
            AND user_repair_shops.user_id = auth.uid()
        )
    );

-- Ensure administrators can manage user-repair shop associations
DROP POLICY IF EXISTS "Admins can manage user-repair shop associations" ON public.user_repair_shops;
CREATE POLICY "Admins can manage user-repair shop associations" ON public.user_repair_shops
    FOR ALL
    USING (
        EXISTS (
            SELECT 1 FROM public.user_repair_shops AS urs
            WHERE urs.user_id = auth.uid()
            AND urs.repair_shop_id = user_repair_shops.repair_shop_id
            AND urs.role = 'administrator'
        )
    );

-- Ensure users can view their own associations
DROP POLICY IF EXISTS "Users can view their own user-repair shop associations" ON public.user_repair_shops;
CREATE POLICY "Users can view their own user-repair shop associations" ON public.user_repair_shops
    FOR SELECT
    USING (user_id = auth.uid());

-- Create or update stock-related tables and policies if they exist
DO $$
BEGIN
    -- Check if products table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'products') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "Users can view products from their repair shops" ON public.products;
        DROP POLICY IF EXISTS "Users can manage products from their repair shops" ON public.products;
        
        -- Create new policies
        CREATE POLICY "Users can view products from their repair shops" ON public.products
            FOR SELECT
            USING (
                EXISTS (
                    SELECT 1 FROM public.user_repair_shops
                    WHERE user_repair_shops.repair_shop_id = products.repair_shop_id
                    AND user_repair_shops.user_id = auth.uid()
                )
            );
            
        CREATE POLICY "Users can manage products from their repair shops" ON public.products
            FOR ALL
            USING (
                EXISTS (
                    SELECT 1 FROM public.user_repair_shops
                    WHERE user_repair_shops.repair_shop_id = products.repair_shop_id
                    AND user_repair_shops.user_id = auth.uid()
                    AND user_repair_shops.role IN ('administrator', 'technician')
                )
            );
    END IF;
    
    -- Check if technician_time_entries table exists
    IF EXISTS (SELECT FROM information_schema.tables WHERE table_name = 'technician_time_entries') THEN
        -- Drop existing policies
        DROP POLICY IF EXISTS "Users can view time entries from their repair shops" ON public.technician_time_entries;
        DROP POLICY IF EXISTS "Users can manage their own time entries" ON public.technician_time_entries;
        
        -- Create new policies
        CREATE POLICY "Users can view time entries from their repair shops" ON public.technician_time_entries
            FOR SELECT
            USING (
                EXISTS (
                    SELECT 1 FROM public.user_repair_shops
                    WHERE user_repair_shops.repair_shop_id = technician_time_entries.repair_shop_id
                    AND user_repair_shops.user_id = auth.uid()
                )
            );
            
        CREATE POLICY "Users can manage their own time entries" ON public.technician_time_entries
            FOR ALL
            USING (
                user_id = auth.uid() OR
                EXISTS (
                    SELECT 1 FROM public.user_repair_shops
                    WHERE user_repair_shops.repair_shop_id = technician_time_entries.repair_shop_id
                    AND user_repair_shops.user_id = auth.uid()
                    AND user_repair_shops.role = 'administrator'
                )
            );
    END IF;
END $$;

-- If you know your user email, uncomment and modify this line to ensure you have administrator access:
-- INSERT INTO public.user_repair_shops (user_id, repair_shop_id, role)
-- SELECT auth.users.id, repair_shops.id, 'administrator'
-- FROM auth.users, public.repair_shops
-- WHERE auth.users.email = '<EMAIL>'
-- AND NOT EXISTS (
--     SELECT 1 FROM public.user_repair_shops 
--     WHERE user_id = auth.users.id 
--     AND repair_shop_id = repair_shops.id
-- );

COMMIT;